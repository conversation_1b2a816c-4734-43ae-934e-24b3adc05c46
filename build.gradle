plugins {
    id 'java'
    id 'org.springframework.boot' version '3.2.3'
    id 'io.spring.dependency-management' version '1.1.4'
}

group = 'com.example'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '17'

repositories {
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter'
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.10.0'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.10.0'
    testImplementation 'org.junit.platform:junit-platform-launcher:1.10.0'
    testImplementation 'org.junit.platform:junit-platform-commons:1.10.0'
    testImplementation 'org.junit.platform:junit-platform-suite:1.10.0'
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'com.fasterxml.jackson.core:jackson-core'
    implementation 'com.fasterxml.jackson.core:jackson-annotations'
    testCompileOnly 'org.openjdk.jmh:jmh-core:1.19'
    testCompileOnly 'org.openjdk.jmh:jmh-generator-annprocess:1.19'
    implementation 'it.unimi.dsi:fastutil:8.5.9'
    implementation 'javax.annotation:javax.annotation-api:1.3.2'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'
}

test {
    useJUnitPlatform()
} 