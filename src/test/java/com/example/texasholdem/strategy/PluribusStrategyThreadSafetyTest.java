package com.example.texasholdem.strategy;

import com.example.texasholdem.model.*;
import com.example.texasholdem.strategy.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Thread safety tests for PluribusStrategy with ThreadSafeInfosetStore integration
 * 
 * Validates that PluribusStrategy correctly handles concurrent access when using
 * ThreadSafeInfosetStore for enhanced parallel training integration.
 */
public class PluribusStrategyThreadSafetyTest {

    @Mock
    private Player mockPlayer;

    private ThreadSafeInfosetStore threadSafeStore;
    private InfosetStore regularStore;
    private PluribusStrategy threadSafeStrategy;
    private PluribusStrategy regularStrategy;

    private static final int NUM_PLAYERS = 6;
    private static final int NUM_ACTIONS = 6;
    private static final int NUM_THREADS = 8;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // Create stores
        threadSafeStore = new ThreadSafeInfosetStore(NUM_PLAYERS, NUM_ACTIONS);
        regularStore = new InfosetStore(NUM_PLAYERS, NUM_ACTIONS);
        
        // Create strategies
        threadSafeStrategy = new PluribusStrategy(threadSafeStore);
        regularStrategy = new PluribusStrategy(regularStore);
        
        // Setup mock player
        when(mockPlayer.getPlayerIndex()).thenReturn(0);
        when(mockPlayer.getName()).thenReturn("TestPlayer");
        when(mockPlayer.getChips()).thenReturn(1000);
        when(mockPlayer.getHand()).thenReturn(createTestHoleCards());
    }

    @Test
    void testConstructorWithThreadSafeStore() {
        // Test that ThreadSafeInfosetStore constructor works correctly
        assertNotNull(threadSafeStrategy);
        assertTrue(threadSafeStrategy.isUsingThreadSafeStore());
        assertNotNull(threadSafeStrategy.getCurrentThreadSafeBlueprintStore());
        assertNull(threadSafeStrategy.getCurrentBlueprintStore());
        assertEquals("initial", threadSafeStrategy.getCurrentStrategyVersion());
    }

    @Test
    void testConstructorWithRegularStore() {
        // Test that InfosetStore constructor works correctly
        assertNotNull(regularStrategy);
        assertFalse(regularStrategy.isUsingThreadSafeStore());
        assertNotNull(regularStrategy.getCurrentBlueprintStore());
        assertNull(regularStrategy.getCurrentThreadSafeBlueprintStore());
        assertEquals("initial", regularStrategy.getCurrentStrategyVersion());
    }

    @Test
    void testHotSwapWithCorrectStoreType() {
        // Test hot-swapping with correct store types
        ThreadSafeInfosetStore newThreadSafeStore = new ThreadSafeInfosetStore(NUM_PLAYERS, NUM_ACTIONS);
        InfosetStore newRegularStore = new InfosetStore(NUM_PLAYERS, NUM_ACTIONS);
        
        // ThreadSafe strategy should accept ThreadSafeInfosetStore
        assertTrue(threadSafeStrategy.swapBlueprintStrategy(newThreadSafeStore, "v2.0"));
        assertEquals("v2.0", threadSafeStrategy.getCurrentStrategyVersion());
        
        // Regular strategy should accept InfosetStore
        assertTrue(regularStrategy.swapBlueprintStrategy(newRegularStore, "v2.0"));
        assertEquals("v2.0", regularStrategy.getCurrentStrategyVersion());
    }

    @Test
    void testHotSwapWithIncorrectStoreType() {
        // Test hot-swapping with incorrect store types should fail
        ThreadSafeInfosetStore newThreadSafeStore = new ThreadSafeInfosetStore(NUM_PLAYERS, NUM_ACTIONS);
        InfosetStore newRegularStore = new InfosetStore(NUM_PLAYERS, NUM_ACTIONS);
        
        // ThreadSafe strategy should reject InfosetStore
        assertFalse(threadSafeStrategy.swapBlueprintStrategy(newRegularStore, "v2.0"));
        assertEquals("initial", threadSafeStrategy.getCurrentStrategyVersion());
        
        // Regular strategy should reject ThreadSafeInfosetStore
        assertFalse(regularStrategy.swapBlueprintStrategy(newThreadSafeStore, "v2.0"));
        assertEquals("initial", regularStrategy.getCurrentStrategyVersion());
    }

    @Test
    @Timeout(value = 30, unit = TimeUnit.SECONDS)
    void testConcurrentActionDecisions() throws InterruptedException {
        // Test concurrent action decisions with ThreadSafeInfosetStore
        ExecutorService executor = Executors.newFixedThreadPool(NUM_THREADS);
        CountDownLatch latch = new CountDownLatch(NUM_THREADS);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);

        // Populate store with test data
        populateStoreWithTestData(threadSafeStore);

        for (int i = 0; i < NUM_THREADS; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < 50; j++) {
                        ActionInstance action = threadSafeStrategy.decideAction(
                            createTestHoleCards(),
                            createTestCommunityCards(),
                            BettingRound.FLOP,
                            mockPlayer,
                            100,
                            new ArrayList<>(),
                            500
                        );
                        
                        if (action != null) {
                            successCount.incrementAndGet();
                        }
                    }
                } catch (Exception e) {
                    errorCount.incrementAndGet();
                } finally {
                    latch.countDown();
                }
            });
        }

        assertTrue(latch.await(25, TimeUnit.SECONDS));
        
        // Verify results
        assertTrue(successCount.get() > 0, "Should have successful action decisions");
        assertEquals(0, errorCount.get(), "Should have no errors in concurrent access");
        
        executor.shutdown();
    }

    @Test
    @Timeout(value = 20, unit = TimeUnit.SECONDS)
    void testConcurrentStoreAccess() throws InterruptedException {
        // Test concurrent access to ThreadSafeInfosetStore through strategy
        ExecutorService executor = Executors.newFixedThreadPool(NUM_THREADS);
        CountDownLatch latch = new CountDownLatch(NUM_THREADS);
        AtomicInteger accessCount = new AtomicInteger(0);

        for (int i = 0; i < NUM_THREADS; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < 100; j++) {
                        // Access store through strategy methods
                        ThreadSafeInfosetStore store = threadSafeStrategy.getCurrentThreadSafeBlueprintStore();
                        assertNotNull(store);
                        
                        // Create test infoset
                        ThreadSafeInfosetValue infoset = store.getOrCreate(threadId % NUM_PLAYERS, j);
                        assertNotNull(infoset);
                        
                        accessCount.incrementAndGet();
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        assertTrue(latch.await(15, TimeUnit.SECONDS));
        assertEquals(NUM_THREADS * 100, accessCount.get());
        
        executor.shutdown();
    }

    @Test
    void testStrategyVersionManagement() {
        // Test strategy version management with both store types
        assertEquals("initial", threadSafeStrategy.getCurrentStrategyVersion());
        assertEquals("initial", regularStrategy.getCurrentStrategyVersion());
        
        // Test version updates
        ThreadSafeInfosetStore newThreadSafeStore = new ThreadSafeInfosetStore(NUM_PLAYERS, NUM_ACTIONS);
        assertTrue(threadSafeStrategy.swapBlueprintStrategy(newThreadSafeStore, "enhanced-v1.0"));
        assertEquals("enhanced-v1.0", threadSafeStrategy.getCurrentStrategyVersion());
        
        InfosetStore newRegularStore = new InfosetStore(NUM_PLAYERS, NUM_ACTIONS);
        assertTrue(regularStrategy.swapBlueprintStrategy(newRegularStore, "regular-v1.0"));
        assertEquals("regular-v1.0", regularStrategy.getCurrentStrategyVersion());
    }

    // Helper methods
    private List<Card> createTestHoleCards() {
        List<Card> cards = new ArrayList<>();
        cards.add(new Card(Suit.HEARTS, Rank.ACE));
        cards.add(new Card(Suit.SPADES, Rank.KING));
        return cards;
    }

    private List<Card> createTestCommunityCards() {
        List<Card> cards = new ArrayList<>();
        cards.add(new Card(Suit.HEARTS, Rank.QUEEN));
        cards.add(new Card(Suit.DIAMONDS, Rank.JACK));
        cards.add(new Card(Suit.CLUBS, Rank.TEN));
        return cards;
    }

    private void populateStoreWithTestData(ThreadSafeInfosetStore store) {
        // Populate store with test data for realistic testing
        for (int player = 0; player < NUM_PLAYERS; player++) {
            for (int key = 0; key < 100; key++) {
                ThreadSafeInfosetValue infoset = store.getOrCreate(player, key);
                
                // Set realistic strategy values
                float[] strategy = new float[NUM_ACTIONS];
                float sum = 0.0f;
                for (int i = 0; i < NUM_ACTIONS; i++) {
                    strategy[i] = (float) Math.random();
                    sum += strategy[i];
                }
                
                // Normalize strategy
                for (int i = 0; i < NUM_ACTIONS; i++) {
                    strategy[i] /= sum;
                }
                
                infoset.updateActionCounter(strategy);
            }
        }
        
        // Enable caching for performance
        store.setCachingEnabled(true);
    }
}
