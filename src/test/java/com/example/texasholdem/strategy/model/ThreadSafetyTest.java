package com.example.texasholdem.strategy.model;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive thread safety tests for Pluribus MCCFR training components
 * <p>
 * Tests thread safety of ThreadSafeInfosetValue, ThreadSafeInfosetStore, and multi-threaded
 * training scenarios to ensure data consistency and performance.
 */
@Execution(ExecutionMode.CONCURRENT)
public class ThreadSafetyTest {

  private static final int NUM_THREADS = 8;
  private static final int NUM_OPERATIONS = 10000;
  private static final int NUM_ACTIONS = 6;
  private static final int NUM_PLAYERS = 6;
  private static final long TEST_TIMEOUT_SECONDS = 30;

  private ThreadSafeInfosetValue threadSafeInfosetValue;
  private ThreadSafeInfosetStore threadSafeInfosetStore;
  private ExecutorService executorService;

  @BeforeEach
  void setUp() {
    threadSafeInfosetValue = new ThreadSafeInfosetValue(NUM_ACTIONS);
    threadSafeInfosetStore = new ThreadSafeInfosetStore(NUM_PLAYERS, NUM_ACTIONS);
    executorService = Executors.newFixedThreadPool(NUM_THREADS);
  }

  // ===== THREAD-SAFE INFOSET VALUE TESTS =====

  @Test
  @Timeout(TEST_TIMEOUT_SECONDS)
  void testConcurrentRegretUpdates() throws InterruptedException {
    CountDownLatch startLatch = new CountDownLatch(1);
    CountDownLatch completionLatch = new CountDownLatch(NUM_THREADS);
    AtomicInteger successCount = new AtomicInteger(0);

    // Launch concurrent regret update threads
    for (int t = 0; t < NUM_THREADS; t++) {
      final int threadId = t;
      executorService.submit(() -> {
        try {
          startLatch.await(); // Synchronized start

          for (int i = 0; i < NUM_OPERATIONS; i++) {
            int actionIndex = i % NUM_ACTIONS;
            float delta = (float) (threadId * 0.1 + i * 0.001);

            threadSafeInfosetValue.addRegret(actionIndex, delta);
          }

          successCount.incrementAndGet();
        } catch (Exception e) {
          e.printStackTrace();
        } finally {
          completionLatch.countDown();
        }
      });
    }

    startLatch.countDown(); // Start all threads
    assertTrue(completionLatch.await(TEST_TIMEOUT_SECONDS, TimeUnit.SECONDS));
    assertEquals(NUM_THREADS, successCount.get());

    // Verify regret values are consistent
    float[] regrets = threadSafeInfosetValue.getRegret();
    assertNotNull(regrets);
    assertEquals(NUM_ACTIONS, regrets.length);

    // All regret values should be positive (we only added positive deltas)
    for (float regret : regrets) {
      assertTrue(regret >= 0, "Regret should be non-negative: " + regret);
    }

    System.out.println(
        "Concurrent regret updates test passed. Final regrets: " + Arrays.toString(regrets));
  }

  @Test
  @Timeout(TEST_TIMEOUT_SECONDS)
  void testConcurrentStrategyCalculation() throws InterruptedException {
    // Pre-populate with some regret values
    for (int i = 0; i < NUM_ACTIONS; i++) {
      threadSafeInfosetValue.addRegret(i, (float) (i + 1) * 10.0f);
    }

    CountDownLatch startLatch = new CountDownLatch(1);
    CountDownLatch completionLatch = new CountDownLatch(NUM_THREADS);
    List<float[]> strategies = Collections.synchronizedList(new ArrayList<>());
    AtomicInteger successCount = new AtomicInteger(0);

    // Launch concurrent strategy calculation threads
    for (int t = 0; t < NUM_THREADS; t++) {
      executorService.submit(() -> {
        try {
          startLatch.await();

          for (int i = 0; i < NUM_OPERATIONS / 10;
              i++) { // Fewer operations for strategy calculation
            float[] strategy = threadSafeInfosetValue.calculateStrategy();
            strategies.add(strategy.clone());

            // Verify strategy is valid probability distribution
            float sum = 0.0f;
            for (float prob : strategy) {
              assertTrue(prob >= 0, "Strategy probability should be non-negative");
              sum += prob;
            }
            assertTrue(Math.abs(sum - 1.0f) < 1e-6f, "Strategy should sum to 1.0, got: " + sum);
          }

          successCount.incrementAndGet();
        } catch (Exception e) {
          e.printStackTrace();
        } finally {
          completionLatch.countDown();
        }
      });
    }

    startLatch.countDown();
    assertTrue(completionLatch.await(TEST_TIMEOUT_SECONDS, TimeUnit.SECONDS));
    assertEquals(NUM_THREADS, successCount.get());

    // Verify all strategies are consistent (should be the same since regrets don't change)
    assertFalse(strategies.isEmpty());
    float[] firstStrategy = strategies.get(0);

    for (float[] strategy : strategies) {
      assertArrayEquals(firstStrategy, strategy, 1e-6f, "All strategies should be identical");
    }

    System.out.println(
        "Concurrent strategy calculation test passed. Strategy: " + Arrays.toString(firstStrategy));
  }

  @Test
  @Timeout(TEST_TIMEOUT_SECONDS)
  void testConcurrentActionCounterUpdates() throws InterruptedException {
    CountDownLatch startLatch = new CountDownLatch(1);
    CountDownLatch completionLatch = new CountDownLatch(NUM_THREADS);
    AtomicInteger successCount = new AtomicInteger(0);

    // Launch concurrent action counter update threads
    for (int t = 0; t < NUM_THREADS; t++) {
      final int threadId = t;
      executorService.submit(() -> {
        try {
          startLatch.await();

          for (int i = 0; i < NUM_OPERATIONS / 100;
              i++) { // Fewer operations for action counter updates
            float[] strategy = new float[NUM_ACTIONS];
            Arrays.fill(strategy, 1.0f / NUM_ACTIONS); // Uniform strategy

            threadSafeInfosetValue.updateActionCounter(strategy);
          }

          successCount.incrementAndGet();
        } catch (Exception e) {
          e.printStackTrace();
        } finally {
          completionLatch.countDown();
        }
      });
    }

    startLatch.countDown();
    assertTrue(completionLatch.await(TEST_TIMEOUT_SECONDS, TimeUnit.SECONDS));
    assertEquals(NUM_THREADS, successCount.get());

    // Verify action counters are consistent
    float[] actionCounters = threadSafeInfosetValue.getActionCounter();
    assertNotNull(actionCounters);
    assertEquals(NUM_ACTIONS, actionCounters.length);

    // All action counters should be equal (uniform strategy)
    float expectedValue = actionCounters[0];
    for (float counter : actionCounters) {
      assertEquals(expectedValue, counter, 1e-6f,
          "All action counters should be equal for uniform strategy");
    }

    // Visit count should match total updates
    int expectedVisitCount = NUM_THREADS * (NUM_OPERATIONS / 100);
    assertEquals(expectedVisitCount, threadSafeInfosetValue.getVisitCount());

    // Verify the calculation: 8 threads * (10000/100) = 8 * 100 = 800
    assertEquals(800, expectedVisitCount, "Expected visit count calculation verification");

    System.out.println("Concurrent action counter updates test passed. Visit count: " +
        threadSafeInfosetValue.getVisitCount());
  }

  // ===== THREAD-SAFE INFOSET STORE TESTS =====

  @Test
  @Timeout(TEST_TIMEOUT_SECONDS)
  void testConcurrentInfosetCreation() throws InterruptedException {
    CountDownLatch startLatch = new CountDownLatch(1);
    CountDownLatch completionLatch = new CountDownLatch(NUM_THREADS);
    AtomicInteger successCount = new AtomicInteger(0);
    Set<Integer> createdKeys = ConcurrentHashMap.newKeySet();

    // Launch concurrent infoset creation threads
    for (int t = 0; t < NUM_THREADS; t++) {
      final int threadId = t;
      executorService.submit(() -> {
        try {
          startLatch.await();

          for (int i = 0; i < NUM_OPERATIONS / 10; i++) {
            int playerIndex = i % NUM_PLAYERS;
            int abstractionKey = threadId * NUM_OPERATIONS + i;

            ThreadSafeInfosetValue infosetValue = threadSafeInfosetStore.getOrCreate(playerIndex,
                abstractionKey);
            assertNotNull(infosetValue);
            assertEquals(NUM_ACTIONS, infosetValue.getNumActions());

            createdKeys.add(abstractionKey);
          }

          successCount.incrementAndGet();
        } catch (Exception e) {
          e.printStackTrace();
        } finally {
          completionLatch.countDown();
        }
      });
    }

    startLatch.countDown();
    assertTrue(completionLatch.await(TEST_TIMEOUT_SECONDS, TimeUnit.SECONDS));
    assertEquals(NUM_THREADS, successCount.get());

    // Verify all keys were created
    int expectedKeys = NUM_THREADS * (NUM_OPERATIONS / 10);
    assertEquals(expectedKeys, createdKeys.size());

    System.out.println("Concurrent infoset creation test passed. Created " + createdKeys.size()
        + " unique infosets");
  }

  @Test
  @Timeout(TEST_TIMEOUT_SECONDS)
  void testConcurrentStrategyCache() throws InterruptedException {
    // Pre-populate store with some infosets
    for (int p = 0; p < NUM_PLAYERS; p++) {
      for (int k = 0; k < 100; k++) {
        ThreadSafeInfosetValue infosetValue = threadSafeInfosetStore.getOrCreate(p, k);
        // Add some regret to make strategy calculation meaningful
        for (int a = 0; a < NUM_ACTIONS; a++) {
          infosetValue.addRegret(a, (float) (a + 1));
        }
      }
    }

    CountDownLatch startLatch = new CountDownLatch(1);
    CountDownLatch completionLatch = new CountDownLatch(NUM_THREADS);
    AtomicLong cacheHits = new AtomicLong(0);
    AtomicLong cacheMisses = new AtomicLong(0);
    AtomicInteger successCount = new AtomicInteger(0);

    // Launch concurrent cache access threads
    for (int t = 0; t < NUM_THREADS; t++) {
      executorService.submit(() -> {
        try {
          startLatch.await();

          for (int i = 0; i < NUM_OPERATIONS / 10; i++) {
            int playerIndex = i % NUM_PLAYERS;
            int abstractionKey = i % 100; // Access existing keys

            float[] strategy = threadSafeInfosetStore.getCachedStrategy(playerIndex,
                abstractionKey);
            if (strategy != null) {
              cacheHits.incrementAndGet();

              // Verify strategy is valid
              float sum = 0.0f;
              for (float prob : strategy) {
                assertTrue(prob >= 0);
                sum += prob;
              }
              assertTrue(Math.abs(sum - 1.0f) < 1e-6f);
            } else {
              cacheMisses.incrementAndGet();
            }
          }

          successCount.incrementAndGet();
        } catch (Exception e) {
          e.printStackTrace();
        } finally {
          completionLatch.countDown();
        }
      });
    }

    startLatch.countDown();
    assertTrue(completionLatch.await(TEST_TIMEOUT_SECONDS, TimeUnit.SECONDS));
    assertEquals(NUM_THREADS, successCount.get());

    long totalAccess = cacheHits.get() + cacheMisses.get();
    double hitRate = totalAccess > 0 ? (double) cacheHits.get() / totalAccess * 100.0 : 0.0;

    System.out.println(
        "Concurrent strategy cache test passed. Hit rate: " + String.format("%.1f%%", hitRate) +
            " (" + cacheHits.get() + " hits, " + cacheMisses.get() + " misses)");

    // Cache hit rate should improve over time as cache warms up
    assertTrue(hitRate >= 0.0, "Hit rate should be non-negative");
  }

  @Test
  @Timeout(TEST_TIMEOUT_SECONDS)
  void testConcurrentDiscountApplication() throws InterruptedException {
    // Pre-populate store with infosets
    for (int p = 0; p < NUM_PLAYERS; p++) {
      for (int k = 0; k < 50; k++) {
        ThreadSafeInfosetValue infosetValue = threadSafeInfosetStore.getOrCreate(p, k);
        for (int a = 0; a < NUM_ACTIONS; a++) {
          infosetValue.addRegret(a, 100.0f); // Add significant regret
        }
      }
    }

    CountDownLatch startLatch = new CountDownLatch(1);
    CountDownLatch completionLatch = new CountDownLatch(NUM_THREADS);
    AtomicInteger successCount = new AtomicInteger(0);

    // Launch concurrent discount application threads
    for (int t = 0; t < NUM_THREADS; t++) {
      final int threadId = t;
      executorService.submit(() -> {
        try {
          startLatch.await();

          for (int i = 0; i < 10; i++) { // Fewer discount operations
            int currentTurn = threadId * 100 + i * 10;
            float discount = 0.9f;

            threadSafeInfosetStore.applyDiscount(currentTurn, 10, discount);
          }

          successCount.incrementAndGet();
        } catch (Exception e) {
          e.printStackTrace();
        } finally {
          completionLatch.countDown();
        }
      });
    }

    startLatch.countDown();
    assertTrue(completionLatch.await(TEST_TIMEOUT_SECONDS, TimeUnit.SECONDS));
    assertEquals(NUM_THREADS, successCount.get());

    // Verify that discount was applied (regrets should be less than original 100.0f)
    for (int p = 0; p < NUM_PLAYERS; p++) {
      ThreadSafeInfosetValue infosetValue = threadSafeInfosetStore.get(p, 0);
      if (infosetValue != null) {
        float[] regrets = infosetValue.getRegret();
        for (float regret : regrets) {
          assertTrue(regret <= 100.0f, "Regret should be discounted: " + regret);
        }
      }
    }

    System.out.println("Concurrent discount application test passed");
  }

  // ===== PERFORMANCE AND STRESS TESTS =====

  @Test
  @Timeout(TEST_TIMEOUT_SECONDS)
  void testHighConcurrencyStress() throws InterruptedException {
    final int STRESS_THREADS = 16;
    final int STRESS_OPERATIONS = 50000;

    ExecutorService stressExecutor = Executors.newFixedThreadPool(STRESS_THREADS);
    CountDownLatch startLatch = new CountDownLatch(1);
    CountDownLatch completionLatch = new CountDownLatch(STRESS_THREADS);
    AtomicInteger successCount = new AtomicInteger(0);
    AtomicLong totalOperations = new AtomicLong(0);

    long startTime = System.currentTimeMillis();

    // Launch high-concurrency stress test
    for (int t = 0; t < STRESS_THREADS; t++) {
      final int threadId = t;
      stressExecutor.submit(() -> {
        try {
          startLatch.await();

          for (int i = 0; i < STRESS_OPERATIONS; i++) {
            // Mix of operations
            if (i % 3 == 0) {
              // Regret update
              threadSafeInfosetValue.addRegret(i % NUM_ACTIONS, (float) (threadId + i) * 0.001f);
            } else if (i % 3 == 1) {
              // Strategy calculation
              float[] strategy = threadSafeInfosetValue.calculateStrategy();
              assertNotNull(strategy);
            } else {
              // InfosetStore operation
              int playerIndex = i % NUM_PLAYERS;
              int key = threadId * 1000 + i;
              ThreadSafeInfosetValue infoset = threadSafeInfosetStore.getOrCreate(playerIndex, key);
              assertNotNull(infoset);
            }

            totalOperations.incrementAndGet();
          }

          successCount.incrementAndGet();
        } catch (Exception e) {
          e.printStackTrace();
        } finally {
          completionLatch.countDown();
        }
      });
    }

    startLatch.countDown();
    assertTrue(completionLatch.await(TEST_TIMEOUT_SECONDS, TimeUnit.SECONDS));

    long endTime = System.currentTimeMillis();
    long duration = endTime - startTime;

    assertEquals(STRESS_THREADS, successCount.get());

    double operationsPerSecond = (double) totalOperations.get() / (duration / 1000.0);

    System.out.println("High concurrency stress test passed:");
    System.out.println("  Threads: " + STRESS_THREADS);
    System.out.println("  Total operations: " + totalOperations.get());
    System.out.println("  Duration: " + duration + "ms");
    System.out.println("  Operations/second: " + String.format("%.0f", operationsPerSecond));

    // Performance should be reasonable (at least 10,000 ops/sec)
    assertTrue(operationsPerSecond > 10000,
        "Performance should be at least 10,000 ops/sec, got: " + operationsPerSecond);

    stressExecutor.shutdown();
  }
}
