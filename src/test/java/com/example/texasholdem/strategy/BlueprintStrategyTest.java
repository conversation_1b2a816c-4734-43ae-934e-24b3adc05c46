package com.example.texasholdem.strategy;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.example.texasholdem.model.Action;
import com.example.texasholdem.model.ActionInstance;
import com.example.texasholdem.model.BettingRound;
import com.example.texasholdem.model.Card;
import com.example.texasholdem.model.Card.Rank;
import com.example.texasholdem.model.Card.Suit;
import com.example.texasholdem.model.Player;
import com.example.texasholdem.strategy.model.AbstractAction;
import com.example.texasholdem.strategy.model.ActionTrace;
import com.example.texasholdem.strategy.model.Infoset;
import com.example.texasholdem.strategy.model.InfosetStore;
import com.example.texasholdem.strategy.model.InfosetValue;
import com.example.texasholdem.strategy.train.InfosetStoreKey;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

public class BlueprintStrategyTest {

  private BlueprintStrategy strategy;
  private InfosetStore mockStore;
  private InfosetStoreKey mockAbstraction;

  @BeforeEach
  public void setUp() {
    mockStore = mock(InfosetStore.class);
    mockAbstraction = mock(InfosetStoreKey.class);
    strategy = new BlueprintStrategy(mockStore);
    strategy.setAbstraction(mockAbstraction);
  }

  @Test
  public void testNormalizedStrategy_handlesZeroSum() {
    float[] input = new float[]{0f, 0f, 0f};
    float[] result = strategy.normalizedStrategy(input);

    for (float v : result) {
      assertEquals(1f / input.length, v, 1e-5);
    }
  }

  @Test
  public void testNormalizedStrategy_normalCase() {
    float[] input = new float[]{1f, 2f, 3f};
    float[] result = strategy.normalizedStrategy(input);

    assertEquals(1f / 6f, result[0], 1e-5);
    assertEquals(2f / 6f, result[1], 1e-5);
    assertEquals(3f / 6f, result[2], 1e-5);
  }

  @Test
  public void testFilterIllegalActions_foldOnlyWhenAllIn() {
    Player player = new Player("p1", 0, 50); // all-in
    player.placeBet(50);
    int currentBet = 100;
    int potSize = 500;
    float[] inputStrategy = new float[7];
    Arrays.fill(inputStrategy, 1f);

    float[] result = strategy.filterIllegalActionsNew(player, currentBet, potSize, List.of(new Card(Rank.ACE, Suit.SPADES)), BettingRound.PREFLOP, inputStrategy);
    for (int i = 0; i < result.length; i++) {
      if (i == AbstractAction.FOLD.ordinal()) {
        assertTrue(result[i] > 0);
      } else {
        assertEquals(0f, result[i], 1e-5);
      }
    }
  }

  @Test
  public void testDecideAction_returnsCheckOrCall_whenOnlyThatIsNonZero() {
    InfosetValue dummyInfoset = mock(InfosetValue.class);
    float[] dummyStrategy = new float[7];
    dummyStrategy[AbstractAction.CHECK_OR_CALL.ordinal()] = 1f;
    when(dummyInfoset.getActionCounter()).thenReturn(dummyStrategy);

    try (MockedStatic<InfosetStoreKey> mocked = mockStatic(InfosetStoreKey.class)) {
      mocked.when(() -> InfosetStoreKey.abstractInfoset(any())).thenReturn(42);
      when(mockStore.getOrCreate(eq(0), eq(42))).thenReturn(dummyInfoset);

      Player player = new Player("p0", 0, 1000);
      List<Card> holeCards = List.of(new Card(Rank.ACE, Suit.SPADES));
      List<Card> communityCards = List.of(new Card(Rank.KING, Suit.HEARTS));
      List<ActionTrace> history = new ArrayList<>();

      ActionInstance action = strategy.decideAction(
          holeCards,
          communityCards,
          BettingRound.FLOP,
          player,
          0,
          history,
          100
      );

      assertNotNull(action);
      assertTrue(Set.of(Action.CHECK, Action.CALL).contains(action.getAction()));
    }
  }

  @Test
  public void testFilterIllegalActions_whenPlayerIsFolded_returnsAllZero() {
    Player player = new Player("p1", 0, 1000);
    player.fold();
    float[] inputStrategy = new float[7];
    Arrays.fill(inputStrategy, 1f);

    float[] result = strategy.filterIllegalActionsNew(
        player,
        100,
        500,
        List.of(new Card(Rank.ACE, Suit.SPADES)),
        BettingRound.PREFLOP,
        inputStrategy
    );
    for (int i = 0; i < result.length; i++) {
      if (i == AbstractAction.FOLD.ordinal()) {
        assertTrue(result[i] > 0);
      } else {
        assertEquals(0f, result[i], 1e-5);
      }
    }
  }

  @Test
  public void testFilterIllegalActions_whenPlayerHasZeroChips_returnsOnlyFoldIfValid() {
    Player player = new Player("p1", 0, 0);
    float[] inputStrategy = new float[7];
    Arrays.fill(inputStrategy, 1f);

    float[] result = strategy.filterIllegalActionsNew(
        player,
        0,
        100,
        List.of(new Card(Rank.ACE, Suit.SPADES)),
        BettingRound.PREFLOP,
        inputStrategy
    );

    assertEquals(inputStrategy[AbstractAction.FOLD.ordinal()], result[AbstractAction.FOLD.ordinal()], 1e-5);
    for (int i = 0; i < result.length; i++) {
      if (i != AbstractAction.FOLD.ordinal()) {
        assertEquals(0f, result[i], 1e-5);
      }
    }
  }

  @Test
  public void testNormalizedStrategy_withSinglePositiveValue() {
    float[] input = new float[]{0f, 0f, 10f, 0f};
    float[] result = strategy.normalizedStrategy(input);

    for (int i = 0; i < result.length; i++) {
      if (i == 2) {
        assertEquals(1f, result[i], 1e-5);
      } else {
        assertEquals(0f, result[i], 1e-5);
      }
    }
  }

  @Test
  public void testSampleFromDistribution_allZeroFallbacksToLast() throws Exception {
    float[] probabilities = new float[]{0f, 0f, 0f, 0f};

    Method sampleMethod = BlueprintStrategy.class.getDeclaredMethod("sampleFromDistribution", float[].class);
    sampleMethod.setAccessible(true);
    int result = (int) sampleMethod.invoke(strategy, (Object) probabilities);

    assertEquals(probabilities.length - 1, result);
  }
}