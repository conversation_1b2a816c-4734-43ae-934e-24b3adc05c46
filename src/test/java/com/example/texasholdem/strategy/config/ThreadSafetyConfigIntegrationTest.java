package com.example.texasholdem.strategy.config;

import com.example.texasholdem.strategy.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for ThreadSafetyConfig with parallel training components
 * 
 * This test suite validates:
 * 1. ThreadSafetyConfig parameter externalization and configurability
 * 2. Dynamic resource allocation based on system capabilities
 * 3. Configuration profile effectiveness (training/real-time/development)
 * 4. Runtime configuration adjustment and system load adaptation
 * 5. Integration with existing ThreadSafetyTest patterns
 * 6. Performance impact of different configuration settings
 */
@Execution(ExecutionMode.CONCURRENT)
public class ThreadSafetyConfigIntegrationTest {

    private static final int NUM_THREADS = 8;
    private static final int NUM_OPERATIONS = 1000;
    private static final int NUM_PLAYERS = 6;
    private static final int NUM_ACTIONS = 6;
    private static final long TEST_TIMEOUT_SECONDS = 30;

    private ThreadSafeInfosetStore store;
    private ExecutorService executorService;

    @BeforeEach
    void setUp() {
        store = new ThreadSafeInfosetStore(NUM_PLAYERS, NUM_ACTIONS);
        executorService = Executors.newFixedThreadPool(NUM_THREADS);
    }

    // ========================================
    // CONFIGURATION VALIDATION TESTS
    // ========================================

    @Test
    @Timeout(TEST_TIMEOUT_SECONDS)
    void testConfigurationProfiles() {
        // Test all configuration profiles are valid
        ThreadSafetyConfig productionConfig = ThreadSafetyConfig.productionConfig();
        ThreadSafetyConfig testingConfig = ThreadSafetyConfig.testingConfig();
        ThreadSafetyConfig developmentConfig = ThreadSafetyConfig.developmentConfig();
        
        assertTrue(productionConfig.isValid(), "Production config should be valid");
        assertTrue(testingConfig.isValid(), "Testing config should be valid");
        assertTrue(developmentConfig.isValid(), "Development config should be valid");
        
        // Test enhanced configurations
        EnhancedThreadSafetyConfig enhancedTraining = EnhancedThreadSafetyConfig.forTraining();
        EnhancedThreadSafetyConfig enhancedRealtime = EnhancedThreadSafetyConfig.forTesting();
        EnhancedThreadSafetyConfig enhancedDev = EnhancedThreadSafetyConfig.forDevelopment();
        
        assertTrue(enhancedTraining.isValid(), "Enhanced training config should be valid");
        assertTrue(enhancedRealtime.isValid(), "Enhanced real-time config should be valid");
        assertTrue(enhancedDev.isValid(), "Enhanced development config should be valid");
        
        log("Configuration profiles validated");
    }

    @Test
    @Timeout(TEST_TIMEOUT_SECONDS)
    void testDynamicResourceAllocation() {
        // Test that configuration adapts to system resources
        EnhancedThreadSafetyConfig config = EnhancedThreadSafetyConfig.forTraining();
        
        int availableProcessors = Runtime.getRuntime().availableProcessors();
        long availableMemoryMB = Runtime.getRuntime().maxMemory() / (1024 * 1024);
        
        // Verify thread count is reasonable for system
        assertTrue(config.getNumTrainingThreads() > 0, "Should have at least 1 thread");
        assertTrue(config.getNumTrainingThreads() <= availableProcessors, 
            "Should not exceed available processors");
        assertTrue(config.getMaxThreadPoolSize() >= config.getNumTrainingThreads(),
            "Max pool size should be >= training threads");
        
        // Verify memory allocation is reasonable
        long memoryPerThread = config.getMemoryThresholdBytesPerThread();
        long totalMemoryNeeded = memoryPerThread * config.getNumTrainingThreads() / (1024 * 1024);
        assertTrue(totalMemoryNeeded < availableMemoryMB, 
            "Total memory needed should be less than available");
        
        log("Dynamic resource allocation validated: {} threads, {}MB per thread", 
            config.getNumTrainingThreads(), memoryPerThread / (1024 * 1024));
    }

    @Test
    @Timeout(TEST_TIMEOUT_SECONDS)
    void testSystemLoadAdaptation() {
        // Test configuration adjustment based on system load
        EnhancedThreadSafetyConfig baseConfig = EnhancedThreadSafetyConfig.forTraining();
        EnhancedThreadSafetyConfig adjustedConfig = baseConfig.adjustForSystemLoad();
        
        assertNotNull(adjustedConfig, "Adjusted config should not be null");
        assertTrue(adjustedConfig.isValid(), "Adjusted config should be valid");
        
        // Verify adjustment logic
        Runtime runtime = Runtime.getRuntime();
        long freeMemory = runtime.freeMemory();
        long totalMemory = runtime.totalMemory();
        double memoryUsage = (double) (totalMemory - freeMemory) / totalMemory;
        
        if (memoryUsage > 0.8) {
            // High memory usage should reduce thread count
            assertTrue(adjustedConfig.getNumTrainingThreads() <= baseConfig.getNumTrainingThreads(),
                "High memory usage should reduce thread count");
        }
        
        log("System load adaptation validated: memory usage %.1f", memoryUsage * 100);
    }

    // ========================================
    // PARAMETER EXTERNALIZATION TESTS
    // ========================================

    @Test
    @Timeout(TEST_TIMEOUT_SECONDS)
    void testParameterExternalization() {
        // Test that hard-coded parameters are now configurable
        EnhancedThreadSafetyConfig config = EnhancedThreadSafetyConfig.forDevelopment();
        
        // Verify MCCFR algorithm parameters are configurable
        assertTrue(config.getPruneThreshold() >= 0, "Prune threshold should be configurable");
        assertTrue(config.getPruningProbability() >= 0 && config.getPruningProbability() <= 1,
            "Pruning probability should be valid");
        assertTrue(config.getLcfrThreshold() > 0, "LCFR threshold should be positive");
        assertTrue(config.getMemoryThresholdBytesPerThread() > 0, "Memory threshold should be positive");
        
        // Verify thread management parameters are configurable
        assertTrue(config.getThreadKeepAliveMs() > 0, "Thread keep-alive should be positive");
        assertTrue(config.getThreadPoolQueueCapacity() > 0, "Queue capacity should be positive");
        
        // Verify monitoring parameters are configurable
        assertTrue(config.getProgressLoggingInterval() > 0, "Progress logging interval should be positive");
        
        log("Parameter externalization validated: {} configurable parameters", 15);
    }

    @Test
    @Timeout(TEST_TIMEOUT_SECONDS)
    void testConfigurationImpactOnPerformance() {
        // Test that different configurations have measurable impact
        
        // High-performance configuration
        EnhancedThreadSafetyConfig highPerfConfig = EnhancedThreadSafetyConfig.builder()
            .baseConfig(ThreadSafetyConfig.productionConfig())
            .optimalThreadCount(Math.min(8, Runtime.getRuntime().availableProcessors()))
            .enableThreadPerformanceProfiling(false) // Reduce overhead
            .enableDetailedProgressLogging(false)
            .build();
        
        // Low-performance configuration (for comparison)
        EnhancedThreadSafetyConfig lowPerfConfig = EnhancedThreadSafetyConfig.builder()
            .baseConfig(ThreadSafetyConfig.developmentConfig())
            .optimalThreadCount(1) // Single-threaded
            .enableThreadPerformanceProfiling(true) // Add overhead
            .enableDetailedProgressLogging(true)
            .build();
        
        assertTrue(highPerfConfig.getNumTrainingThreads() >= lowPerfConfig.getNumTrainingThreads(),
            "High-perf config should use more threads");
        
        log("Configuration performance impact validated");
    }

    // ========================================
    // INTEGRATION WITH EXISTING PATTERNS TESTS
    // ========================================

    @Test
    @Timeout(TEST_TIMEOUT_SECONDS)
    void testIntegrationWithThreadSafetyTestPatterns() throws InterruptedException {
        // Test that ThreadSafetyConfig works with existing ThreadSafetyTest patterns
        
        ThreadSafetyConfig config = ThreadSafetyConfig.productionConfig();
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch completionLatch = new CountDownLatch(NUM_THREADS);
        AtomicInteger successCount = new AtomicInteger(0);

        // Use configuration parameters in test
        int numThreads = Math.min(NUM_THREADS, config.getNumTrainingThreads());
        long timeoutMs = config.getThreadFailureRecoveryTimeoutMs();

        // Launch concurrent operations using configured parameters
        for (int t = 0; t < numThreads; t++) {
            final int threadId = t;
            executorService.submit(() -> {
                try {
                    startLatch.await();

                    // Perform operations similar to existing ThreadSafetyTest
                    for (int i = 0; i < NUM_OPERATIONS / 10; i++) {
                        int playerIndex = i % NUM_PLAYERS;
                        int abstractionKey = threadId * NUM_OPERATIONS + i;

                        ThreadSafeInfosetValue infosetValue = store.getOrCreate(playerIndex, abstractionKey);
                        assertNotNull(infosetValue);
                        
                        // Add regret update
                        infosetValue.addRegret(i % NUM_ACTIONS, (float) (threadId * 0.1 + i * 0.001));
                    }

                    successCount.incrementAndGet();
                } catch (Exception e) {
                    log("Thread {} failed: {}", threadId, e.getMessage());
                } finally {
                    completionLatch.countDown();
                }
            });
        }

        startLatch.countDown();
        assertTrue(completionLatch.await(timeoutMs, TimeUnit.MILLISECONDS),
            "Should complete within configured timeout");
        assertEquals(numThreads, successCount.get());

        log("Integration with ThreadSafetyTest patterns validated: {} threads", numThreads);
    }

    @Test
    @Timeout(TEST_TIMEOUT_SECONDS)
    void testConcurrentActionCounterWithConfiguration() throws InterruptedException {
        // Test concurrent action counter updates with configurable parameters
        
        EnhancedThreadSafetyConfig config = EnhancedThreadSafetyConfig.forDevelopment();
        ThreadSafeInfosetValue infosetValue = new ThreadSafeInfosetValue(NUM_ACTIONS);
        
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch completionLatch = new CountDownLatch(config.getNumTrainingThreads());
        AtomicInteger successCount = new AtomicInteger(0);

        // Launch concurrent action counter updates
        for (int t = 0; t < config.getNumTrainingThreads(); t++) {
            final int threadId = t;
            executorService.submit(() -> {
                try {
                    startLatch.await();

                    int operations = NUM_OPERATIONS / config.getNumTrainingThreads();
                    for (int i = 0; i < operations; i++) {
                        float[] strategy = new float[NUM_ACTIONS];
                        Arrays.fill(strategy, 1.0f / NUM_ACTIONS); // Uniform strategy

                        infosetValue.updateActionCounter(strategy);
                    }

                    successCount.incrementAndGet();
                } catch (Exception e) {
                    log("Action counter thread {} failed: {}", threadId, e.getMessage());
                } finally {
                    completionLatch.countDown();
                }
            });
        }

        startLatch.countDown();
        assertTrue(completionLatch.await(config.getThreadFailureRecoveryTimeoutMs(), TimeUnit.MILLISECONDS));
        assertEquals(config.getNumTrainingThreads(), successCount.get());

        // Verify action counters are consistent
        float[] actionCounters = infosetValue.getActionCounter();
        assertNotNull(actionCounters);
        assertEquals(NUM_ACTIONS, actionCounters.length);

        // All action counters should be equal (uniform strategy)
        float expectedValue = actionCounters[0];
        for (float counter : actionCounters) {
            assertEquals(expectedValue, counter, 1e-6f,
                "All action counters should be equal for uniform strategy");
        }

        log("Concurrent action counter with configuration validated");
    }

    // ========================================
    // PERFORMANCE IMPACT TESTS
    // ========================================

    @Test
    @Timeout(TEST_TIMEOUT_SECONDS)
    void testConfigurationPerformanceImpact() throws InterruptedException {
        // Test performance impact of different monitoring settings
        
        // Configuration with minimal monitoring
        EnhancedThreadSafetyConfig minimalConfig = EnhancedThreadSafetyConfig.builder()
            .baseConfig(ThreadSafetyConfig.testingConfig())
            .enableThreadPerformanceProfiling(false)
            .enableDetailedProgressLogging(false)
            .enableMemoryProfiling(false)
            .build();
        
        // Configuration with full monitoring
        EnhancedThreadSafetyConfig fullConfig = EnhancedThreadSafetyConfig.builder()
            .baseConfig(ThreadSafetyConfig.developmentConfig())
            .enableThreadPerformanceProfiling(true)
            .enableDetailedProgressLogging(true)
            .enableMemoryProfiling(true)
            .build();
        
        // Measure performance with minimal monitoring
        long startTime = System.currentTimeMillis();
        performStandardOperations(minimalConfig.getNumTrainingThreads());
        long minimalTime = System.currentTimeMillis() - startTime;
        
        // Measure performance with full monitoring
        startTime = System.currentTimeMillis();
        performStandardOperations(fullConfig.getNumTrainingThreads());
        long fullTime = System.currentTimeMillis() - startTime;
        
        // Monitoring should have some overhead, but not excessive
        double overhead = (double) (fullTime - minimalTime) / minimalTime * 100;
        assertTrue(overhead < 50, "Monitoring overhead should be < 50%, got: " + overhead + "%");
        
        log("Configuration performance impact validated: %.1f%% monitoring overhead", overhead);
    }

    private void performStandardOperations(int numThreads) throws InterruptedException {
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch completionLatch = new CountDownLatch(numThreads);

        for (int t = 0; t < numThreads; t++) {
            final int threadId = t;
            executorService.submit(() -> {
                try {
                    startLatch.await();

                    for (int i = 0; i < 100; i++) {
                        int playerIndex = i % NUM_PLAYERS;
                        int key = threadId * 100 + i;
                        ThreadSafeInfosetValue infoset = store.getOrCreate(playerIndex, key);
                        infoset.addRegret(i % NUM_ACTIONS, 0.1f);
                    }
                } catch (Exception e) {
                    // Ignore for performance test
                } finally {
                    completionLatch.countDown();
                }
            });
        }

        startLatch.countDown();
        completionLatch.await(10, TimeUnit.SECONDS);
    }

    // ========================================
    // CONFIGURATION VALIDATION TESTS
    // ========================================

    @Test
    @Timeout(TEST_TIMEOUT_SECONDS)
    void testConfigurationValidation() {
        // Test invalid configurations are rejected
        
        // Test invalid thread counts
        assertThrows(IllegalArgumentException.class, () -> {
            EnhancedThreadSafetyConfig.builder()
                .baseConfig(ThreadSafetyConfig.productionConfig())
                .optimalThreadCount(0) // Invalid
                .build();
        });
        
        // Test invalid memory thresholds
        assertThrows(IllegalArgumentException.class, () -> {
            EnhancedThreadSafetyConfig.builder()
                .baseConfig(ThreadSafetyConfig.productionConfig())
                .memoryThresholdBytesPerThread(-1) // Invalid
                .build();
        });
        
        log("Configuration validation tests passed");
    }

    // ========================================
    // HELPER METHODS
    // ========================================

    private void log(String message, Object... args) {
        System.out.println(String.format(message, args));
    }


}
