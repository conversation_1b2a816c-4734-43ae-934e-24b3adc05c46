package com.example.texasholdem.strategy;

import com.example.texasholdem.strategy.model.InfosetStore;
import com.example.texasholdem.strategy.model.InfosetValue;
import com.example.texasholdem.strategy.train.StrategyVersionManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.junit.jupiter.api.io.TempDir;

import java.nio.file.Path;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for StrategyVersionManager functionality
 */
public class StrategyVersionManagerTest {

  @TempDir
  Path tempDir;

  private StrategyVersionManager versionManager;
  private InfosetStore testStore;

  @BeforeEach
  void setUp() {
    versionManager = new StrategyVersionManager(tempDir, 5, true);
    testStore = createTestInfosetStore();
  }

  private InfosetStore createTestInfosetStore() {
    InfosetStore store = new InfosetStore(3, 6);

    // Add test strategies
    for (int player = 0; player < 3; player++) {
      for (int i = 0; i < 100; i++) {
        InfosetValue infoset = new InfosetValue(6);
        float[] strategy = {0.2f, 0.3f, 0.1f, 0.15f, 0.15f, 0.1f};
        infoset.updateActionCounter(strategy);
        store.put(player, i, infoset);
      }
    }

    return store;
  }

  @Test
  @Timeout(value = 10)
  void testSaveAndLoadVersionedStrategy() {
    System.out.println("\n🧪 Testing Save and Load Versioned Strategy");

    // Debug original store
    System.out.println("Original store info:");
    System.out.println("  Players: " + testStore.getNumPlayers());
    System.out.println("  Actions: " + testStore.getNumActions());
    for (int player = 0; player < 3; player++) {
      System.out.println("  Player " + player + " regular infosets: " + testStore.getMapForPlayer(player).size());
      System.out.println("  Player " + player + " abstraction infosets: " + testStore.getAbstractionMapForPlayer(player).size());
    }

    // Save strategy
    String version = versionManager.saveVersionedStrategy(testStore, "Test strategy v1");
    assertNotNull(version, "Version should not be null");
    assertTrue(version.startsWith("v"), "Version should start with 'v'");

    System.out.println("Saved strategy version: " + version);

    // Load strategy
    InfosetStore loadedStore = versionManager.loadVersionedStrategy(version);
    assertNotNull(loadedStore, "Loaded store should not be null");

    // Debug loaded store
    System.out.println("Loaded store info:");
    System.out.println("  Players: " + loadedStore.getNumPlayers());
    System.out.println("  Actions: " + loadedStore.getNumActions());
    for (int player = 0; player < 3; player++) {
      System.out.println("  Player " + player + " regular infosets: " + loadedStore.getMapForPlayer(player).size());
      System.out.println("  Player " + player + " abstraction infosets: " + loadedStore.getAbstractionMapForPlayer(player).size());
    }

    assertEquals(testStore.getNumPlayers(), loadedStore.getNumPlayers(),
        "Player count should match");
    assertEquals(testStore.getNumActions(), loadedStore.getNumActions(),
        "Action count should match");

    // Verify strategy data
    for (int player = 0; player < 3; player++) {
      assertEquals(testStore.getAbstractionMapForPlayer(player).size(),
          loadedStore.getAbstractionMapForPlayer(player).size(),
          "Abstraction map size should match for player " + player);
    }

    System.out.println("✅ Strategy saved and loaded successfully");
  }

  @Test
  void testVersionMetadata() {
    System.out.println("\n🧪 Testing Version Metadata");

    String version = versionManager.saveVersionedStrategy(testStore, "Test metadata");

    StrategyVersionManager.StrategyMetadata metadata = versionManager.getVersionMetadata(version);
    assertNotNull(metadata, "Metadata should not be null");
    assertEquals(version, metadata.version, "Version should match");
    assertEquals(3, metadata.numPlayers, "Player count should match");
    assertEquals(6, metadata.numActions, "Action count should match");
    assertTrue(metadata.numInfosets > 0, "Should have infosets");
    assertTrue(metadata.fileSize > 0, "File size should be positive");
    assertEquals("Test metadata", metadata.description, "Description should match");

    System.out.println("Metadata: " + metadata);
    System.out.println("✅ Metadata validation successful");
  }

  @Test
  void testGetAvailableVersions() {
    System.out.println("\n🧪 Testing Get Available Versions");

    // Save multiple versions
    String v1 = versionManager.saveVersionedStrategy(testStore, "Version 1");
    String v2 = versionManager.saveVersionedStrategy(testStore, "Version 2");
    String v3 = versionManager.saveVersionedStrategy(testStore, "Version 3");

    List<String> versions = versionManager.getAvailableVersions();
    assertEquals(3, versions.size(), "Should have 3 versions");
    assertTrue(versions.contains(v1), "Should contain version 1");
    assertTrue(versions.contains(v2), "Should contain version 2");
    assertTrue(versions.contains(v3), "Should contain version 3");

    // Verify newest first ordering
    assertEquals(v3, versions.get(0), "Newest version should be first");

    System.out.println("Available versions: " + versions);
    System.out.println("✅ Version listing successful");
  }

  @Test
  void testRollbackToVersion() {
    System.out.println("\n🧪 Testing Rollback to Version");

    // Save initial version
    String v1 = versionManager.saveVersionedStrategy(testStore, "Version 1");

    // Create modified store
    InfosetStore modifiedStore = createTestInfosetStore();
    InfosetValue newInfoset = new InfosetValue(6);
    float[] newStrategy = {0.5f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f};
    newInfoset.updateActionCounter(newStrategy);
    modifiedStore.put(0, 999, newInfoset);

    // Save modified version
    String v2 = versionManager.saveVersionedStrategy(modifiedStore, "Version 2");
    assertEquals(v2, versionManager.getCurrentVersion(), "Current version should be v2");

    // Rollback to v1
    InfosetStore rolledBackStore = versionManager.rollbackToVersion(v1);
    assertEquals(v1, versionManager.getCurrentVersion(), "Current version should be v1 after rollback");

    // Verify rollback worked
    assertNull(rolledBackStore.getAbstractionMapForPlayer(0).get(999),
        "Rolled back store should not contain new infoset");

    System.out.println("Rolled back from " + v2 + " to " + v1);
    System.out.println("✅ Rollback successful");
  }

  @Test
  void testDeleteVersion() {
    System.out.println("\n🧪 Testing Delete Version");

    // Save multiple versions
    String v1 = versionManager.saveVersionedStrategy(testStore, "Version 1");
    String v2 = versionManager.saveVersionedStrategy(testStore, "Version 2");

    assertEquals(2, versionManager.getAvailableVersions().size(), "Should have 2 versions");

    // Delete v1 (not current)
    boolean deleted = versionManager.deleteVersion(v1);
    assertTrue(deleted, "Should successfully delete v1");
    assertEquals(1, versionManager.getAvailableVersions().size(), "Should have 1 version after deletion");
    assertFalse(versionManager.getAvailableVersions().contains(v1), "Should not contain deleted version");

    // Try to delete current version (should fail)
    assertThrows(IllegalArgumentException.class, () -> {
      versionManager.deleteVersion(v2);
    }, "Should not allow deleting current version");

    System.out.println("✅ Version deletion successful");
  }

  @Test
  void testMaxVersionsCleanup() {
    System.out.println("\n🧪 Testing Max Versions Cleanup");

    // Create manager with max 3 versions
    StrategyVersionManager limitedManager = new StrategyVersionManager(tempDir.resolve("limited"), 3, true);

    // Save 5 versions with debugging
    String[] versions = new String[5];
    for (int i = 0; i < 5; i++) {
      versions[i] = limitedManager.saveVersionedStrategy(testStore, "Version " + (i + 1));
      System.out.println("Saved version " + (i + 1) + ": " + versions[i]);

      // Check intermediate state
      List<String> currentVersions = limitedManager.getAvailableVersions();
      System.out.println("  Available versions after save " + (i + 1) + ": " + currentVersions.size() + " versions");

      // Longer delay to ensure different timestamps (though nanosecond precision should handle this)
      try { Thread.sleep(50); } catch (InterruptedException e) { /* ignore */ }
    }

    // Verify all version IDs are unique
    Set<String> uniqueVersions = new HashSet<>(Arrays.asList(versions));
    assertEquals(5, uniqueVersions.size(), "All 5 version IDs should be unique. Got: " + Arrays.toString(versions));

    // Should only have 3 versions (oldest cleaned up)
    List<String> availableVersions = limitedManager.getAvailableVersions();
    System.out.println("Final available versions: " + availableVersions);
    assertEquals(3, availableVersions.size(), "Should have max 3 versions after cleanup");

    // Should contain the 3 newest versions (versions[2], versions[3], versions[4])
    assertTrue(availableVersions.contains(versions[2]), "Should contain version 3: " + versions[2]);
    assertTrue(availableVersions.contains(versions[3]), "Should contain version 4: " + versions[3]);
    assertTrue(availableVersions.contains(versions[4]), "Should contain version 5: " + versions[4]);

    // Should NOT contain the 2 oldest versions (versions[0], versions[1])
    assertFalse(availableVersions.contains(versions[0]), "Should NOT contain version 1 (oldest): " + versions[0]);
    assertFalse(availableVersions.contains(versions[1]), "Should NOT contain version 2 (second oldest): " + versions[1]);

    System.out.println("✅ Version cleanup successful - kept newest 3, removed oldest 2");
  }

  @Test
  void testStrategyValidation() {
    System.out.println("\n🧪 Testing Strategy Validation");

    // Test with valid strategy
    String validVersion = versionManager.saveVersionedStrategy(testStore, "Valid strategy");
    assertNotNull(validVersion, "Should save valid strategy");

    // Test with invalid strategy (empty)
    InfosetStore emptyStore = new InfosetStore(3, 6);
    assertThrows(IllegalArgumentException.class, () -> {
      versionManager.saveVersionedStrategy(emptyStore, "Invalid strategy");
    }, "Should reject empty strategy");

    System.out.println("✅ Strategy validation successful");
  }

  @Test
  @Timeout(value = 5)
  void testFastCompressionPerformance() {
    System.out.println("\n🧪 Testing Fast Compression Performance");

    // Create larger test store
    InfosetStore largeStore = new InfosetStore(3, 6);
    for (int player = 0; player < 3; player++) {
      for (int i = 0; i < 1000; i++) {
        InfosetValue infoset = new InfosetValue(6);
        float[] strategy = {0.2f, 0.3f, 0.1f, 0.15f, 0.15f, 0.1f};
        infoset.updateActionCounter(strategy);
        largeStore.put(player, i, infoset);
      }
    }

    // Measure save/load performance
    long startTime = System.currentTimeMillis();
    String version = versionManager.saveVersionedStrategy(largeStore, "Performance test");
    long saveTime = System.currentTimeMillis() - startTime;

    startTime = System.currentTimeMillis();
    InfosetStore loadedStore = versionManager.loadVersionedStrategy(version);
    long loadTime = System.currentTimeMillis() - startTime;

    System.out.println("📊 Performance Results:");
    System.out.println("  Save time: " + saveTime + "ms");
    System.out.println("  Load time: " + loadTime + "ms");
    System.out.println("  Total infosets: " + (3 * 1000));

    // Verify reasonable performance
    assertTrue(saveTime < 3000, "Save should complete within 3 seconds");
    assertTrue(loadTime < 2000, "Load should complete within 2 seconds");
    assertNotNull(loadedStore, "Loaded store should not be null");

    System.out.println("✅ Performance test successful");
  }

  @Test
  void testVersionManagerInitialization() {
    System.out.println("\n🧪 Testing Version Manager Initialization");

    // Save a version
    String version = versionManager.saveVersionedStrategy(testStore, "Persistence test");

    // Create new manager with same directory
    StrategyVersionManager newManager = new StrategyVersionManager(tempDir, 5, true);

    // Should load existing versions
    List<String> versions = newManager.getAvailableVersions();
    assertEquals(1, versions.size(), "Should load existing version");
    assertEquals(version, versions.get(0), "Should load correct version");
    assertEquals(version, newManager.getCurrentVersion(), "Should set correct current version");

    System.out.println("Loaded existing version: " + version);
    System.out.println("✅ Initialization with existing data successful");
  }
}