package com.example.texasholdem.strategy;

import com.example.texasholdem.model.*;
import com.example.texasholdem.strategy.model.*;
import com.example.texasholdem.strategy.train.InfosetStore;
import com.example.texasholdem.strategy.train.ThreadSafeInfosetStore;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Comprehensive test suite for Enhanced Pluribus Strategy with Nested Search Algorithm
 * 
 * Tests the implementation of the Pluribus nested search algorithm including:
 * - Dynamic action abstraction expansion
 * - Infoset state management and freezing
 * - Subgame root updating
 * - Integration with enhanced parallel training infrastructure
 * - Performance validation and compliance with specifications
 */
@Slf4j
public class EnhancedPluribusStrategyTest {

    private static final int TEST_TIMEOUT_SECONDS = 30;
    private static final int NUM_PLAYERS = 3;
    private static final int NUM_ACTIONS = 6;

    private PluribusStrategy strategy;
    private InfosetStore mockStore;
    private Player testPlayer;
    private List<Card> testHoleCards;
    private List<Card> testCommunityCards;
    private List<ActionTrace> testActionHistory;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // Create thread-safe store for testing
        mockStore = new ThreadSafeInfosetStore(NUM_PLAYERS, NUM_ACTIONS);
        
        // Initialize strategy with enhanced search capabilities
        strategy = new PluribusStrategy(mockStore);
        
        // Create test player
        testPlayer = new Player("TestPlayer", 0, 10000);
        testPlayer.setCurrentBet(0);
        
        // Create test cards
        testHoleCards = Arrays.asList(new Card("Ah"), new Card("Ks"));
        testCommunityCards = Arrays.asList(new Card("Qd"), new Card("Jc"), new Card("Th"));
        
        // Create test action history
        testActionHistory = new ArrayList<>();
        testActionHistory.add(new ActionTrace(1, AbstractAction.CHECK_OR_CALL, 0, 0, 1));
        testActionHistory.add(new ActionTrace(2, AbstractAction.BET_OR_RAISE_60, 0, 1, 2));
        
        log.info("Enhanced Pluribus Strategy test setup completed");
    }

    // ========================================
    // NESTED SEARCH ALGORITHM CORE TESTS
    // ========================================

    @Test
    @Timeout(TEST_TIMEOUT_SECONDS)
    void testNestedSearchAlgorithmInitialization() {
        // Test that nested search state is properly initialized
        Map<String, Object> searchState = strategy.getNestedSearchState();
        
        assertNotNull(searchState);
        assertTrue(searchState.containsKey("currentInfoset"));
        assertTrue(searchState.containsKey("subgameRoot"));
        assertTrue(searchState.containsKey("frozenInfosets"));
        assertTrue(searchState.containsKey("dynamicAbstractions"));
        
        // Verify initial state
        assertEquals("", searchState.get("currentInfoset"));
        assertEquals(BettingRound.PREFLOP, BettingRound.valueOf(searchState.get("subgameRoot").toString()));
        
        log.info("✅ Nested search algorithm initialization validated");
    }

    @Test
    @Timeout(TEST_TIMEOUT_SECONDS)
    void testInfosetManagementAndFreezing() {
        // Test preflop decision (should freeze infoset)
        ActionInstance preflopAction = strategy.decideAction(
            testHoleCards, new ArrayList<>(), BettingRound.PREFLOP,
            testPlayer, 0, new ArrayList<>(), 100);
        
        assertNotNull(preflopAction);
        
        // Verify infoset was created and frozen
        Map<String, Object> searchState = strategy.getNestedSearchState();
        assertFalse(((String) searchState.get("currentInfoset")).isEmpty());
        assertTrue(((Map<?, ?>) searchState.get("frozenInfosets")).size() > 0);
        
        log.info("✅ Infoset management and freezing validated");
    }

    @Test
    @Timeout(TEST_TIMEOUT_SECONDS)
    void testSubgameRootUpdating() {
        // Make preflop decision
        strategy.decideAction(testHoleCards, new ArrayList<>(), BettingRound.PREFLOP,
            testPlayer, 0, new ArrayList<>(), 100);
        
        Map<String, Object> initialState = strategy.getNestedSearchState();
        assertEquals(BettingRound.PREFLOP, BettingRound.valueOf(initialState.get("subgameRoot").toString()));
        
        // Make flop decision (should update subgame root)
        strategy.decideAction(testHoleCards, testCommunityCards, BettingRound.FLOP,
            testPlayer, 100, testActionHistory, 300);
        
        Map<String, Object> updatedState = strategy.getNestedSearchState();
        assertEquals(BettingRound.FLOP, BettingRound.valueOf(updatedState.get("subgameRoot").toString()));
        
        // Verify frozen infosets were cleared for new subgame
        assertTrue(((Map<?, ?>) updatedState.get("frozenInfosets")).size() >= 0);
        
        log.info("✅ Subgame root updating (CheckNewRound) validated");
    }

    @Test
    @Timeout(TEST_TIMEOUT_SECONDS)
    void testDynamicActionAbstractionExpansion() {
        // Create action history with opponent action not in base abstraction
        List<ActionTrace> expansionHistory = new ArrayList<>();
        expansionHistory.add(new ActionTrace(1, AbstractAction.BET_OR_RAISE_500, 1, 0, 1)); // Large bet
        
        // Make decision that should trigger abstraction expansion
        ActionInstance action = strategy.decideAction(
            testHoleCards, testCommunityCards, BettingRound.FLOP,
            testPlayer, 200, expansionHistory, 500);
        
        assertNotNull(action);
        
        // Verify abstraction expansion occurred
        Map<String, Object> stats = strategy.getEnhancedSearchStatistics();
        assertTrue((Long) stats.get("actionAbstractionExpansions") >= 0);
        assertTrue((Integer) stats.get("dynamicAbstractions") >= 0);
        
        log.info("✅ Dynamic action abstraction expansion validated");
    }

    // ========================================
    // PERFORMANCE AND INTEGRATION TESTS
    // ========================================

    @Test
    @Timeout(TEST_TIMEOUT_SECONDS)
    void testEnhancedSearchPerformance() {
        long startTime = System.currentTimeMillis();
        
        // Perform multiple search operations
        for (int i = 0; i < 5; i++) {
            BettingRound round = (i < 2) ? BettingRound.PREFLOP : BettingRound.FLOP;
            List<Card> community = (i < 2) ? new ArrayList<>() : testCommunityCards;
            
            ActionInstance action = strategy.decideAction(
                testHoleCards, community, round, testPlayer, 
                i * 50, testActionHistory, 200 + i * 100);
            
            assertNotNull(action);
        }
        
        long totalTime = System.currentTimeMillis() - startTime;
        
        // Verify performance is within acceptable bounds (should be < 2 seconds for 5 searches)
        assertTrue(totalTime < 2000, "Enhanced search took too long: " + totalTime + "ms");
        
        // Verify search statistics
        Map<String, Object> stats = strategy.getEnhancedSearchStatistics();
        assertTrue((Long) stats.get("searchInvocations") > 0);
        
        log.info("✅ Enhanced search performance validated: {}ms for 5 searches", totalTime);
    }

    @Test
    @Timeout(TEST_TIMEOUT_SECONDS)
    void testSearchStatisticsAndMonitoring() {
        // Perform some search operations
        strategy.decideAction(testHoleCards, new ArrayList<>(), BettingRound.PREFLOP,
            testPlayer, 0, new ArrayList<>(), 100);
        strategy.decideAction(testHoleCards, testCommunityCards, BettingRound.FLOP,
            testPlayer, 100, testActionHistory, 300);
        
        // Test statistics collection
        Map<String, Object> stats = strategy.getEnhancedSearchStatistics();
        
        assertNotNull(stats);
        assertTrue(stats.containsKey("searchInvocations"));
        assertTrue(stats.containsKey("actionAbstractionExpansions"));
        assertTrue(stats.containsKey("abstractionExpansionRate"));
        assertTrue(stats.containsKey("currentInfoset"));
        assertTrue(stats.containsKey("subgameRoot"));
        
        // Test statistics reset
        strategy.resetEnhancedSearchStatistics();
        Map<String, Object> resetStats = strategy.getEnhancedSearchStatistics();
        assertEquals(0L, resetStats.get("searchInvocations"));
        assertEquals(0L, resetStats.get("actionAbstractionExpansions"));
        
        log.info("✅ Search statistics and monitoring validated");
    }

    @Test
    @Timeout(TEST_TIMEOUT_SECONDS)
    void testIntegrationWithTrainingInfrastructure() {
        // Test that enhanced search integrates properly with existing systems
        assertTrue(strategy.isEnhancedSearchIntegrated() || 
                   strategy.getEnhancedSearchStatistics().get("searchInvocations").equals(0L));
        
        // Perform search to trigger integration
        strategy.decideAction(testHoleCards, testCommunityCards, BettingRound.FLOP,
            testPlayer, 100, testActionHistory, 300);
        
        // Verify integration is active
        assertTrue(strategy.isEnhancedSearchIntegrated());
        
        log.info("✅ Integration with training infrastructure validated");
    }

    // ========================================
    // ALGORITHM COMPLIANCE TESTS
    // ========================================

    @Test
    @Timeout(TEST_TIMEOUT_SECONDS)
    void testPluribusAlgorithmCompliance() {
        // Test that the implementation follows Pluribus algorithm specifications
        
        // 1. Test OurTurn() - should freeze infoset strategy
        strategy.decideAction(testHoleCards, new ArrayList<>(), BettingRound.PREFLOP,
            testPlayer, 0, new ArrayList<>(), 100);
        
        Map<String, Object> state = strategy.getNestedSearchState();
        assertTrue(((Map<?, ?>) state.get("frozenInfosets")).size() > 0);
        
        // 2. Test CheckNewRound() - should update subgame root
        strategy.decideAction(testHoleCards, testCommunityCards, BettingRound.FLOP,
            testPlayer, 100, testActionHistory, 300);
        
        state = strategy.getNestedSearchState();
        assertEquals(BettingRound.FLOP, BettingRound.valueOf(state.get("subgameRoot").toString()));
        
        // 3. Test OpponentTurn() - should expand action abstraction when needed
        List<ActionTrace> opponentHistory = new ArrayList<>();
        opponentHistory.add(new ActionTrace(1, AbstractAction.BET_OR_RAISE_200, 1, 0, 1));
        
        strategy.decideAction(testHoleCards, testCommunityCards, BettingRound.TURN,
            testPlayer, 200, opponentHistory, 600);
        
        // Verify algorithm compliance
        Map<String, Object> stats = strategy.getEnhancedSearchStatistics();
        assertNotNull(stats.get("searchInvocations"));
        assertNotNull(stats.get("actionAbstractionExpansions"));
        
        log.info("✅ Pluribus algorithm compliance validated");
    }

    @Test
    @Timeout(TEST_TIMEOUT_SECONDS)
    void testErrorHandlingAndGracefulDegradation() {
        // Test with invalid inputs
        ActionInstance action1 = strategy.decideAction(
            null, testCommunityCards, BettingRound.FLOP,
            testPlayer, 100, testActionHistory, 300);
        assertNotNull(action1); // Should not crash
        
        ActionInstance action2 = strategy.decideAction(
            testHoleCards, null, BettingRound.FLOP,
            testPlayer, 100, null, 300);
        assertNotNull(action2); // Should not crash
        
        // Test with extreme values
        ActionInstance action3 = strategy.decideAction(
            testHoleCards, testCommunityCards, BettingRound.RIVER,
            testPlayer, Integer.MAX_VALUE, testActionHistory, Integer.MAX_VALUE);
        assertNotNull(action3); // Should handle gracefully
        
        log.info("✅ Error handling and graceful degradation validated");
    }

    @Test
    @Timeout(TEST_TIMEOUT_SECONDS)
    void testPerformanceLogging() {
        // Test performance logging functionality
        assertDoesNotThrow(() -> {
            strategy.logEnhancedSearchPerformance();
        });
        
        // Perform some operations and log again
        strategy.decideAction(testHoleCards, testCommunityCards, BettingRound.FLOP,
            testPlayer, 100, testActionHistory, 300);
        
        assertDoesNotThrow(() -> {
            strategy.logEnhancedSearchPerformance();
        });
        
        log.info("✅ Performance logging validated");
    }
}
