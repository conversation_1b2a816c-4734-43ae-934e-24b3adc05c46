package com.example.texasholdem.strategy;

import com.example.texasholdem.model.*;
import com.example.texasholdem.strategy.model.*;
import com.example.texasholdem.strategy.model.InfosetStore;
import com.example.texasholdem.strategy.model.ThreadSafeInfosetStore;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * Simple validation test for Enhanced Pluribus Strategy implementation
 * 
 * This test validates the core functionality of the enhanced real-time search
 * algorithm without requiring complex test framework setup.
 */
@Slf4j
public class EnhancedSearchValidationTest {

    public static void main(String[] args) {
        log.info("🚀 Starting Enhanced Pluribus Strategy Validation");
        
        try {
            EnhancedSearchValidationTest validator = new EnhancedSearchValidationTest();
            validator.runValidationTests();
            log.info("✅ All validation tests completed successfully!");
        } catch (Exception e) {
            log.error("❌ Validation failed: {}", e.getMessage(), e);
            System.exit(1);
        }
    }

    public void runValidationTests() {
        log.info("=== Enhanced Search Algorithm Validation ===");
        
        // Test 1: Basic initialization
        testBasicInitialization();
        
        // Test 2: Nested search state management
        testNestedSearchStateManagement();
        
        // Test 3: Dynamic action abstraction
        testDynamicActionAbstraction();
        
        // Test 4: Performance characteristics
        testPerformanceCharacteristics();
        
        // Test 5: Integration validation
        testIntegrationValidation();
        
        log.info("=== All Enhanced Search Tests Passed ===");
    }

    private void testBasicInitialization() {
        log.info("🔧 Test 1: Basic Initialization");
        
        ThreadSafeInfosetStore store = new ThreadSafeInfosetStore(3, 6);
        InfosetStore store2 = new InfosetStore(3, 6);
        PluribusStrategy strategy = new PluribusStrategy(store2);
        
        // Verify initial state
        Map<String, Object> searchState = strategy.getNestedSearchState();
        assert searchState != null : "Search state should not be null";
        assert searchState.containsKey("currentInfoset") : "Should contain currentInfoset";
        assert searchState.containsKey("subgameRoot") : "Should contain subgameRoot";
        
        log.info("✅ Basic initialization validated");
    }

    private void testNestedSearchStateManagement() {
        log.info("🔧 Test 2: Nested Search State Management");
        
        ThreadSafeInfosetStore store = new ThreadSafeInfosetStore(3, 6);
        InfosetStore store2 = new InfosetStore(3, 6);
        PluribusStrategy strategy = new PluribusStrategy(store2);
        
        // Create test data
        Player testPlayer = new Player("TestPlayer", 0, 10000);
        List<Card> holeCards = Arrays.asList(new Card("Ah"), new Card("Ks"));
        List<Card> communityCards = Arrays.asList(new Card("Qd"), new Card("Jc"), new Card("Th"));
        List<ActionTrace> actionHistory = new ArrayList<>();
        
        // Test preflop decision (should create and freeze infoset)
        ActionInstance preflopAction = strategy.decideAction(
            holeCards, new ArrayList<>(), BettingRound.PREFLOP,
            testPlayer, 0, new ArrayList<>(), 100);
        
        assert preflopAction != null : "Preflop action should not be null";
        
        Map<String, Object> state1 = strategy.getNestedSearchState();
        String initialInfoset = (String) state1.get("currentInfoset");
        assert !initialInfoset.isEmpty() : "Current infoset should be set";
        
        // Test flop decision (should update subgame root)
        ActionInstance flopAction = strategy.decideAction(
            holeCards, communityCards, BettingRound.FLOP,
            testPlayer, 100, actionHistory, 300);
        
        assert flopAction != null : "Flop action should not be null";
        
        Map<String, Object> state2 = strategy.getNestedSearchState();
        assert state2.get("subgameRoot").toString().equals("FLOP") : "Subgame root should be updated to FLOP";
        
        log.info("✅ Nested search state management validated");
    }

    private void testDynamicActionAbstraction() {
        log.info("🔧 Test 3: Dynamic Action Abstraction");
        
        ThreadSafeInfosetStore store = new ThreadSafeInfosetStore(3, 6);
        InfosetStore store2 = new InfosetStore(3, 6);
        PluribusStrategy strategy = new PluribusStrategy(store2);
        
        Player testPlayer = new Player("TestPlayer", 0, 10000);
        List<Card> holeCards = Arrays.asList(new Card("Ah"), new Card("Ks"));
        List<Card> communityCards = Arrays.asList(new Card("Qd"), new Card("Jc"), new Card("Th"));
        
        // Create action history with opponent large bet (should trigger abstraction expansion)
        List<ActionTrace> expansionHistory = new ArrayList<>();
        expansionHistory.add(new ActionTrace(1, AbstractAction.BET_OR_RAISE_500, 1, 0, 1));
        
        Map<String, Object> statsBefore = strategy.getEnhancedSearchStatistics();
        long expansionsBefore = (Long) statsBefore.get("actionAbstractionExpansions");
        
        // Make decision that should consider the opponent's large bet
        ActionInstance action = strategy.decideAction(
            holeCards, communityCards, BettingRound.FLOP,
            testPlayer, 200, expansionHistory, 500);
        
        assert action != null : "Action should not be null";
        
        Map<String, Object> statsAfter = strategy.getEnhancedSearchStatistics();
        long expansionsAfter = (Long) statsAfter.get("actionAbstractionExpansions");
        
        // Verify that abstraction system is working (expansions may or may not occur depending on implementation)
        assert expansionsAfter >= expansionsBefore : "Abstraction system should be functional";
        
        log.info("✅ Dynamic action abstraction validated");
    }

    private void testPerformanceCharacteristics() {
        log.info("🔧 Test 4: Performance Characteristics");
        
        ThreadSafeInfosetStore store = new ThreadSafeInfosetStore(3, 6);
        InfosetStore store2 = new InfosetStore(3, 6);
        PluribusStrategy strategy = new PluribusStrategy(store2);
        
        Player testPlayer = new Player("TestPlayer", 0, 10000);
        List<Card> holeCards = Arrays.asList(new Card("Ah"), new Card("Ks"));
        List<Card> communityCards = Arrays.asList(new Card("Qd"), new Card("Jc"), new Card("Th"));
        List<ActionTrace> actionHistory = new ArrayList<>();
        
        long startTime = System.currentTimeMillis();
        
        // Perform multiple search operations
        for (int i = 0; i < 10; i++) {
            BettingRound round = (i < 3) ? BettingRound.PREFLOP : 
                               (i < 6) ? BettingRound.FLOP : BettingRound.TURN;
            List<Card> community = (i < 3) ? new ArrayList<>() : communityCards;
            
            ActionInstance action = strategy.decideAction(
                holeCards, community, round, testPlayer, 
                i * 25, actionHistory, 100 + i * 50);
            
            assert action != null : "Action " + i + " should not be null";
        }
        
        long totalTime = System.currentTimeMillis() - startTime;
        
        // Performance should be reasonable (< 5 seconds for 10 searches)
        assert totalTime < 5000 : "Performance too slow: " + totalTime + "ms for 10 searches";
        
        Map<String, Object> stats = strategy.getEnhancedSearchStatistics();
        long searchInvocations = (Long) stats.get("searchInvocations");
        assert searchInvocations > 0 : "Should have recorded search invocations";
        
        log.info("✅ Performance characteristics validated: {}ms for 10 searches, {} invocations", 
                totalTime, searchInvocations);
    }

    private void testIntegrationValidation() {
        log.info("🔧 Test 5: Integration Validation");
        
        ThreadSafeInfosetStore store = new ThreadSafeInfosetStore(3, 6);
        InfosetStore store2 = new InfosetStore(3, 6);
        PluribusStrategy strategy = new PluribusStrategy(store2);
        
        // Test statistics collection
        Map<String, Object> stats = strategy.getEnhancedSearchStatistics();
        assert stats != null : "Statistics should not be null";
        assert stats.containsKey("searchInvocations") : "Should track search invocations";
        assert stats.containsKey("actionAbstractionExpansions") : "Should track abstraction expansions";
        assert stats.containsKey("abstractionExpansionRate") : "Should calculate expansion rate";
        
        // Test performance logging (should not throw exceptions)
        try {
            strategy.logEnhancedSearchPerformance();
        } catch (Exception e) {
            throw new AssertionError("Performance logging should not throw exceptions: " + e.getMessage());
        }
        
        // Test statistics reset
        strategy.resetEnhancedSearchStatistics();
        Map<String, Object> resetStats = strategy.getEnhancedSearchStatistics();
        assert resetStats.get("searchInvocations").equals(0L) : "Search invocations should be reset";
        assert resetStats.get("actionAbstractionExpansions").equals(0L) : "Expansions should be reset";
        
        // Test integration status
        boolean integrated = strategy.isEnhancedSearchIntegrated();
        // Should be false after reset, true after operations
        
        log.info("✅ Integration validation completed");
    }
}
