package com.example.texasholdem.service;
import com.example.texasholdem.model.Card;
import com.example.texasholdem.model.Player;
import com.example.texasholdem.model.BettingRound;
import com.example.texasholdem.model.SidePot;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Set;

import static com.example.texasholdem.model.BettingRound.FLOP;
import static com.example.texasholdem.model.BettingRound.PREFLOP;
import static org.junit.jupiter.api.Assertions.*;

public class GameServiceTest {

    private GameService gameService;

    @BeforeEach
    void setUp() {
        gameService = new GameService();
        gameService.addPlayer("Player1", 1,1000);
        gameService.addPlayer("Player2",2, 1000);
    }

    @Test
    void testInitializeGame() {
        gameService.dealHoleCards();
        gameService.dealCommunityCards(3);
        assertEquals(3, gameService.getCommunityCards().size());

        gameService.initializeGame();
        assertEquals(0, gameService.getCommunityCards().size());
        assertEquals(null, gameService.getCurrentRound());

        for (Player player : gameService.getPlayers()) {
            assertTrue(player.getHand().isEmpty());
        }
    }

    @Test
    void testAddPlayer() {
        gameService.addPlayer("Player3", 3,1000);
        boolean exists = gameService.getPlayers().stream()
                .anyMatch(p -> p.getName().equals("Player3") && p.getChips() == 1000);
        assertTrue(exists);
    }

    @Test
    void testDealHoleCards() {
        gameService.dealHoleCards();
        for (Player player : gameService.getPlayers()) {
            assertEquals(2, player.getHandSize());
        }
    }

    @Test
    void testDealInvalidCommunityCardsThrowsException() {
        assertThrows(IllegalArgumentException.class, () -> gameService.dealCommunityCards(6));
        assertThrows(IllegalArgumentException.class, () -> gameService.dealCommunityCards(0));
    }

    @Test
    void testStartBettingRound() {
        gameService.startBettingRound(PREFLOP);
        assertEquals(PREFLOP, gameService.getCurrentRound());
    }

    @Test
    void testStartBettingRoundInvalidRound() {
        assertThrows(IllegalArgumentException.class, () -> gameService.startBettingRound(null));
    }

    @Test
    void testBettingActions() {
        gameService.startBettingRound(PREFLOP);
        Player player = gameService.getPlayers().get(0);
        player.placeBet(100);
        assertEquals(900, player.getChips());
        assertEquals(100, player.getCurrentBet());
    }

    @Test
    void testBettingActionsInvalidAmount() {
        Player player = gameService.getPlayers().get(0);
        assertThrows(IllegalArgumentException.class, () -> player.placeBet(2000)); // Not enough chips
    }

    @Test
    void testBettingActionsFold() {
        Player player = gameService.getPlayers().get(0);
        player.fold();
        assertTrue(player.isFolded());
    }

    @Test
    void testBettingActionsAllIn() {
        Player player = gameService.getPlayers().get(0);
        player.placeBet(1000);
        assertTrue(player.isAllIn());
    }

    @Test
    void testResetPlayerState() {
        Player player = gameService.getPlayers().get(0);
        player.placeBet(100);
        player.fold();
        player.resetPlayerState();
        assertEquals(0, player.getCurrentBet());
        assertFalse(player.isFolded());
        assertFalse(player.isAllIn());
        assertTrue(player.getHand().isEmpty());
    }

    @Test
    void testGetCurrentRoundWhenRoundIsNull() {
        assertEquals(null, gameService.getCurrentRound());
    }

    @Test
    void testGetCurrentRoundWhenRoundIsSet() {
        gameService.startBettingRound(FLOP);
        assertEquals(FLOP, gameService.getCurrentRound());
    }

    @Test
    void testSetFirstToActValidIndex() {
        gameService.setFirstToAct(1);
    }

    @Test
    void testSetFirstToActInvalidIndex() {
        assertThrows(IllegalArgumentException.class, () -> gameService.setFirstToAct(-1));
        assertThrows(IllegalArgumentException.class, () -> gameService.setFirstToAct(5)); // assuming < 5 players
    }

    @Test
    void testDistributeWinningsGivesChipsToWinner() {
        gameService.getPlayers().get(0).setFolded(false);  // Simulate winner
        gameService.getPlayers().get(1).setFolded(true);

        gameService.getPlayers().get(0).placeBet(500);
        gameService.getPlayers().get(1).placeBet(500);

        gameService.collectBetsIntoPot();
        gameService.distributeWinnings();

        assertTrue(gameService.getPlayers().get(0).getChips() > 1000);
    }

    @Test
    void testDistributeWinningsNoWinner() {
        gameService.getPlayers().get(0).setFolded(true);
        gameService.getPlayers().get(1).setFolded(true);

        gameService.collectBetsIntoPot();
        gameService.distributeWinnings();

        assertEquals(1000, gameService.getPlayers().get(0).getChips());
        assertEquals(1000, gameService.getPlayers().get(1).getChips());
    }

    @Test
    void testDistributeWinningsMultipleWinners() {
        Player player1 = gameService.getPlayers().get(0);
        Player player2 = gameService.getPlayers().get(1);

        //gameService.dealCommunityCards(5);
        gameService.setCommunityCards(List.of(
            new Card(Card.Rank.TEN, Card.Suit.HEARTS),
            new Card(Card.Rank.TWO, Card.Suit.SPADES),
            new Card(Card.Rank.EIGHT, Card.Suit.DIAMONDS),
            new Card(Card.Rank.FOUR, Card.Suit.CLUBS),
            new Card(Card.Rank.THREE, Card.Suit.HEARTS)
        ));

        player1.setFolded(false);
        player2.setFolded(false);

        player1.addCardToHand(new Card(Card.Rank.ACE, Card.Suit.HEARTS));
        player1.addCardToHand(new Card(Card.Rank.KING, Card.Suit.HEARTS));

        player2.addCardToHand(new Card(Card.Rank.ACE, Card.Suit.CLUBS));
        player2.addCardToHand(new Card(Card.Rank.KING, Card.Suit.CLUBS));

        gameService.getPlayers().get(0).placeBet(500);
        gameService.getPlayers().get(1).placeBet(500);

        gameService.collectBetsIntoPot();
        gameService.distributeWinnings();

        assertTrue(gameService.getPlayers().get(0).getChips() > 500);
        assertTrue(gameService.getPlayers().get(1).getChips() > 500);
    }

    @Test
    void testDistributeWinningsNoBets() {
        gameService.getPlayers().get(0).setFolded(false);
        gameService.getPlayers().get(1).setFolded(false);

        gameService.distributeWinnings();

        assertEquals(1000, gameService.getPlayers().get(0).getChips());
        assertEquals(1000, gameService.getPlayers().get(1).getChips());
    }

    @Test
    void testDistributeWinningsWithSidePot() {
        Player player1 = gameService.getPlayers().get(0);
        Player player2 = gameService.getPlayers().get(1);

        player1.setChips(7000);
        player2.setChips(400);

        player1.setFolded(false);
        player2.setFolded(false);

        player1.addCardToHand(new Card(Card.Rank.ACE, Card.Suit.HEARTS));
        player1.addCardToHand(new Card(Card.Rank.KING, Card.Suit.HEARTS));

        player2.addCardToHand(new Card(Card.Rank.TWO, Card.Suit.CLUBS));
        player2.addCardToHand(new Card(Card.Rank.THREE, Card.Suit.SPADES));

        gameService.setCommunityCards(List.of(
                new Card(Card.Rank.QUEEN, Card.Suit.SPADES),
                new Card(Card.Rank.JACK, Card.Suit.HEARTS),
                new Card(Card.Rank.NINE, Card.Suit.DIAMONDS),
                new Card(Card.Rank.FIVE, Card.Suit.CLUBS),
                new Card(Card.Rank.FOUR, Card.Suit.SPADES)
        ));

        player1.placeBet(100);

        player2.placeBet(400);

        player1.placeBet(500);

        gameService.collectBetsIntoPot();
        gameService.distributeWinnings();

        assertEquals(7400, player1.getChips());
        assertEquals(0, player2.getChips());
    }

    @Test
    void testCollectBetsIntoPot_MultipleAllInsAndSidePots() {
        gameService = new GameService();
        gameService.addPlayer("P0", 0,1000);
        gameService.addPlayer("P1", 1,100);  // All-in preflop
        gameService.addPlayer("P2",2, 200);  // All-in flop
        gameService.addPlayer("P3",3, 300);  // All-in flop
        gameService.addPlayer("P4",4, 400);  // Call flop

        List<Player> players = gameService.getPlayers();


        // Preflop actions
        players.get(0).placeBet(50);    // Big blind
        players.get(0).fold();          // Folded
        players.get(1).placeBet(100);   // All-in
        players.get(2).placeBet(100);   // Call
        players.get(3).placeBet(100);   // Call
        players.get(4).placeBet(100);   // Call

        SidePot mainPot = gameService.getMainPot();
        gameService.collectBetsIntoPot();  // Collect preflop bets

        // Validate preflop main pot
        assertEquals(450, mainPot.getAmount());
        assertEquals(Set.of(players.get(1), players.get(2), players.get(3), players.get(4)),
                mainPot.getEligiblePlayers());

        // No side pots yet
        List<SidePot> sidePots = gameService.getSidePots();
        assertEquals(0, sidePots.size());

        // Flop actions
        players.get(2).placeBet(100);  // All-in (total 200)
        players.get(3).placeBet(200);  // All-in (total 300)
        players.get(4).placeBet(200);  // Call (total 300)

        gameService.collectBetsIntoPot();  // Collect flop bets

        // Validate total pot
        int totalPot = gameService.getMainPot().getAmount();
        for (SidePot pot : gameService.getSidePots()) {
            totalPot += pot.getAmount();
            System.out.println("Add pot amount: "+ pot.getAmount());
        }
        assertEquals(950, totalPot);  // 450 (preflop) + 100*3 + 100*2 = 950

        // Validate main pot remains unchanged
        assertEquals(450, gameService.getMainPot().getAmount());
        assertEquals(Set.of(players.get(1), players.get(2), players.get(3), players.get(4)),
                gameService.getMainPot().getEligiblePlayers());

        // Validate side pots
        List<SidePot> updatedSidePots = gameService.getSidePots();
        assertEquals(2, updatedSidePots.size());

        SidePot sidePot1 = updatedSidePots.get(0);
        assertEquals(300, sidePot1.getAmount());
        assertEquals(Set.of(players.get(2), players.get(3), players.get(4)),
                sidePot1.getEligiblePlayers());

        SidePot sidePot2 = updatedSidePots.get(1);
        assertEquals(200, sidePot2.getAmount());
        assertEquals(Set.of(players.get(3), players.get(4)),
                sidePot2.getEligiblePlayers());
    }

    @Test
    void testCollectPot_NoAllIn() {
        gameService = new GameService();
        gameService.startBettingRound(PREFLOP);
        gameService.addPlayer("Player0", 0,1000);
        gameService.addPlayer("Player1", 1,1000);
        gameService.addPlayer("Player2", 2,1000);
        gameService.addPlayer("Player3",3, 1000);

        Player player0 = gameService.getPlayers().get(0);
        Player player1 = gameService.getPlayers().get(1);
        Player player2 = gameService.getPlayers().get(2);
        Player player3 = gameService.getPlayers().get(3);
        player0.placeBet(100);
        player1.placeBet(100);
        player2.setFolded(true);
        player3.placeBet(100);

        gameService.collectBetsIntoPot();
        assertEquals(300, gameService.getMainPot().getAmount());
        assertTrue(gameService.getMainPot().getEligiblePlayers().containsAll(Set.of(player0, player1, player3)));
        assertTrue(gameService.getSidePots().isEmpty());
    }

    @Test
    void testCollectPot_WithAllIn() {
        gameService = new GameService();
        gameService.startBettingRound(PREFLOP);
        gameService.addPlayer("Player0", 0,1000);
        gameService.addPlayer("Player1", 1,1000);
        gameService.addPlayer("Player2", 2,1000);
        gameService.addPlayer("Player3",3, 1000);

        Player player0 = gameService.getPlayers().get(0);
        Player player1 = gameService.getPlayers().get(1);
        Player player2 = gameService.getPlayers().get(2);
        Player player3 = gameService.getPlayers().get(3);

        player0.placeBet(100);
        player1.placeBet(1000);
        player2.setFolded(true);
        player3.placeBet(1000);
        player0.setFolded(true);

        gameService.collectBetsIntoPot();

        assertEquals(2100, gameService.getMainPot().getAmount());
        assertTrue(gameService.getMainPot().getEligiblePlayers().containsAll(Set.of(player1, player3)));
    }

    @Test
    void testCollectPot_WithAllInAndSidePot() {
        gameService = new GameService();
        gameService.startBettingRound(PREFLOP);
        gameService.addPlayer("Player0", 0,100);
        gameService.addPlayer("Player1",1, 1000);
        gameService.addPlayer("Player2",2, 1000);
        gameService.addPlayer("Player3",3, 1000);

        Player player0 = gameService.getPlayers().get(0);
        Player player1 = gameService.getPlayers().get(1);
        Player player2 = gameService.getPlayers().get(2);
        Player player3 = gameService.getPlayers().get(3);

        player0.placeBet(100);
        player1.placeBet(1000);
        player2.setFolded(true);
        player3.placeBet(1000);

        gameService.collectBetsIntoPot();

        assertEquals(300, gameService.getMainPot().getAmount());
        assertTrue(gameService.getMainPot().getEligiblePlayers().containsAll(Set.of(player0, player1, player3)));

        assertEquals(1, gameService.getSidePots().size());
        assertEquals(1800, gameService.getSidePots().get(0).getAmount());
        assertTrue(gameService.getSidePots().get(0).getEligiblePlayers().containsAll(Set.of(player1, player3)));
    }

    @Test
    void testCollectPot_WithMultipleAllIn() {
        gameService = new GameService();
        gameService.startBettingRound(PREFLOP);
        gameService.addPlayer("Player0", 0,100);
        gameService.addPlayer("Player1",1, 200);
        gameService.addPlayer("Player2", 2,300);
        gameService.addPlayer("Player3",3, 400);

        Player player0 = gameService.getPlayers().get(0);
        Player player1 = gameService.getPlayers().get(1);
        Player player2 = gameService.getPlayers().get(2);
        Player player3 = gameService.getPlayers().get(3);

        // player0 goes all-in, player1 goes all-in, player2 goes all-in, player3 calls
        player0.placeBet(100);
        player1.placeBet(200);
        player2.placeBet(300);
        player3.placeBet(300);

        gameService.collectBetsIntoPot();

        assertEquals(400, gameService.getMainPot().getAmount());
        assertTrue(gameService.getMainPot().getEligiblePlayers().containsAll(Set.of(player0, player1, player2, player3)));

        assertEquals(2, gameService.getSidePots().size());
        assertEquals(300, gameService.getSidePots().get(0).getAmount());
        assertTrue(gameService.getSidePots().get(0).getEligiblePlayers().containsAll(Set.of(player1, player2, player3)));

        assertEquals(200, gameService.getSidePots().get(1).getAmount());
        assertTrue(gameService.getSidePots().get(1).getEligiblePlayers().containsAll(Set.of(player2, player3)));
    }

    @Test
    void testCollectPot_WithMultipleAllIn_MultiRound() {
        gameService = new GameService();
        gameService.startBettingRound(PREFLOP);
        gameService.addPlayer("Player0", 0,100);
        gameService.addPlayer("Player1",1, 200);
        gameService.addPlayer("Player2", 2,300);
        gameService.addPlayer("Player3",3, 400);

        Player player0 = gameService.getPlayers().get(0);
        Player player1 = gameService.getPlayers().get(1);
        Player player2 = gameService.getPlayers().get(2);
        Player player3 = gameService.getPlayers().get(3);

        // player0 goes all-in, player1 calls, player2 calls, player3 calls
        player0.placeBet(100);
        player1.placeBet(100);
        player2.placeBet(100);
        player3.placeBet(100);

        gameService.collectBetsIntoPot();

        assertEquals(400, gameService.getMainPot().getAmount());
        assertTrue(gameService.getMainPot().getEligiblePlayers().containsAll(Set.of(player0, player1, player2, player3)));
        assertEquals(0, gameService.getSidePots().size());

        gameService.startBettingRound(FLOP);

        // player0 went all-in already, player1 goes all-in, player2 calls, player3 calls
        player1.placeBet(100);
        player2.placeBet(100);
        player3.placeBet(100);

        gameService.collectBetsIntoPot();

        assertEquals(400, gameService.getMainPot().getAmount());
        assertTrue(gameService.getMainPot().getEligiblePlayers().containsAll(Set.of(player0, player1, player2, player3)));
        assertEquals(1, gameService.getSidePots().size());
        assertEquals(300, gameService.getSidePots().get(0).getAmount());
        assertTrue(gameService.getSidePots().get(0).getEligiblePlayers().containsAll(Set.of(player1, player2, player3)));

        gameService.startBettingRound(BettingRound.TURN);

        // player0 went all-in already, player1 went all-in already, player2 goes all-in, player3 calls
        player2.placeBet(100);
        player3.placeBet(100);

        gameService.collectBetsIntoPot();

        assertEquals(400, gameService.getMainPot().getAmount());
        assertTrue(gameService.getMainPot().getEligiblePlayers().containsAll(Set.of(player0, player1, player2, player3)));
        assertEquals(2, gameService.getSidePots().size());
        assertEquals(300, gameService.getSidePots().get(0).getAmount());
        assertTrue(gameService.getSidePots().get(0).getEligiblePlayers().containsAll(Set.of(player1, player2, player3)));
        assertEquals(200, gameService.getSidePots().get(1).getAmount());
        assertTrue(gameService.getSidePots().get(1).getEligiblePlayers().containsAll(Set.of(player2, player3)));
    }

    @Test
    void testCollectPot_WithMultipleAllIn_MultiRound_2() {
        gameService = new GameService();
        gameService.startBettingRound(PREFLOP);
        gameService.addPlayer("Player0", 0,100);
        gameService.addPlayer("Player1",1, 200);
        gameService.addPlayer("Player2", 2,300);
        gameService.addPlayer("Player3",3, 400);

        Player player0 = gameService.getPlayers().get(0);
        Player player1 = gameService.getPlayers().get(1);
        Player player2 = gameService.getPlayers().get(2);
        Player player3 = gameService.getPlayers().get(3);

        // player0 goes all-in, player1 calls, player2 calls, player3 calls
        player0.placeBet(100);
        player1.placeBet(100);
        player2.placeBet(100);
        player3.placeBet(100);

        gameService.collectBetsIntoPot();

        assertEquals(400, gameService.getMainPot().getAmount());
        assertTrue(gameService.getMainPot().getEligiblePlayers().containsAll(Set.of(player0, player1, player2, player3)));
        assertEquals(0, gameService.getSidePots().size());

        gameService.startBettingRound(FLOP);

        // player0 went all-in already, player1 bets, player2 calls, player3 calls
        player1.placeBet(50);
        player2.placeBet(50);
        player3.placeBet(50);

        gameService.collectBetsIntoPot();

        assertEquals(400, gameService.getMainPot().getAmount());
        assertTrue(gameService.getMainPot().getEligiblePlayers().containsAll(Set.of(player0, player1, player2, player3)));
        assertEquals(1, gameService.getSidePots().size());
        assertEquals(150, gameService.getSidePots().get(0).getAmount());
        assertTrue(gameService.getSidePots().get(0).getEligiblePlayers().containsAll(Set.of(player1, player2, player3)));

        gameService.startBettingRound(BettingRound.TURN);

        // player0 went all-in already, player1 goes all-in, player2 goes all-in, player3 calls
        player1.placeBet(50);
        player2.placeBet(150);
        player3.placeBet(150);

        gameService.collectBetsIntoPot();

        assertEquals(400, gameService.getMainPot().getAmount());
        assertTrue(gameService.getMainPot().getEligiblePlayers().containsAll(Set.of(player0, player1, player2, player3)));
        assertEquals(2, gameService.getSidePots().size());
        assertEquals(300, gameService.getSidePots().get(0).getAmount());
        assertTrue(gameService.getSidePots().get(0).getEligiblePlayers().containsAll(Set.of(player1, player2, player3)));
        assertEquals(200, gameService.getSidePots().get(1).getAmount());
        assertTrue(gameService.getSidePots().get(1).getEligiblePlayers().containsAll(Set.of(player2, player3)));
    }






}