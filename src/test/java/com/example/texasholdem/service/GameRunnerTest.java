package com.example.texasholdem.service;

import com.example.texasholdem.model.Player;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class GameRunnerTest {

    private GameService gameService;
    private GameRunner gameRunner;

    @BeforeEach
    void setUp() {
        gameService = new GameService();
        gameRunner = new GameRunner(gameService);
    }

    @Test
    void testRunGamesMultipleTimes() {
        gameRunner.runGames();
        long activePlayers = gameService.getPlayers().stream().filter(Player::isActive).count();
        assertTrue(activePlayers >= 1);
    }

    @Test
    void testDealerRotation() {
        assertDoesNotThrow(() -> gameRunner.runGames());
    }
}