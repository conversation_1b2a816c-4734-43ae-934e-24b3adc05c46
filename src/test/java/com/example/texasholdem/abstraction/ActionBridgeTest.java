package com.example.texasholdem.abstraction;

import com.example.texasholdem.strategy.model.AbstractAction;
import com.example.texasholdem.abstraction.AbstractionDataStructures.*;
import com.example.texasholdem.model.BettingRound;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test suite for ActionBridge integration between AbstractAction and ActionTemplate
 * 
 * Validates the seamless conversion and compatibility between the existing
 * AbstractAction system and the new ActionTemplate abstraction system.
 */
public class ActionBridgeTest {

    @BeforeEach
    void setUp() {
        // Clear caches before each test
        ActionBridge.clearCaches();
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testAbstractActionToActionTemplateConversion() {
        // Test all AbstractAction values
        for (AbstractAction action : AbstractAction.values()) {
            ActionTemplate template = ActionBridge.toActionTemplate(action);
            
            assertNotNull(template, "Template should not be null for action: " + action);
            assertNotNull(template.getActionType(), "ActionType should not be null for: " + action);
            assertNotNull(template.getDescription(), "Description should not be null for: " + action);
            
            // Verify specific mappings
            switch (action) {
                case FOLD:
                    assertEquals(ActionType.FOLD, template.getActionType());
                    break;
                case CHECK_OR_CALL:
                    assertEquals(ActionType.CALL, template.getActionType());
                    break;
                case BET_OR_RAISE_30:
                    assertEquals(ActionType.BET, template.getActionType());
                    assertEquals(0.3, template.getBetSizeRatio(), 0.01);
                    break;
                case BET_OR_RAISE_100:
                    assertEquals(ActionType.RAISE, template.getActionType());
                    assertEquals(1.0, template.getBetSizeRatio(), 0.01);
                    break;
            }
        }
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testActionTemplateToAbstractActionConversion() {
        // Test fold
        ActionTemplate foldTemplate = ActionTemplate.builder()
            .actionType(ActionType.FOLD)
            .build();
        assertEquals(AbstractAction.FOLD, ActionBridge.toAbstractAction(foldTemplate));
        
        // Test call
        ActionTemplate callTemplate = ActionTemplate.builder()
            .actionType(ActionType.CALL)
            .build();
        assertEquals(AbstractAction.CHECK_OR_CALL, ActionBridge.toAbstractAction(callTemplate));
        
        // Test bet sizes
        ActionTemplate smallBetTemplate = ActionTemplate.builder()
            .actionType(ActionType.BET)
            .betSizeRatio(0.3)
            .build();
        assertEquals(AbstractAction.BET_OR_RAISE_30, ActionBridge.toAbstractAction(smallBetTemplate));
        
        ActionTemplate largeBetTemplate = ActionTemplate.builder()
            .actionType(ActionType.RAISE)
            .betSizeRatio(2.0)
            .build();
        assertEquals(AbstractAction.BET_OR_RAISE_200, ActionBridge.toAbstractAction(largeBetTemplate));
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testRoundTripConversion() {
        // Test that converting AbstractAction -> ActionTemplate -> AbstractAction preserves compatibility
        for (AbstractAction originalAction : AbstractAction.values()) {
            ActionTemplate template = ActionBridge.toActionTemplate(originalAction);
            AbstractAction convertedAction = ActionBridge.toAbstractAction(template);
            
            // Should be compatible even if not identical
            assertTrue(ActionBridge.isCompatible(originalAction, template),
                "Original action " + originalAction + " should be compatible with its template");
            
            // ActionType should match
            ActionType expectedType = ActionBridge.getActionType(originalAction);
            assertEquals(expectedType, template.getActionType(),
                "ActionType should match for " + originalAction);
        }
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testEnhancedTemplateCreation() {
        AbstractAction action = AbstractAction.BET_OR_RAISE_60;
        BettingRound round = BettingRound.FLOP;
        double potSize = 200.0;
        int stackSize = 1000;
        
        ActionTemplate enhanced = ActionBridge.createEnhancedTemplate(action, round, potSize, stackSize);
        
        assertNotNull(enhanced);
        assertEquals(ActionType.BET, enhanced.getActionType());
        assertNotNull(enhanced.getBetSizeRatio());
        assertNotNull(enhanced.getFrequency());
        assertTrue(enhanced.getDescription().contains("FLOP"));
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testBulkConversion() {
        List<AbstractAction> actions = Arrays.asList(
            AbstractAction.FOLD,
            AbstractAction.CHECK_OR_CALL,
            AbstractAction.BET_OR_RAISE_30,
            AbstractAction.BET_OR_RAISE_100
        );
        
        // Convert to templates
        List<ActionTemplate> templates = ActionBridge.toActionTemplateList(actions);
        assertEquals(actions.size(), templates.size());
        
        // Convert back to actions
        List<AbstractAction> convertedActions = ActionBridge.toAbstractActionList(templates);
        assertEquals(actions.size(), convertedActions.size());
        
        // Verify compatibility
        for (int i = 0; i < actions.size(); i++) {
            assertTrue(ActionBridge.isCompatible(actions.get(i), templates.get(i)));
        }
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testActionTypeMapping() {
        // Test all ActionType mappings
        assertEquals(ActionType.FOLD, ActionBridge.getActionType(AbstractAction.FOLD));
        assertEquals(ActionType.CALL, ActionBridge.getActionType(AbstractAction.CHECK_OR_CALL));
        assertEquals(ActionType.BET, ActionBridge.getActionType(AbstractAction.BET_OR_RAISE_30));
        assertEquals(ActionType.RAISE, ActionBridge.getActionType(AbstractAction.BET_OR_RAISE_200));
        
        // Test reverse mapping
        List<AbstractAction> foldActions = ActionBridge.getAbstractActionsForType(ActionType.FOLD);
        assertTrue(foldActions.contains(AbstractAction.FOLD));
        
        List<AbstractAction> betActions = ActionBridge.getAbstractActionsForType(ActionType.BET);
        assertTrue(betActions.contains(AbstractAction.BET_OR_RAISE_30));
        assertTrue(betActions.contains(AbstractAction.BET_OR_RAISE_60));
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testCompatibilityChecking() {
        AbstractAction foldAction = AbstractAction.FOLD;
        ActionTemplate foldTemplate = ActionTemplate.builder()
            .actionType(ActionType.FOLD)
            .build();
        assertTrue(ActionBridge.isCompatible(foldAction, foldTemplate));
        
        AbstractAction betAction = AbstractAction.BET_OR_RAISE_30;
        ActionTemplate callTemplate = ActionTemplate.builder()
            .actionType(ActionType.CALL)
            .build();
        assertFalse(ActionBridge.isCompatible(betAction, callTemplate));
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testCachePerformance() {
        AbstractAction action = AbstractAction.BET_OR_RAISE_100;
        
        // First conversion (cache miss)
        long startTime = System.nanoTime();
        ActionTemplate template1 = ActionBridge.toActionTemplate(action);
        long firstConversionTime = System.nanoTime() - startTime;
        
        // Second conversion (cache hit)
        startTime = System.nanoTime();
        ActionTemplate template2 = ActionBridge.toActionTemplate(action);
        long secondConversionTime = System.nanoTime() - startTime;
        
        // Cache hit should be faster
        assertTrue(secondConversionTime <= firstConversionTime,
            "Cached conversion should be faster or equal");
        
        // Should return same instance from cache
        assertSame(template1, template2, "Cache should return same instance");
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testNullHandling() {
        // Test null AbstractAction
        ActionTemplate template = ActionBridge.toActionTemplate(null);
        assertNull(template);
        
        // Test null ActionTemplate
        AbstractAction action = ActionBridge.toAbstractAction(null);
        assertEquals(AbstractAction.FOLD, action); // Safe default
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testEdgeCases() {
        // Test very large bet size
        ActionTemplate largeBetTemplate = ActionTemplate.builder()
            .actionType(ActionType.RAISE)
            .betSizeRatio(10.0)
            .build();
        AbstractAction largeAction = ActionBridge.toAbstractAction(largeBetTemplate);
        assertEquals(AbstractAction.BET_OR_RAISE_500, largeAction);
        
        // Test very small bet size
        ActionTemplate smallBetTemplate = ActionTemplate.builder()
            .actionType(ActionType.BET)
            .betSizeRatio(0.1)
            .build();
        AbstractAction smallAction = ActionBridge.toAbstractAction(smallBetTemplate);
        assertEquals(AbstractAction.BET_OR_RAISE_30, smallAction);
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testCacheStatistics() {
        // Perform some conversions
        for (int i = 0; i < 5; i++) {
            ActionBridge.toActionTemplate(AbstractAction.BET_OR_RAISE_60);
        }
        
        String stats = ActionBridge.getCacheStatistics();
        assertNotNull(stats);
        assertTrue(stats.contains("Cache Stats"));
        assertTrue(stats.contains("abstract->template"));
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testThreadSafety() throws InterruptedException {
        final int NUM_THREADS = 4;
        final int OPERATIONS_PER_THREAD = 100;
        Thread[] threads = new Thread[NUM_THREADS];
        final boolean[] success = {true};
        
        for (int i = 0; i < NUM_THREADS; i++) {
            final int threadId = i;
            threads[i] = new Thread(() -> {
                try {
                    for (int j = 0; j < OPERATIONS_PER_THREAD; j++) {
                        AbstractAction action = AbstractAction.values()[j % AbstractAction.values().length];
                        ActionTemplate template = ActionBridge.toActionTemplate(action);
                        AbstractAction converted = ActionBridge.toAbstractAction(template);
                        
                        if (!ActionBridge.isCompatible(action, template)) {
                            success[0] = false;
                            break;
                        }
                    }
                } catch (Exception e) {
                    success[0] = false;
                }
            });
        }
        
        for (Thread thread : threads) {
            thread.start();
        }
        
        for (Thread thread : threads) {
            thread.join();
        }
        
        assertTrue(success[0], "All threads should complete successfully");
    }
}
