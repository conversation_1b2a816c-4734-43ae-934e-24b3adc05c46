package com.example.texasholdem.abstraction;

import com.example.texasholdem.model.*;
import com.example.texasholdem.service.HandEvaluator;
import com.example.texasholdem.strategy.model.*;
import com.example.texasholdem.strategy.train.InfosetStoreKey;
import com.example.texasholdem.abstraction.AbstractionDataStructures.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Test suite for UnifiedAbstractionPipeline
 * 
 * Validates the integration of all abstraction engines and ensures
 * backward compatibility with existing InfosetStoreKey functionality.
 */
public class UnifiedAbstractionPipelineTest {

    @Mock
    private HandEvaluator mockHandEvaluator;
    
    private InfosetStore infosetStore;
    private ThreadSafeInfosetStore threadSafeInfosetStore;
    private AbstractionConfig config;
    private UnifiedAbstractionPipeline pipeline;
    private AbstractionIntegrationService integrationService;
    
    private static final int NUM_PLAYERS = 6;
    private static final int NUM_ACTIONS = 6;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // Create stores
        infosetStore = new InfosetStore(NUM_PLAYERS, NUM_ACTIONS);
        threadSafeInfosetStore = new ThreadSafeInfosetStore(NUM_PLAYERS, NUM_ACTIONS);
        
        // Create test configuration
        config = AbstractionConfig.testConfig();
        
        // Create pipeline
        pipeline = new UnifiedAbstractionPipeline(config, infosetStore, 
            threadSafeInfosetStore, mockHandEvaluator);
        
        // Create integration service
        integrationService = new AbstractionIntegrationService();
        integrationService.initialize(config, infosetStore, threadSafeInfosetStore, mockHandEvaluator);
    }

    @Test
    @Timeout(value = 30, unit = TimeUnit.SECONDS)
    void testPipelineInitialization() {
        assertNotNull(pipeline);
        assertNotNull(integrationService);
        assertTrue(integrationService.isEnhancedAbstractionEnabled());
    }

    @Test
    @Timeout(value = 30, unit = TimeUnit.SECONDS)
    void testBackwardCompatibilityWithInfosetStoreKey() {
        // Create test infoset
        Infoset testInfoset = createTestInfoset();
        
        // Get abstraction using original method
        int originalAbstraction = InfosetStoreKey.abstractInfoset(testInfoset);
        
        // Get abstraction using new pipeline with legacy fallback
        config = config.toBuilder()
            .enableComprehensiveAbstraction(false)
            .enableLegacyFallback(true)
            .build();
        
        pipeline = new UnifiedAbstractionPipeline(config, infosetStore, 
            threadSafeInfosetStore, mockHandEvaluator);
        
        int pipelineAbstraction = pipeline.abstractInfoset(testInfoset);
        
        // Should produce same result when using legacy fallback
        assertEquals(originalAbstraction, pipelineAbstraction);
    }

    @Test
    @Timeout(value = 30, unit = TimeUnit.SECONDS)
    void testComprehensiveAbstractionCreation() {
        // Create test infoset
        Infoset testInfoset = createTestInfoset();
        
        // Test comprehensive abstraction
        int abstractionId = pipeline.abstractInfoset(testInfoset);
        
        // Should return valid abstraction ID
        assertTrue(abstractionId >= 0);
        
        // Test caching - second call should be faster
        long startTime = System.nanoTime();
        int cachedAbstractionId = pipeline.abstractInfoset(testInfoset);
        long endTime = System.nanoTime();
        
        assertEquals(abstractionId, cachedAbstractionId);
        
        // Verify cache hit
        Map<String, Object> stats = pipeline.getPerformanceStatistics();
        assertTrue((Long) stats.get("cacheHits") > 0);
    }

    @Test
    @Timeout(value = 30, unit = TimeUnit.SECONDS)
    void testIntegrationServiceAbstraction() {
        // Create test infoset
        Infoset testInfoset = createTestInfoset();
        
        // Test integration service abstraction
        int abstractionId = integrationService.abstractInfoset(testInfoset);
        
        // Should return valid abstraction ID
        assertTrue(abstractionId >= 0);
        
        // Test performance statistics
        String stats = integrationService.getPerformanceStatistics();
        assertNotNull(stats);
        assertTrue(stats.contains("total="));
    }

    @Test
    @Timeout(value = 30, unit = TimeUnit.SECONDS)
    void testThreadSafetyWithConcurrentAccess() throws InterruptedException {
        final int NUM_THREADS = 4;
        final int OPERATIONS_PER_THREAD = 100;
        final List<Exception> exceptions = Collections.synchronizedList(new ArrayList<>());
        final List<Thread> threads = new ArrayList<>();
        
        // Create test infosets
        List<Infoset> testInfosets = createMultipleTestInfosets(10);
        
        // Create threads that perform concurrent abstractions
        for (int i = 0; i < NUM_THREADS; i++) {
            Thread thread = new Thread(() -> {
                try {
                    Random random = new Random();
                    for (int j = 0; j < OPERATIONS_PER_THREAD; j++) {
                        Infoset infoset = testInfosets.get(random.nextInt(testInfosets.size()));
                        int abstractionId = pipeline.abstractInfoset(infoset);
                        assertTrue(abstractionId >= 0);
                    }
                } catch (Exception e) {
                    exceptions.add(e);
                }
            });
            threads.add(thread);
            thread.start();
        }
        
        // Wait for all threads to complete
        for (Thread thread : threads) {
            thread.join();
        }
        
        // Verify no exceptions occurred
        if (!exceptions.isEmpty()) {
            fail("Concurrent access failed: " + exceptions.get(0).getMessage());
        }
        
        // Verify performance statistics
        Map<String, Object> stats = pipeline.getPerformanceStatistics();
        long totalCalls = (Long) stats.get("totalAbstractionCalls");
        assertEquals(NUM_THREADS * OPERATIONS_PER_THREAD, totalCalls);
    }

    @Test
    @Timeout(value = 30, unit = TimeUnit.SECONDS)
    void testPerformanceOptimization() {
        // Create test infoset
        Infoset testInfoset = createTestInfoset();
        
        // Measure performance with caching
        long startTime = System.nanoTime();
        for (int i = 0; i < 1000; i++) {
            pipeline.abstractInfoset(testInfoset);
        }
        long endTime = System.nanoTime();
        long timeWithCaching = endTime - startTime;
        
        // Verify cache effectiveness
        Map<String, Object> stats = pipeline.getPerformanceStatistics();
        double cacheHitRate = (Double) stats.get("cacheHitRate");
        
        // Should have high cache hit rate for repeated calls
        assertTrue(cacheHitRate > 0.9, "Cache hit rate should be > 90%, was: " + cacheHitRate);
        
        log.info("Performance test completed: {} ns with {:.2f}% cache hit rate", 
            timeWithCaching, cacheHitRate * 100);
    }

    @Test
    @Timeout(value = 30, unit = TimeUnit.SECONDS)
    void testConfigurationFlexibility() {
        // Test with different configurations
        AbstractionConfig trainingConfig = AbstractionConfig.trainingConfig();
        AbstractionConfig realtimeConfig = AbstractionConfig.realtimeConfig();
        
        // Create pipelines with different configs
        UnifiedAbstractionPipeline trainingPipeline = new UnifiedAbstractionPipeline(
            trainingConfig, infosetStore, threadSafeInfosetStore, mockHandEvaluator);
        
        UnifiedAbstractionPipeline realtimePipeline = new UnifiedAbstractionPipeline(
            realtimeConfig, infosetStore, threadSafeInfosetStore, mockHandEvaluator);
        
        // Test that both work
        Infoset testInfoset = createTestInfoset();
        
        int trainingResult = trainingPipeline.abstractInfoset(testInfoset);
        int realtimeResult = realtimePipeline.abstractInfoset(testInfoset);
        
        assertTrue(trainingResult >= 0);
        assertTrue(realtimeResult >= 0);
        
        // Cleanup
        trainingPipeline.cleanup();
        realtimePipeline.cleanup();
    }

    @Test
    @Timeout(value = 30, unit = TimeUnit.SECONDS)
    void testResourceCleanup() {
        // Test cleanup doesn't throw exceptions
        assertDoesNotThrow(() -> {
            pipeline.cleanup();
            integrationService.cleanup();
        });
        
        // Test that cleanup can be called multiple times
        assertDoesNotThrow(() -> {
            pipeline.cleanup();
            integrationService.cleanup();
        });
    }

    // ========================================
    // HELPER METHODS
    // ========================================

    private Infoset createTestInfoset() {
        List<Card> holeCards = Arrays.asList(
            new Card(Card.Rank.ACE, Card.Suit.SPADES),
            new Card(Card.Rank.KING, Card.Suit.HEARTS)
        );
        
        List<Card> communityCards = Arrays.asList(
            new Card(Card.Rank.QUEEN, Card.Suit.DIAMONDS),
            new Card(Card.Rank.JACK, Card.Suit.CLUBS),
            new Card(Card.Rank.TEN, Card.Suit.SPADES)
        );
        
        List<ActionTrace> history = Arrays.asList(
            new ActionTrace(0, AbstractAction.BET_OR_RAISE_30, 0, 0, 0),
            new ActionTrace(1, AbstractAction.CHECK_OR_CALL, 0, 1, 1)
        );
        
        return Infoset.of(holeCards, communityCards, BettingRound.FLOP, 0, 30, history);
    }

    private List<Infoset> createMultipleTestInfosets(int count) {
        List<Infoset> infosets = new ArrayList<>();
        Random random = new Random(42); // Fixed seed for reproducibility
        
        for (int i = 0; i < count; i++) {
            List<Card> holeCards = generateRandomCards(random, 2);
            List<Card> communityCards = generateRandomCards(random, 3);
            List<ActionTrace> history = generateRandomHistory(random);
            
            infosets.add(Infoset.of(holeCards, communityCards, BettingRound.FLOP, 
                i % NUM_PLAYERS, random.nextInt(100), history));
        }
        
        return infosets;
    }

    private List<Card> generateRandomCards(Random random, int count) {
        List<Card> cards = new ArrayList<>();
        Set<String> usedCards = new HashSet<>();
        
        while (cards.size() < count) {
            Card.Rank rank = Card.Rank.values()[random.nextInt(Card.Rank.values().length)];
            Card.Suit suit = Card.Suit.values()[random.nextInt(Card.Suit.values().length)];
            String cardKey = rank + "_" + suit;
            
            if (!usedCards.contains(cardKey)) {
                cards.add(new Card(rank, suit));
                usedCards.add(cardKey);
            }
        }
        
        return cards;
    }

    private List<ActionTrace> generateRandomHistory(Random random) {
        List<ActionTrace> history = new ArrayList<>();
        AbstractAction[] actions = {AbstractAction.CHECK_OR_CALL, AbstractAction.BET_OR_RAISE_30, 
            AbstractAction.BET_OR_RAISE_60, AbstractAction.FOLD};
        
        int historyLength = random.nextInt(5) + 1;
        for (int i = 0; i < historyLength; i++) {
            AbstractAction action = actions[random.nextInt(actions.length)];
            history.add(new ActionTrace(i % NUM_PLAYERS, action, 0, i, i % NUM_PLAYERS));
        }
        
        return history;
    }
}
