package com.example.texasholdem.abstraction;

import com.example.texasholdem.model.*;
import com.example.texasholdem.service.HandEvaluator;
import com.example.texasholdem.strategy.model.ActionTrace;
import com.example.texasholdem.strategy.model.InfosetStore;
import com.example.texasholdem.strategy.model.InfosetValue;
import com.example.texasholdem.strategy.SearchGameState;
import com.example.texasholdem.abstraction.AbstractionDataStructures.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Comprehensive test suite for the Abstraction System
 * 
 * Tests all three abstraction engines and their integration:
 * - Information Abstraction Engine
 * - Action Abstraction Engine  
 * - Card Abstraction Engine
 * - Abstraction Manager coordination
 */
public class AbstractionSystemTest {
    
    @Mock
    private InfosetStore mockInfosetStore;
    
    private AbstractionConfig testConfig;
    private InformationAbstractionEngine informationEngine;
    private ActionAbstractionEngine actionEngine;
    private CardAbstractionEngine cardEngine;
    private AbstractionManager abstractionManager;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // Configure mock InfosetStore
        when(mockInfosetStore.getNumPlayers()).thenReturn(3);
        when(mockInfosetStore.getNumActions()).thenReturn(6);

        // Note: HandEvaluator is now used as static methods, so no mock configuration needed
        
        // Create test configuration
        testConfig = AbstractionConfig.testConfig();
        
        // Initialize engines
        informationEngine = new InformationAbstractionEngine(3, 6, testConfig);
        actionEngine = new ActionAbstractionEngine(testConfig);
        cardEngine = new CardAbstractionEngine(testConfig, null); // HandEvaluator now used as static methods
        abstractionManager = new AbstractionManager(testConfig, mockInfosetStore, null); // HandEvaluator now used as static methods
    }
    
    @Test
    @Timeout(value = 5)
    void testInformationAbstractionEngine() {
        System.out.println("\n🧪 Testing Information Abstraction Engine");
        
        // Create test game context
        GameContext context = createTestGameContext();
        
        // Test abstraction creation
        int abstractionId1 = informationEngine.createAbstraction(12345L, context);
        int abstractionId2 = informationEngine.createAbstraction(12346L, context);
        int abstractionId3 = informationEngine.createAbstraction(12345L, context); // Same as first
        
        assertNotNull(abstractionId1);
        assertNotNull(abstractionId2);
        assertEquals(abstractionId1, abstractionId3, "Same infoset should get same abstraction");
        
        // Test abstraction retrieval
        Integer retrieved = informationEngine.getAbstraction(12345L);
        assertEquals(abstractionId1, retrieved, "Should retrieve correct abstraction");
        
        // Test statistics
        AbstractionStatistics stats = informationEngine.getStatistics();
        assertNotNull(stats);
        assertTrue(stats.getTotalInfosets() >= 2, "Should have at least 2 infosets");
        assertTrue(stats.getTotalAbstractions() >= 1, "Should have at least 1 abstraction");
        
        System.out.println("Information abstraction stats: " + stats.getSummary());
        System.out.println("✅ Information abstraction engine test passed");
    }
    
    @Test
    @Timeout(value = 5)
    void testActionAbstractionEngine() {
        System.out.println("\n🧪 Testing Action Abstraction Engine");
        
        // Test different action types
        ActionInstance foldAction = createActionInstance(Action.FOLD, 0);
        ActionInstance callAction = createActionInstance(Action.CALL, 100);
        ActionInstance betAction = createActionInstance(Action.BET, 200);
        ActionInstance raiseAction = createActionInstance(Action.RAISE, 500);
        
        // Test action abstraction
        ActionTemplate foldTemplate = actionEngine.abstractAction(foldAction, BettingRound.FLOP, 400);
        ActionTemplate callTemplate = actionEngine.abstractAction(callAction, BettingRound.FLOP, 400);
        ActionTemplate betTemplate = actionEngine.abstractAction(betAction, BettingRound.FLOP, 400);
        ActionTemplate raiseTemplate = actionEngine.abstractAction(raiseAction, BettingRound.FLOP, 400);
        
        // Verify action types
        assertEquals(ActionType.FOLD, foldTemplate.getActionType());
        assertEquals(ActionType.CALL, callTemplate.getActionType());
        assertEquals(ActionType.BET, betTemplate.getActionType());
        assertEquals(ActionType.RAISE, raiseTemplate.getActionType());
        
        // Verify bet sizing abstraction
        assertNull(foldTemplate.getBetSizeRatio());
        assertNull(callTemplate.getBetSizeRatio());
        assertNotNull(betTemplate.getBetSizeRatio());
        assertNotNull(raiseTemplate.getBetSizeRatio());
        
        // Test available actions
        GameContext context = createTestGameContext();
        List<ActionTemplate> availableActions = actionEngine.getAvailableActions(BettingRound.FLOP, context);
        assertFalse(availableActions.isEmpty(), "Should have available actions");
        
        // Test statistics
        ActionAbstractionStatistics stats = actionEngine.getStatistics();
        assertNotNull(stats);
        assertTrue(stats.getTotalClusters() > 0, "Should have action clusters");
        
        System.out.println("Action abstraction stats: " + stats.getSummary());
        System.out.println("✅ Action abstraction engine test passed");
    }
    
    @Test
    @Timeout(value = 5)
    void testCardAbstractionEngine() {
        System.out.println("\n🧪 Testing Card Abstraction Engine");
        
        // Create test hands
        List<Card> holeCards1 = Arrays.asList(new Card("Ah"), new Card("Ks"));
        List<Card> holeCards2 = Arrays.asList(new Card("2c"), new Card("3d"));
        List<Card> communityCards = Arrays.asList(
            new Card("Qd"), new Card("Jc"), new Card("Th")
        );
        
        // Test hand abstraction
        HandBucket bucket1 = cardEngine.abstractHand(holeCards1, communityCards, BettingRound.FLOP);
        HandBucket bucket2 = cardEngine.abstractHand(holeCards2, communityCards, BettingRound.FLOP);
        HandBucket bucket3 = cardEngine.abstractHand(holeCards1, communityCards, BettingRound.FLOP); // Same as first
        
        assertNotNull(bucket1);
        assertNotNull(bucket2);
        assertNotNull(bucket3);
        
        // Strong hand and weak hand should be in different buckets
        assertNotEquals(bucket1.getBucketId(), bucket2.getBucketId(), 
                       "Strong and weak hands should be in different buckets");
        
        // Same hand should get same bucket
        assertEquals(bucket1.getBucketId(), bucket3.getBucketId(), 
                    "Same hand should get same bucket");
        
        // Test bucket properties
        assertNotNull(bucket1.getBucketName());
        assertNotNull(bucket1.getMinStrength());
        assertNotNull(bucket1.getMaxStrength());
        
        // Test statistics
        CardAbstractionStatistics stats = cardEngine.getStatistics();
        assertNotNull(stats);
        assertTrue(stats.getTotalBuckets() > 0, "Should have hand buckets");
        
        System.out.println("Card abstraction stats: " + stats.getSummary());
        System.out.println("✅ Card abstraction engine test passed");
    }
    
    @Test
    @Timeout(value = 10)
    void testAbstractionManagerIntegration() {
        System.out.println("\n🧪 Testing Abstraction Manager Integration");
        
        // Create test game state
        SearchGameState gameState = createTestSearchGameState();
        
        // Test comprehensive abstraction creation
        ComprehensiveAbstraction abstraction = abstractionManager.createAbstraction(gameState);
        
        assertNotNull(abstraction, "Comprehensive abstraction should not be null");
        assertNotNull(abstraction.getInformationAbstractionId(), "Information abstraction should not be null");
        assertNotNull(abstraction.getActionTemplate(), "Action template should not be null");
        assertNotNull(abstraction.getHandBucket(), "Hand bucket should not be null");
        assertNotNull(abstraction.getGameContext(), "Game context should not be null");
        assertTrue(abstraction.getCreationTime() >= 0, "Creation time should be non-negative");
        
        // Test multiple abstractions for consistency
        ComprehensiveAbstraction abstraction2 = abstractionManager.createAbstraction(gameState);
        assertEquals(abstraction.getInformationAbstractionId(), abstraction2.getInformationAbstractionId(),
                    "Same game state should produce same information abstraction");
        
        // Test statistics
        AbstractionManagerStatistics stats = abstractionManager.getStatistics();
        assertNotNull(stats);
        assertNotNull(stats.getInformationStats());
        assertNotNull(stats.getActionStats());
        assertNotNull(stats.getCardStats());
        
        System.out.println("Abstraction manager stats: " + stats.getSummary());
        System.out.println("✅ Abstraction manager integration test passed");
    }
    
    @Test
    @Timeout(value = 5)
    void testAbstractionFeatures() {
        System.out.println("\n🧪 Testing Abstraction Features");
        
        // Test feature creation and similarity
        AbstractionFeatures features1 = AbstractionFeatures.builder()
            .handStrength(0.8)
            .handPotential(0.3)
            .handCategory(HandCategory.PAIR)
            .bettingPattern(BettingPattern.AGGRESSIVE)
            .aggressionLevel(0.7)
            .position(2)
            .bettingRound(BettingRound.FLOP)
            .numActivePlayers(3)
            .build();
        
        AbstractionFeatures features2 = AbstractionFeatures.builder()
            .handStrength(0.82) // Similar strength
            .handPotential(0.28) // Similar potential
            .handCategory(HandCategory.PAIR)
            .bettingPattern(BettingPattern.AGGRESSIVE)
            .aggressionLevel(0.72)
            .position(2)
            .bettingRound(BettingRound.FLOP)
            .numActivePlayers(3)
            .build();
        
        AbstractionFeatures features3 = AbstractionFeatures.builder()
            .handStrength(0.2) // Very different
            .handPotential(0.8)
            .handCategory(HandCategory.HIGH_CARD)
            .bettingPattern(BettingPattern.PASSIVE)
            .aggressionLevel(0.1)
            .position(5)
            .bettingRound(BettingRound.RIVER)
            .numActivePlayers(2)
            .build();
        
        // Test feature validation
        assertTrue(features1.isValid(), "Features1 should be valid");
        assertTrue(features2.isValid(), "Features2 should be valid");
        assertTrue(features3.isValid(), "Features3 should be valid");
        
        // Test similarity calculation
        double similarity12 = features1.calculateSimilarity(features2);
        double similarity13 = features1.calculateSimilarity(features3);
        
        assertTrue(similarity12 > similarity13, 
                  "Similar features should have higher similarity than different features");
        assertTrue(similarity12 > 0.7, "Similar features should have high similarity");
        assertTrue(similarity13 < 0.5, "Different features should have low similarity");
        
        // Test feature vectors
        double[] vector1 = features1.toFeatureVector();
        AbstractionFeatures reconstructed = AbstractionFeatures.fromFeatureVector(vector1);
        
        assertEquals(features1.getHandStrength(), reconstructed.getHandStrength(), 0.01);
        assertEquals(features1.getHandCategory(), reconstructed.getHandCategory());
        assertEquals(features1.getBettingPattern(), reconstructed.getBettingPattern());
        
        System.out.println("Feature similarity (similar): " + similarity12);
        System.out.println("Feature similarity (different): " + similarity13);
        System.out.println("✅ Abstraction features test passed");
    }
    
    @Test
    @Timeout(value = 5)
    void testAbstractionQualityMetrics() {
        System.out.println("\n🧪 Testing Abstraction Quality Metrics");
        
        // Test quality metrics
        AbstractionQualityMetrics metrics = new AbstractionQualityMetrics();
        
        // Update metrics with sample data
        metrics.updateMetrics(0.8, 2.5);
        metrics.updateMetrics(0.7, 3.0);
        metrics.updateMetrics(0.9, 2.0);
        
        assertEquals(3, metrics.getSampleCount());
        assertEquals(0.8, metrics.getAverageStrategicValue(), 0.01);
        assertEquals(2.5, metrics.getAverageCompressionRatio(), 0.01);
        
        // Test validation
        ValidationResult validation = informationEngine.validateAbstractions();
        assertNotNull(validation);
        
        System.out.println("Quality metrics: samples=" + metrics.getSampleCount() + 
                          ", strategic=" + metrics.getAverageStrategicValue() +
                          ", compression=" + metrics.getAverageCompressionRatio());
        System.out.println("Validation: " + validation.getSummary());
        System.out.println("✅ Abstraction quality metrics test passed");
    }
    
    @Test
    @Timeout(value = 5)
    void testAbstractionPerformance() {
        System.out.println("\n🧪 Testing Abstraction Performance");
        
        GameContext context = createTestGameContext();
        SearchGameState gameState = createTestSearchGameState();
        
        // Measure performance of abstraction creation
        long startTime = System.nanoTime();
        
        for (int i = 0; i < 100; i++) {
            // Test information abstraction performance
            informationEngine.createAbstraction(i, context);
            
            // Test action abstraction performance
            ActionInstance action = createActionInstance(Action.BET, 200);
            actionEngine.abstractAction(action, BettingRound.FLOP, 400);
            
            // Test card abstraction performance
            List<Card> holeCards = Arrays.asList(new Card("Ah"), new Card("Ks"));
            List<Card> communityCards = Arrays.asList(new Card("Qd"), new Card("Jc"), new Card("Th"));
            cardEngine.abstractHand(holeCards, communityCards, BettingRound.FLOP);
        }
        
        long elapsedTime = System.nanoTime() - startTime;
        double avgTimePerAbstraction = elapsedTime / (100.0 * 3); // 100 iterations, 3 abstractions each
        
        // Performance should be reasonable (less than 1ms per abstraction on average)
        assertTrue(avgTimePerAbstraction < 1_000_000, // 1ms in nanoseconds
                  "Average abstraction time should be less than 1ms, was: " + 
                  (avgTimePerAbstraction / 1_000_000) + "ms");
        
        System.out.println("Average abstraction time: " + String.format("%.2f", avgTimePerAbstraction / 1000) + " μs");
        System.out.println("✅ Abstraction performance test passed");
    }
    
    // Helper methods
    
    private GameContext createTestGameContext() {
        return GameContext.builder()
            .holeCards(Arrays.asList(new Card("Ah"), new Card("Ks")))
            .communityCards(Arrays.asList(new Card("Qd"), new Card("Jc"), new Card("Th")))
            .actionHistory(new ArrayList<>())
            .position(2)
            .bettingRound(BettingRound.FLOP)
            .numActivePlayers(3)
            .potSize(400)
            .currentBet(100)
            .stackSize(2000)
            .build();
    }
    
    private SearchGameState createTestSearchGameState() {
        // Create test players
        List<Player> players = Arrays.asList(
            createTestPlayer(0, "Player1", 2000, Arrays.asList(new Card("Ah"), new Card("Ks"))),
            createTestPlayer(1, "Player2", 1800, Arrays.asList(new Card("Qc"), new Card("Jd"))),
            createTestPlayer(2, "Player3", 2200, Arrays.asList(new Card("Tc"), new Card("9s")))
        );

        List<Card> communityCards = Arrays.asList(new Card("Qd"), new Card("Jc"), new Card("Th"));
        List<ActionTrace> actionHistory = new ArrayList<>();

        return SearchGameState.createSnapshot(
            communityCards,
            BettingRound.FLOP,
            400, // potSize
            100, // currentRoundBetAmount
            actionHistory,
            players,
            0, // currentPlayerIndex
            1  // searchDepth
        );
    }
    
    private SearchGameState.PlayerSnapshot createTestPlayerSnapshot(int playerIndex, String name, int chips, List<Card> holeCards) {
        // Create a temporary Player object to use PlayerSnapshot constructor
        Player tempPlayer = new Player(name, playerIndex, chips);
        for (Card card : holeCards) {
            tempPlayer.addCardToHand(card);
        }
        return new SearchGameState.PlayerSnapshot(tempPlayer);
    }

    private Player createTestPlayer(int position, String name, int chips, List<Card> holeCards) {
        Player player = new Player(name, position, chips);
        for (Card card : holeCards) {
            player.addCardToHand(card);
        }
        return player;
    }
    
    private ActionInstance createActionInstance(Action action, int amount) {
        return new ActionInstance(action, amount);
    }
}
