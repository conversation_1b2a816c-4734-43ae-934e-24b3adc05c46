package com.example.texasholdem.abstraction;

import com.example.texasholdem.model.*;
import com.example.texasholdem.service.GameService;
import com.example.texasholdem.service.HandEvaluator;
import com.example.texasholdem.strategy.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Test to verify compilation fixes for UnifiedAbstractionPipeline and AbstractionIntegrationService
 */
public class CompilationFixTest {

    @Mock
    private HandEvaluator mockHandEvaluator;
    
    @Mock
    private GameService mockGameService;
    
    @Mock
    private Player mockPlayer;
    
    private InfosetStore infosetStore;
    private ThreadSafeInfosetStore threadSafeInfosetStore;
    private AbstractionConfig config;
    private UnifiedAbstractionPipeline pipeline;
    private AbstractionIntegrationService integrationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // Create stores
        infosetStore = new InfosetStore(6, 6);
        threadSafeInfosetStore = new ThreadSafeInfosetStore(6, 6);
        
        // Create config
        config = AbstractionConfig.builder()
            .enableComprehensiveAbstraction(true)
            .enablePerformanceOptimization(true)
            .cacheSize(1000)
            .build();
        
        // Mock GameService methods
        when(mockGameService.getPlayers()).thenReturn(Arrays.asList(mockPlayer));
        when(mockGameService.getCommunityCards()).thenReturn(new ArrayList<>());
        when(mockGameService.getCurrentBettingRound()).thenReturn(BettingRound.PREFLOP);
        when(mockGameService.getCurrentRoundBetAmount()).thenReturn(0);
        when(mockGameService.getTotalPotAmount()).thenReturn(100);
        
        // Mock Player methods
        when(mockPlayer.getPlayerIndex()).thenReturn(0);
        when(mockPlayer.getHand()).thenReturn(Arrays.asList(
            new Card(Card.Rank.ACE, Card.Suit.SPADES),
            new Card(Card.Rank.KING, Card.Suit.HEARTS)
        ));
        when(mockPlayer.getChips()).thenReturn(1000);
        when(mockPlayer.isFolded()).thenReturn(false);
        
        // Create pipeline
        pipeline = new UnifiedAbstractionPipeline(config, infosetStore, 
            threadSafeInfosetStore, mockHandEvaluator);
        
        // Create integration service
        integrationService = new AbstractionIntegrationService();
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testUnifiedAbstractionPipelineCompilation() {
        // Test that UnifiedAbstractionPipeline compiles and basic methods work
        assertNotNull(pipeline);
        
        // Test abstractInfoset method
        Infoset testInfoset = createTestInfoset();
        int abstractionId = pipeline.abstractInfoset(testInfoset);
        assertTrue(abstractionId >= 0);
        
        // Test performance statistics
        Map<String, Object> stats = pipeline.getPerformanceStatistics();
        assertNotNull(stats);
        assertTrue(stats.containsKey("totalAbstractionCalls"));
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testAbstractionIntegrationServiceCompilation() {
        // Test that AbstractionIntegrationService compiles and basic methods work
        assertNotNull(integrationService);
        
        // Test initialization
        integrationService.initialize(config, infosetStore, threadSafeInfosetStore, mockHandEvaluator);
        
        // Test abstractInfoset method
        Infoset testInfoset = createTestInfoset();
        int abstractionId = integrationService.abstractInfoset(testInfoset);
        assertTrue(abstractionId >= 0);
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testAdvancedAbstractionMethods() {
        // Test the new abstraction methods that were added
        List<ActionTrace> history = new ArrayList<>();
        
        // Test getActionAbstraction
        ActionTemplate actionTemplate = pipeline.getActionAbstraction(mockPlayer, mockGameService, history);
        assertNotNull(actionTemplate);
        
        // Test getCardAbstraction
        HandBucket handBucket = pipeline.getCardAbstraction(
            mockPlayer.getHand(), 
            mockGameService.getCommunityCards(), 
            BettingRound.PREFLOP
        );
        assertNotNull(handBucket);
        
        // Test getComprehensiveAbstraction
        ComprehensiveAbstraction comprehensive = pipeline.getComprehensiveAbstraction(
            mockPlayer, mockGameService, history);
        assertNotNull(comprehensive);
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testGameServiceIntegration() {
        // Test that the fixed GameService method calls work
        List<ActionTrace> history = new ArrayList<>();
        
        // This should not throw exceptions due to missing methods
        assertDoesNotThrow(() -> {
            pipeline.getActionAbstraction(mockPlayer, mockGameService, history);
        });
        
        assertDoesNotThrow(() -> {
            pipeline.getComprehensiveAbstraction(mockPlayer, mockGameService, history);
        });
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testActionBridgeIntegration() {
        // Test that ActionBridge integration works
        AbstractAction action = AbstractAction.BET_OR_RAISE_60;
        ActionTemplate template = ActionBridge.toActionTemplate(action);
        assertNotNull(template);
        
        AbstractAction convertedBack = ActionBridge.toAbstractAction(template);
        assertNotNull(convertedBack);
        
        // Test compatibility
        assertTrue(ActionBridge.isCompatible(action, template));
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testCacheStatistics() {
        // Test that cache statistics work
        String pipelineStats = pipeline.getCacheStatistics();
        assertNotNull(pipelineStats);
        assertTrue(pipelineStats.contains("Cache Stats"));
        
        String bridgeStats = ActionBridge.getCacheStatistics();
        assertNotNull(bridgeStats);
        assertTrue(bridgeStats.contains("Cache Stats"));
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testResourceCleanup() {
        // Test that cleanup methods work without throwing exceptions
        assertDoesNotThrow(() -> {
            pipeline.cleanup();
        });
        
        assertDoesNotThrow(() -> {
            integrationService.cleanup();
        });
        
        assertDoesNotThrow(() -> {
            ActionBridge.clearCaches();
        });
    }

    @Test
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testConfigurationCreation() {
        // Test that different configuration types can be created
        AbstractionConfig trainingConfig = AbstractionConfig.trainingConfig();
        assertNotNull(trainingConfig);
        
        AbstractionConfig realtimeConfig = AbstractionConfig.realtimeConfig();
        assertNotNull(realtimeConfig);
        
        AbstractionConfig defaultConfig = AbstractionConfig.defaultConfig();
        assertNotNull(defaultConfig);
        
        // Test builder pattern
        AbstractionConfig customConfig = AbstractionConfig.builder()
            .enableComprehensiveAbstraction(true)
            .cacheSize(5000)
            .threadPoolSize(4)
            .build();
        assertNotNull(customConfig);
        assertEquals(5000, customConfig.getCacheSize());
    }

    // Helper method to create test infoset
    private Infoset createTestInfoset() {
        List<Card> holeCards = Arrays.asList(
            new Card(Card.Rank.ACE, Card.Suit.SPADES),
            new Card(Card.Rank.KING, Card.Suit.HEARTS)
        );
        
        List<Card> communityCards = Arrays.asList(
            new Card(Card.Rank.QUEEN, Card.Suit.DIAMONDS),
            new Card(Card.Rank.JACK, Card.Suit.CLUBS),
            new Card(Card.Rank.TEN, Card.Suit.SPADES)
        );
        
        List<ActionTrace> history = Arrays.asList(
            new ActionTrace(0, AbstractAction.BET_OR_RAISE_30, 0, 0, 0),
            new ActionTrace(1, AbstractAction.CHECK_OR_CALL, 0, 1, 1)
        );
        
        return Infoset.of(holeCards, communityCards, BettingRound.FLOP, 0, 30, history);
    }
}
