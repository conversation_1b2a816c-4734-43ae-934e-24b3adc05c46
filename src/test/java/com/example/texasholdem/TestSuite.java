package com.example.texasholdem;

import com.example.texasholdem.abstraction.AbstractionSystemTest;
import com.example.texasholdem.abstraction.ActionBridgeTest;
import com.example.texasholdem.abstraction.CompilationFixTest;
import com.example.texasholdem.abstraction.UnifiedAbstractionPipelineTest;
import com.example.texasholdem.service.*;

import com.example.texasholdem.service.CardEncodeTest;
import com.example.texasholdem.strategy.BlueprintStrategyTest;
import com.example.texasholdem.strategy.config.ThreadSafetyConfigIntegrationTest;
import com.example.texasholdem.strategy.train.EnhancedParallelMCCFRTrainerThreadSafetyTest;
import com.example.texasholdem.strategy.train.ParallelMCCFRTrainerTest;
import com.example.texasholdem.strategy.train.PerformanceEvaluationTest;
import com.example.texasholdem.strategy.train.PluribusStrategyHotSwapTest;
import com.example.texasholdem.strategy.model.ActionTraceTest;
import com.example.texasholdem.strategy.model.InfosetStorePerformanceTest;
import com.example.texasholdem.strategy.model.InfosetStoreTest;
import com.example.texasholdem.strategy.model.InfosetTest;
import com.example.texasholdem.strategy.model.InfosetValueTest;
import com.example.texasholdem.strategy.model.ThreadSafetyTest;
import com.example.texasholdem.strategy.train.InfosetStoreKeyTest;
import com.example.texasholdem.strategy.train.MCCFRTrainerComplexSidePotTest;
import com.example.texasholdem.strategy.train.MCCFRTrainerTest;
import com.example.texasholdem.strategy.train.MemoryOptimizationTest;
import com.example.texasholdem.strategy.StrategyVersionManagerTest;
import org.junit.platform.suite.api.SelectClasses;
import org.junit.platform.suite.api.Suite;

@Suite
@SelectClasses({
    GameRunnerTest.class,
    GameServiceTest.class,
    GameFlowTest.class,
    CardEncodeTest.class,

    ActionTraceTest.class,
    InfosetStoreTest.class,
    InfosetTest.class,
    InfosetValueTest.class,
    InfosetStoreKeyTest.class,

    BlueprintStrategyTest.class,
    MCCFRTrainerTest.class,
    MCCFRTrainerComplexSidePotTest.class,
    InfosetStoreKeyTest.class,

    MemoryOptimizationTest.class,
    InfosetStorePerformanceTest.class,
    //StrategyVersionManagerTest.class,
    PluribusStrategyHotSwapTest.class,
    AbstractionSystemTest.class,
    ThreadSafetyTest.class,

    ParallelMCCFRTrainerTest.class,
    //PerformanceEvaluationTest.class
    AbstractionSystemTest.class,
    ActionBridgeTest.class,
    CompilationFixTest.class,
    UnifiedAbstractionPipelineTest.class,

    EnhancedParallelMCCFRTrainerThreadSafetyTest.class,
    ThreadSafetyConfigIntegrationTest.class
})
public class TestSuite {
}
