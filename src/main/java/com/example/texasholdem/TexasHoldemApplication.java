package com.example.texasholdem;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import com.example.texasholdem.service.GameRunner;
import com.example.texasholdem.service.GameService;

@SpringBootApplication
public class TexasHoldemApplication {
    public static void main(String[] args) {
        ApplicationContext context = SpringApplication.run(TexasHoldemApplication.class, args);
        
        GameService gameService = context.getBean(GameService.class);
        GameRunner gameRunner = new GameRunner(gameService);
        
        gameRunner.runGames();
    }
} 