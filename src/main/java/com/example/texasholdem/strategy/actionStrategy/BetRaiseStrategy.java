package com.example.texasholdem.strategy.actionStrategy;

import com.example.texasholdem.model.Action;
import com.example.texasholdem.model.ActionInstance;
import com.example.texasholdem.model.BettingRound;
import com.example.texasholdem.model.Card;
import com.example.texasholdem.model.Player;
import com.example.texasholdem.strategy.Strategy;
import com.example.texasholdem.strategy.model.ActionTrace;
import java.util.List;

public class BetRaiseStrategy implements Strategy {
  private static final int RAISE_AMOUNT = 20;

  @Override
  public ActionInstance decideAction(List<Card> holeCards, List<Card> communityCards, BettingRound currentRound,
      Player player, int currentRoundBetAmount, List<ActionTrace> actionHistory, int potSize) {
    return betOrRaise(player, currentRoundBetAmount);
  }

  protected ActionInstance betOrRaise(Player player, int currentRoundBetAmount) {
    int playerChips = player.getChips();

    int onTopAmount = currentRoundBetAmount - player.getCurrentBet();
    int remainingAmount = playerChips - onTopAmount;
    int raiseAmount = remainingAmount < RAISE_AMOUNT ? remainingAmount : RAISE_AMOUNT;

    if (currentRoundBetAmount == 0) {
      Action bet = Action.BET;
      return new ActionInstance(bet, onTopAmount + raiseAmount);
    } else if (playerChips <= onTopAmount) {
      return new ActionInstance(Action.CALL, onTopAmount);
    } else {
      Action raise = Action.RAISE;
      return new ActionInstance(raise, onTopAmount + raiseAmount);
    }
  }

  @Override
  public String toString() {
    return "Bet Raise Strategy";
  }
}