package com.example.texasholdem.strategy.actionStrategy;

import com.example.texasholdem.model.Action;
import com.example.texasholdem.model.ActionInstance;
import com.example.texasholdem.model.BettingRound;
import com.example.texasholdem.model.Card;
import com.example.texasholdem.model.Player;
import com.example.texasholdem.strategy.model.ActionTrace;
import java.util.List;

public class BetFoldStrategy extends BetRaiseStrategy {
  private boolean shouldBet = true;

  @Override
  public ActionInstance decideAction(List<Card> holeCards, List<Card> communityCards, BettingRound currentRound,
      Player player, int currentRoundBetAmount, List<ActionTrace> actionHistory, int potSize) {
    if (shouldBet) {
      shouldBet = false;
      return betOrRaise(player, currentRoundBetAmount);
    } else {
      shouldBet = true;
      return new ActionInstance(Action.FOLD,0);
    }
  }

  @Override
  public String toString() {
    return "Bet Fold Strategy";
  }
}