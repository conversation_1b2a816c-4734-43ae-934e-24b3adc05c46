package com.example.texasholdem.strategy.actionStrategy;

import com.example.texasholdem.model.Action;
import com.example.texasholdem.model.ActionInstance;
import com.example.texasholdem.model.BettingRound;
import com.example.texasholdem.model.Card;
import com.example.texasholdem.model.Player;
import com.example.texasholdem.strategy.Strategy;
import com.example.texasholdem.strategy.model.ActionTrace;
import java.util.List;

public class CallStrategy implements Strategy {
  @Override
  public ActionInstance decideAction(List<Card> holeCards, List<Card> communityCards, BettingRound currentRound,
      Player player, int currentRoundBetAmount, List<ActionTrace> actionHistory, int potSize) {
    return new ActionInstance(Action.CALL, currentRoundBetAmount - player.getCurrentBet());
  }

  @Override
  public String toString() {
    return "Call Strategy";
  }
}
