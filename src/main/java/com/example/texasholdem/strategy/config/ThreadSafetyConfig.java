package com.example.texasholdem.strategy.config;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * Configuration class for thread safety and parallel training settings
 * 
 * Provides comprehensive configuration options for multi-threaded MCCFR training
 * including thread pool management, synchronization settings, and performance tuning.
 */
@Data
@Builder
@Slf4j
public class ThreadSafetyConfig {
    
    // ===== THREAD POOL CONFIGURATION =====
    
    /**
     * Enable parallel training with multiple threads
     */
    @Builder.Default
    private boolean enableParallelTraining = true;
    
    /**
     * Number of worker threads for parallel training
     * Recommended: 4-8 threads for optimal performance
     */
    @Builder.Default
    private int numTrainingThreads = 4;
    
    /**
     * Thread pool core size for abstraction operations
     */
    @Builder.Default
    private int abstractionThreadPoolSize = 4;
    
    /**
     * Maximum thread pool size for peak load handling
     */
    @Builder.Default
    private int maxThreadPoolSize = 8;
    
    /**
     * Thread keep-alive time in milliseconds
     */
    @Builder.Default
    private long threadKeepAliveMs = 60000; // 1 minute
    
    /**
     * Thread pool queue capacity
     */
    @Builder.Default
    private int threadPoolQueueCapacity = 1000;
    
    // ===== SYNCHRONIZATION CONFIGURATION =====
    
    /**
     * Enable fine-grained locking for better concurrency
     */
    @Builder.Default
    private boolean enableFineGrainedLocking = true;
    
    /**
     * Lock timeout in milliseconds for avoiding deadlocks
     */
    @Builder.Default
    private long lockTimeoutMs = 5000; // 5 seconds
    
    /**
     * Enable lock contention monitoring
     */
    @Builder.Default
    private boolean enableLockMonitoring = true;
    
    /**
     * Maximum lock wait time before logging warning
     */
    @Builder.Default
    private long lockWarningThresholdMs = 1000; // 1 second
    
    // ===== PERFORMANCE TUNING =====
    
    /**
     * Enable thread-local caching for performance
     */
    @Builder.Default
    private boolean enableThreadLocalCaching = true;
    
    /**
     * Thread-local cache size per thread
     */
    @Builder.Default
    private int threadLocalCacheSize = 1000;
    
    /**
     * Enable atomic operations optimization
     */
    @Builder.Default
    private boolean enableAtomicOptimization = true;
    
    /**
     * Batch size for atomic operations
     */
    @Builder.Default
    private int atomicBatchSize = 100;
    
    /**
     * Enable memory optimization for concurrent access
     */
    @Builder.Default
    private boolean enableMemoryOptimization = true;
    
    /**
     * Memory threshold for triggering optimization (in MB)
     */
    @Builder.Default
    private long memoryOptimizationThresholdMB = 4096; // 4GB
    
    // ===== MONITORING AND DEBUGGING =====
    
    /**
     * Enable comprehensive thread safety monitoring
     */
    @Builder.Default
    private boolean enableThreadSafetyMonitoring = true;
    
    /**
     * Monitoring interval in milliseconds
     */
    @Builder.Default
    private long monitoringIntervalMs = 10000; // 10 seconds
    
    /**
     * Enable deadlock detection
     */
    @Builder.Default
    private boolean enableDeadlockDetection = true;
    
    /**
     * Deadlock detection interval in milliseconds
     */
    @Builder.Default
    private long deadlockDetectionIntervalMs = 30000; // 30 seconds
    
    /**
     * Enable performance profiling for thread operations
     */
    @Builder.Default
    private boolean enablePerformanceProfiling = false; // Disabled by default for performance
    
    /**
     * Maximum number of performance samples to keep
     */
    @Builder.Default
    private int maxPerformanceSamples = 10000;
    
    // ===== ERROR HANDLING =====
    
    /**
     * Enable graceful degradation on thread failures
     */
    @Builder.Default
    private boolean enableGracefulDegradation = true;
    
    /**
     * Maximum number of thread failures before fallback to single-threaded mode
     */
    @Builder.Default
    private int maxThreadFailures = 3;
    
    /**
     * Thread failure recovery timeout in milliseconds
     */
    @Builder.Default
    private long threadFailureRecoveryTimeoutMs = 30000; // 30 seconds
    
    /**
     * Enable automatic retry on transient failures
     */
    @Builder.Default
    private boolean enableAutomaticRetry = true;
    
    /**
     * Maximum number of retry attempts
     */
    @Builder.Default
    private int maxRetryAttempts = 3;
    
    /**
     * Retry delay in milliseconds
     */
    @Builder.Default
    private long retryDelayMs = 1000; // 1 second
    
    // ===== VALIDATION AND SAFETY =====
    
    /**
     * Enable thread safety validation during development
     */
    @Builder.Default
    private boolean enableThreadSafetyValidation = false; // Disabled in production
    
    /**
     * Enable race condition detection
     */
    @Builder.Default
    private boolean enableRaceConditionDetection = false; // Disabled in production
    
    /**
     * Enable data consistency checks
     */
    @Builder.Default
    private boolean enableDataConsistencyChecks = false; // Disabled in production
    
    /**
     * Validation check interval in milliseconds
     */
    @Builder.Default
    private long validationCheckIntervalMs = 60000; // 1 minute
    
    // ===== FACTORY METHODS =====
    
    /**
     * Create configuration optimized for development with extensive monitoring
     */
    public static ThreadSafetyConfig developmentConfig() {
        return ThreadSafetyConfig.builder()
            .enableParallelTraining(true)
            .numTrainingThreads(2) // Lower for development
            .enableThreadSafetyMonitoring(true)
            .enableDeadlockDetection(true)
            .enablePerformanceProfiling(true)
            .enableThreadSafetyValidation(true)
            .enableRaceConditionDetection(true)
            .enableDataConsistencyChecks(true)
            .lockWarningThresholdMs(500) // More sensitive in development
            .monitoringIntervalMs(5000) // More frequent monitoring
            .build();
    }
    
    /**
     * Create configuration optimized for production with maximum performance
     */
    public static ThreadSafetyConfig productionConfig() {
        return ThreadSafetyConfig.builder()
            .enableParallelTraining(true)
            .numTrainingThreads(8) // Higher for production
            .abstractionThreadPoolSize(8)
            .maxThreadPoolSize(16)
            .enableFineGrainedLocking(true)
            .enableThreadLocalCaching(true)
            .enableAtomicOptimization(true)
            .enableMemoryOptimization(true)
            .enableThreadSafetyMonitoring(true)
            .enableDeadlockDetection(true)
            .enablePerformanceProfiling(false) // Disabled for performance
            .enableThreadSafetyValidation(false) // Disabled for performance
            .enableRaceConditionDetection(false) // Disabled for performance
            .enableDataConsistencyChecks(false) // Disabled for performance
            .build();
    }
    
    /**
     * Create configuration for testing with comprehensive validation
     */
    public static ThreadSafetyConfig testingConfig() {
        return ThreadSafetyConfig.builder()
            .enableParallelTraining(true)
            .numTrainingThreads(4)
            .enableThreadSafetyMonitoring(true)
            .enableDeadlockDetection(true)
            .enablePerformanceProfiling(true)
            .enableThreadSafetyValidation(true)
            .enableRaceConditionDetection(true)
            .enableDataConsistencyChecks(true)
            .enableGracefulDegradation(true)
            .enableAutomaticRetry(true)
            .lockWarningThresholdMs(100) // Very sensitive for testing
            .monitoringIntervalMs(1000) // Frequent monitoring for testing
            .validationCheckIntervalMs(5000) // Frequent validation for testing
            .build();
    }
    
    /**
     * Create configuration for single-threaded mode (fallback)
     */
    public static ThreadSafetyConfig singleThreadedConfig() {
        return ThreadSafetyConfig.builder()
            .enableParallelTraining(false)
            .numTrainingThreads(1)
            .abstractionThreadPoolSize(1)
            .maxThreadPoolSize(1)
            .enableFineGrainedLocking(false)
            .enableThreadLocalCaching(false)
            .enableAtomicOptimization(false)
            .enableThreadSafetyMonitoring(false)
            .enableDeadlockDetection(false)
            .enablePerformanceProfiling(false)
            .enableThreadSafetyValidation(false)
            .enableRaceConditionDetection(false)
            .enableDataConsistencyChecks(false)
            .build();
    }
    
    // ===== VALIDATION METHODS =====
    
    /**
     * Validate configuration settings
     * @return True if configuration is valid
     */
    public boolean isValid() {
        if (numTrainingThreads < 1) {
            log.error("Number of training threads must be at least 1");
            return false;
        }
        
        if (abstractionThreadPoolSize < 1) {
            log.error("Abstraction thread pool size must be at least 1");
            return false;
        }
        
        if (maxThreadPoolSize < abstractionThreadPoolSize) {
            log.error("Maximum thread pool size must be >= abstraction thread pool size");
            return false;
        }
        
        if (lockTimeoutMs <= 0) {
            log.error("Lock timeout must be positive");
            return false;
        }
        
        if (threadLocalCacheSize < 0) {
            log.error("Thread local cache size must be non-negative");
            return false;
        }
        
        if (atomicBatchSize < 1) {
            log.error("Atomic batch size must be at least 1");
            return false;
        }
        
        if (memoryOptimizationThresholdMB < 0) {
            log.error("Memory optimization threshold must be non-negative");
            return false;
        }
        
        return true;
    }
    
    /**
     * Get configuration summary for logging
     * @return Configuration summary string
     */
    public String getSummary() {
        return String.format("ThreadSafetyConfig[parallel=%s, threads=%d, poolSize=%d, " +
                           "monitoring=%s, validation=%s, profiling=%s]",
                enableParallelTraining, numTrainingThreads, abstractionThreadPoolSize,
                enableThreadSafetyMonitoring, enableThreadSafetyValidation, enablePerformanceProfiling);
    }
    
    /**
     * Get performance-related settings summary
     * @return Performance settings summary
     */
    public String getPerformanceSummary() {
        return String.format("Performance[threads=%d, caching=%s, atomic=%s, memory=%s, " +
                           "fineLocking=%s, cacheSize=%d]",
                numTrainingThreads, enableThreadLocalCaching, enableAtomicOptimization,
                enableMemoryOptimization, enableFineGrainedLocking, threadLocalCacheSize);
    }
    
    /**
     * Get monitoring-related settings summary
     * @return Monitoring settings summary
     */
    public String getMonitoringSummary() {
        return String.format("Monitoring[enabled=%s, interval=%dms, deadlock=%s, profiling=%s, " +
                           "validation=%s, raceDetection=%s]",
                enableThreadSafetyMonitoring, monitoringIntervalMs, enableDeadlockDetection,
                enablePerformanceProfiling, enableThreadSafetyValidation, enableRaceConditionDetection);
    }
}
