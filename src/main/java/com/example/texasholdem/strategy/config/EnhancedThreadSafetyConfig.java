package com.example.texasholdem.strategy.config;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * Enhanced ThreadSafetyConfig specifically designed for ParallelMCCFRTrainer integration
 * 
 * This configuration extends the base ThreadSafetyConfig with MCCFR-specific parameters
 * and provides optimized settings for parallel poker AI training.
 * 
 * Key Features:
 * - MCCFR algorithm parameter externalization
 * - Dynamic resource allocation based on system capabilities
 * - Performance optimization profiles for different training scenarios
 * - Integration with abstraction system configuration
 * - Backward compatibility with existing ThreadSafetyConfig
 */
@Data
@Builder
@Slf4j
public class EnhancedThreadSafetyConfig {

    // ========================================
    // CORE THREAD SAFETY CONFIGURATION
    // ========================================

    // Base ThreadSafetyConfig for delegation
    private final ThreadSafetyConfig baseConfig;

    // ========================================
    // MCCFR ALGORITHM PARAMETERS
    // ========================================

    // Pruning Configuration
    @Builder.Default
    private int pruneThreshold = 200;

    @Builder.Default
    private double pruningProbability = 0.05;

    @Builder.Default
    private int regretThreshold = -300_000_000;

    // Strategy and Discounting
    @Builder.Default
    private int strategyInterval = 10000;

    @Builder.Default
    private int discountInterval = 10;

    @Builder.Default
    private int lcfrThreshold = 400;

    // Memory Management
    @Builder.Default
    private long memoryThresholdBytesPerThread = 512 * 1024 * 1024; // 512MB per thread

    @Builder.Default
    private int memoryCheckInterval = 1000;

    // Recursion Protection
    @Builder.Default
    private int maxUpdateStrategyRecursionDepth = 200;

    @Builder.Default
    private int maxGameStateRestorationDepth = 10;

    // Game Configuration
    @Builder.Default
    private int smallBlindAmount = 5;

    @Builder.Default
    private int bigBlindAmount = 10;

    // ========================================
    // PARALLEL TRAINING OPTIMIZATION
    // ========================================

    // Thread Pool Configuration
    @Builder.Default
    private int optimalThreadCount = calculateOptimalThreadCount();

    @Builder.Default
    private int maxThreadPoolSize = calculateMaxThreadPoolSize();

    @Builder.Default
    private long threadKeepAliveMs = 60000; // 1 minute

    @Builder.Default
    private int threadPoolQueueCapacity = 1000;

    // Training Distribution
    @Builder.Default
    private boolean enableDynamicLoadBalancing = true;

    @Builder.Default
    private boolean enableWorkStealing = true;

    @Builder.Default
    private int workStealingThreshold = 100;

    // ========================================
    // PERFORMANCE MONITORING
    // ========================================

    // Progress Logging
    @Builder.Default
    private int progressLoggingInterval = 1000;

    @Builder.Default
    private boolean enableDetailedProgressLogging = false;

    // Performance Profiling
    @Builder.Default
    private boolean enableThreadPerformanceProfiling = true;

    @Builder.Default
    private boolean enableMemoryProfiling = true;

    @Builder.Default
    private boolean enableCacheStatistics = true;

    // ========================================
    // ABSTRACTION SYSTEM INTEGRATION
    // ========================================

    // Abstraction Configuration
    @Builder.Default
    private boolean enableAbstractionIntegration = true;

    @Builder.Default
    private int abstractionCacheSize = 20000;

    @Builder.Default
    private boolean enableActionBridgeOptimization = true;

    @Builder.Default
    private boolean enableComprehensiveAbstraction = true;

    // ========================================
    // FACTORY METHODS
    // ========================================

    /**
     * Create configuration optimized for training scenarios
     */
    public static EnhancedThreadSafetyConfig forTraining() {
        return EnhancedThreadSafetyConfig.builder()
            .baseConfig(ThreadSafetyConfig.productionConfig())
            .optimalThreadCount(calculateOptimalThreadCount())
            .maxThreadPoolSize(calculateMaxThreadPoolSize())
            .memoryThresholdBytesPerThread(1024 * 1024 * 1024) // 1GB per thread for training
            .enableDetailedProgressLogging(true)
            .enableThreadPerformanceProfiling(true)
            .enableAbstractionIntegration(true)
            .enableComprehensiveAbstraction(true)
            .build();
    }

    /**
     * Create configuration optimized for real-time play
     */
    public static EnhancedThreadSafetyConfig forRealTime() {
        return EnhancedThreadSafetyConfig.builder()
            .baseConfig(ThreadSafetyConfig.realtimeConfig())
            .optimalThreadCount(Math.min(4, calculateOptimalThreadCount()))
            .maxThreadPoolSize(6)
            .memoryThresholdBytesPerThread(256 * 1024 * 1024) // 256MB per thread for real-time
            .enableDetailedProgressLogging(false)
            .enableThreadPerformanceProfiling(false)
            .enableAbstractionIntegration(true)
            .enableComprehensiveAbstraction(false) // Faster abstraction for real-time
            .build();
    }

    /**
     * Create configuration for development and testing
     */
    public static EnhancedThreadSafetyConfig forDevelopment() {
        return EnhancedThreadSafetyConfig.builder()
            .baseConfig(ThreadSafetyConfig.developmentConfig())
            .optimalThreadCount(2)
            .maxThreadPoolSize(4)
            .memoryThresholdBytesPerThread(128 * 1024 * 1024) // 128MB per thread for development
            .enableDetailedProgressLogging(true)
            .enableThreadPerformanceProfiling(true)
            .enableMemoryProfiling(true)
            .enableCacheStatistics(true)
            .enableAbstractionIntegration(true)
            .enableComprehensiveAbstraction(true)
            .build();
    }

    // ========================================
    // DYNAMIC CONFIGURATION METHODS
    // ========================================

    /**
     * Calculate optimal thread count based on system resources
     */
    private static int calculateOptimalThreadCount() {
        int availableProcessors = Runtime.getRuntime().availableProcessors();
        long availableMemoryMB = Runtime.getRuntime().maxMemory() / (1024 * 1024);
        
        // Base calculation on processors, but limit by memory
        int processorBasedThreads = Math.max(1, availableProcessors - 1); // Leave one core for system
        int memoryBasedThreads = (int) Math.max(1, availableMemoryMB / 512); // 512MB per thread minimum
        
        int optimalThreads = Math.min(processorBasedThreads, memoryBasedThreads);
        
        // Cap at 8 threads for MCCFR efficiency
        return Math.min(optimalThreads, 8);
    }

    /**
     * Calculate maximum thread pool size
     */
    private static int calculateMaxThreadPoolSize() {
        return calculateOptimalThreadCount() * 2; // Allow 2x for burst capacity
    }

    /**
     * Adjust configuration based on current system load
     */
    public EnhancedThreadSafetyConfig adjustForSystemLoad() {
        Runtime runtime = Runtime.getRuntime();
        long freeMemory = runtime.freeMemory();
        long totalMemory = runtime.totalMemory();
        double memoryUsage = (double) (totalMemory - freeMemory) / totalMemory;
        
        EnhancedThreadSafetyConfigBuilder builder = this.toBuilder();
        
        if (memoryUsage > 0.8) {
            // High memory usage - reduce thread count and memory thresholds
            builder.optimalThreadCount(Math.max(1, this.optimalThreadCount / 2));
            builder.memoryThresholdBytesPerThread(this.memoryThresholdBytesPerThread / 2);
            builder.enableComprehensiveAbstraction(false);
            log.info("Adjusted configuration for high memory usage: {:.1f}%", memoryUsage * 100);
        } else if (memoryUsage < 0.3) {
            // Low memory usage - can increase performance settings
            builder.enableDetailedProgressLogging(true);
            builder.enableThreadPerformanceProfiling(true);
            builder.enableComprehensiveAbstraction(true);
            log.debug("Adjusted configuration for low memory usage: {:.1f}%", memoryUsage * 100);
        }
        
        return builder.build();
    }

    // ========================================
    // DELEGATION METHODS
    // ========================================

    /**
     * Delegate to base config for thread safety methods
     */
    public boolean isEnableParallelTraining() {
        return baseConfig.isEnableParallelTraining();
    }

    public boolean isEnableThreadSafetyMonitoring() {
        return baseConfig.isEnableThreadSafetyMonitoring();
    }

    public boolean isEnableGracefulDegradation() {
        return baseConfig.isEnableGracefulDegradation();
    }

    public boolean isEnableAutomaticRetry() {
        return baseConfig.isEnableAutomaticRetry();
    }

    public boolean isEnableDeadlockDetection() {
        return baseConfig.isEnableDeadlockDetection();
    }

    public boolean isEnablePerformanceProfiling() {
        return baseConfig.isEnablePerformanceProfiling();
    }

    public boolean isEnableMemoryOptimization() {
        return baseConfig.isEnableMemoryOptimization();
    }

    public int getNumTrainingThreads() {
        return Math.min(optimalThreadCount, baseConfig.getNumTrainingThreads());
    }

    public int getMaxThreadFailures() {
        return baseConfig.getMaxThreadFailures();
    }

    public int getMaxRetryAttempts() {
        return baseConfig.getMaxRetryAttempts();
    }

    public long getRetryDelayMs() {
        return baseConfig.getRetryDelayMs();
    }

    public long getThreadFailureRecoveryTimeoutMs() {
        return baseConfig.getThreadFailureRecoveryTimeoutMs();
    }

    public long getMonitoringIntervalMs() {
        return baseConfig.getMonitoringIntervalMs();
    }

    public int getMemoryOptimizationThresholdMB() {
        return baseConfig.getMemoryOptimizationThresholdMB();
    }

    // ========================================
    // UTILITY METHODS
    // ========================================

    /**
     * Get comprehensive summary of configuration
     */
    public String getSummary() {
        return String.format(
            "EnhancedThreadSafetyConfig{threads=%d, maxThreads=%d, memoryPerThread=%dMB, " +
            "pruneThreshold=%d, lcfrThreshold=%d, abstraction=%s, monitoring=%s}",
            getNumTrainingThreads(), maxThreadPoolSize, memoryThresholdBytesPerThread / (1024 * 1024),
            pruneThreshold, lcfrThreshold, enableAbstractionIntegration, 
            baseConfig.isEnableThreadSafetyMonitoring()
        );
    }

    /**
     * Get performance-focused summary
     */
    public String getPerformanceSummary() {
        return String.format(
            "Performance{threads=%d, memory=%dMB, profiling=%s, abstraction=%s, loadBalancing=%s}",
            getNumTrainingThreads(), memoryThresholdBytesPerThread / (1024 * 1024),
            enableThreadPerformanceProfiling, enableAbstractionIntegration, enableDynamicLoadBalancing
        );
    }

    /**
     * Validate configuration
     */
    public boolean isValid() {
        return baseConfig.isValid() && 
               optimalThreadCount > 0 && 
               maxThreadPoolSize >= optimalThreadCount &&
               memoryThresholdBytesPerThread > 0 &&
               pruneThreshold >= 0 &&
               lcfrThreshold > 0;
    }
}
