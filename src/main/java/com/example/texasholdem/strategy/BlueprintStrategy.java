package com.example.texasholdem.strategy;

import com.example.texasholdem.model.*;

import com.example.texasholdem.strategy.model.AbstractAction;
import com.example.texasholdem.strategy.model.ActionTrace;
import com.example.texasholdem.strategy.model.InfosetStore;
import com.example.texasholdem.strategy.model.InfosetValue;
import com.example.texasholdem.strategy.model.Infoset;
import com.example.texasholdem.strategy.train.InfosetStoreKey;
import java.util.*;
import lombok.Setter;

public class BlueprintStrategy implements Strategy {

  private final InfosetStore store;
  @Setter
  private InfosetStoreKey abstraction;
  private final Random rng = new Random();

  private static final float[] BET_SIZE_MULTIPLIERS = {0.3f, 0.6f, 1.0f, 2.0f, 5.0f};

  public BlueprintStrategy(InfosetStore store) {
    this.store = store;
  }

  @Override
  public ActionInstance decideAction(List<Card> holeCards, List<Card> communityCards,
      BettingRound currentRound, Player player, int currentRoundBetAmount,
      List<ActionTrace> actionHistory, int potSize) {
    Infoset key = Infoset.of(holeCards, communityCards, currentRound, player, currentRoundBetAmount,
        actionHistory);
    InfosetValue infosetValue = store.getOrCreate(player.getPlayerIndex(),
        InfosetStoreKey.abstractInfoset(key));

    float[] normalizedStrategy = normalizedStrategy(
        filterIllegalActionsNew(player, currentRoundBetAmount, potSize, holeCards, currentRound,
            infosetValue.getActionCounter()));

    AbstractAction actionAbstractChosen = AbstractAction.values()[sampleFromDistribution(
        normalizedStrategy)];

    return convertAbstractToRealAction(actionAbstractChosen, player, currentRoundBetAmount,
        potSize);
  }

  public float[] normalizedStrategy(float[] strategy) {
    float sum = 0f;
    for (float prob : strategy) {
      sum += prob;
    }

    float[] normalStrategy = new float[strategy.length];
    if (sum > 1e-6f) {
      for (int i = 0; i < strategy.length; i++) {
        normalStrategy[i] = strategy[i] / sum;
      }
    } else {
      Arrays.fill(normalStrategy, 1f / strategy.length);
    }
    return normalStrategy;
  }


  public float[] filterIllegalActions(Player player, int currentRoundBetAmount, int potSize,
      float[] initialStrategy) {
    float[] strategy = new float[7];

    if (player.isFolded() || player.isAllIn()) {
      return strategy;
    }

    if (player.getCurrentBet() < currentRoundBetAmount) {
      if (player.getChips() >= currentRoundBetAmount - player.getCurrentBet()) {
        strategy[AbstractAction.CHECK_OR_CALL.ordinal()] = initialStrategy[AbstractAction.CHECK_OR_CALL.ordinal()];
      }
      if (player.getChips() > currentRoundBetAmount - player.getCurrentBet()) {
        for (int i = 0; i < BET_SIZE_MULTIPLIERS.length; i++) {
          if (player.getCurrentBet() + (int) (potSize * BET_SIZE_MULTIPLIERS[i])
              >= currentRoundBetAmount) {
            strategy[AbstractAction.BET_OR_RAISE_30.ordinal() + i] = initialStrategy[
                AbstractAction.BET_OR_RAISE_30.ordinal() + i];
          }
        }
      }
      strategy[AbstractAction.FOLD.ordinal()] = initialStrategy[AbstractAction.FOLD.ordinal()];
    } else {
      strategy[AbstractAction.CHECK_OR_CALL.ordinal()] = initialStrategy[AbstractAction.CHECK_OR_CALL.ordinal()];
      for (int i = 0; i < BET_SIZE_MULTIPLIERS.length; i++) {
        if ((int) (potSize * BET_SIZE_MULTIPLIERS[i]) > 0
            && (int) (potSize * BET_SIZE_MULTIPLIERS[i]) <= player.getChips()) {
          strategy[AbstractAction.BET_OR_RAISE_30.ordinal() + i] =
              initialStrategy[AbstractAction.BET_OR_RAISE_30.ordinal() + i];
        }
      }
    }

    return strategy;
  }

  public float[] filterIllegalActionsNew(Player player, int currentRoundBetAmount, int potSize,
      List<Card> holeCards, BettingRound currentRound, float[] initialStrategy) {
    float[] strategy = new float[7];

    if (holeCards.isEmpty() || currentRound == null || player.getChips() == 0
        || player.isFolded()) {
      strategy[AbstractAction.FOLD.ordinal()] = initialStrategy[AbstractAction.FOLD.ordinal()];
      return strategy;
    }
    if (currentRoundBetAmount == 0) {
      for (int i = 0; i < BET_SIZE_MULTIPLIERS.length; i++) {
        if ((int) (potSize * BET_SIZE_MULTIPLIERS[i]) > 0
            && (int) (potSize * BET_SIZE_MULTIPLIERS[i]) <= player.getChips()) {
          strategy[AbstractAction.BET_OR_RAISE_30.ordinal() + i] =
              initialStrategy[AbstractAction.BET_OR_RAISE_30.ordinal() + i];
        }
      }
      strategy[AbstractAction.CHECK_OR_CALL.ordinal()] = initialStrategy[AbstractAction.CHECK_OR_CALL.ordinal()];
      return strategy;
    } else {
      if (player.getChips() <= currentRoundBetAmount) {
        strategy[AbstractAction.FOLD.ordinal()] = initialStrategy[AbstractAction.FOLD.ordinal()];
        strategy[AbstractAction.CHECK_OR_CALL.ordinal()] = initialStrategy[AbstractAction.CHECK_OR_CALL.ordinal()];
        return strategy;
      } else {
        for (int i = 0; i < BET_SIZE_MULTIPLIERS.length; i++) {
          if ((int) (potSize * BET_SIZE_MULTIPLIERS[i]) > 0 &&
              (int) (potSize * BET_SIZE_MULTIPLIERS[i] + currentRoundBetAmount)
                  <= player.getChips()) {
            strategy[AbstractAction.BET_OR_RAISE_30.ordinal() + i] =
                initialStrategy[AbstractAction.BET_OR_RAISE_30.ordinal() + i];
          }
        }
        strategy[AbstractAction.FOLD.ordinal()] = initialStrategy[AbstractAction.FOLD.ordinal()];
        strategy[AbstractAction.CHECK_OR_CALL.ordinal()] = initialStrategy[AbstractAction.CHECK_OR_CALL.ordinal()];
        return strategy;
      }
    }
  }

  private int sampleFromDistribution(float[] probabilities) {
    float cumulative = 0f;
    for (int i = 0; i < probabilities.length; i++) {
      cumulative += probabilities[i];
      if (rng.nextFloat() <= cumulative) {
        return i;
      }
    }
    return probabilities.length - 1;
  }

  private ActionInstance convertAbstractToRealAction(AbstractAction abstractAction, Player player,
      int currentBet, int potSize) {

    switch (abstractAction) {
      case FOLD:
        return new ActionInstance(Action.FOLD, 0);
      case CHECK_OR_CALL:
        if (player.getCurrentBet() < currentBet) {
          return new ActionInstance(Action.CALL,
              Math.min(player.getChips(), currentBet - player.getCurrentBet()));
        } else {
          return new ActionInstance(Action.CHECK, 0);
        }
      case BET_OR_RAISE_30:
      case BET_OR_RAISE_60:
      case BET_OR_RAISE_100:
      case BET_OR_RAISE_200:
      case BET_OR_RAISE_500:

        if (player.getChips() <= currentBet + potSize * getAllInThreshold(
            abstractAction)) {
          return new ActionInstance((currentBet == 0) ? Action.BET : Action.RAISE,
              player.getChips());
        } else {
          return new ActionInstance((currentBet == 0) ? Action.BET : Action.RAISE,
              (int) (currentBet - player.getCurrentBet() + potSize * getAllInMultiplier(
                  abstractAction)));
        }
      default:
        throw new IllegalArgumentException("Invalid abstract action: " + abstractAction);
    }
  }

  private float getAllInThreshold(AbstractAction action) {
    if (action == AbstractAction.BET_OR_RAISE_30) {
      return 0.45f;
    } else if (action == AbstractAction.BET_OR_RAISE_60) {
      return 0.8f;
    } else if (action == AbstractAction.BET_OR_RAISE_100) {
      return 1.5f;
    } else if (action == AbstractAction.BET_OR_RAISE_200) {
      return 3.5f;
    } else if (action == AbstractAction.BET_OR_RAISE_500) {
      return 7.0f;
    } else {
      return 0.0f;
    }
  }

  private float getAllInMultiplier(AbstractAction abstractAction) {
    if (abstractAction == AbstractAction.BET_OR_RAISE_30) {
      return 0.3f;
    } else if (abstractAction == AbstractAction.BET_OR_RAISE_60) {
      return 0.6f;
    } else if (abstractAction == AbstractAction.BET_OR_RAISE_100) {
      return 1.0f;
    } else if (abstractAction == AbstractAction.BET_OR_RAISE_200) {
      return 2.0f;
    } else if (abstractAction == AbstractAction.BET_OR_RAISE_500) {
      return 5.0f;
    } else {
      return 0.0f;
    }
  }
}
