package com.example.texasholdem.strategy.train;

import com.example.texasholdem.strategy.model.InfosetStore;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Strategy Version Manager for production-ready strategy management Supports versioning, rollback,
 * validation, and hot-swapping capabilities
 */
@Slf4j
public class StrategyVersionManager {

  private final Path baseDirectory;
  private final Map<String, StrategyMetadata> versionMetadata;
  private final ReentrantReadWriteLock lock;
  private volatile String currentVersion;

  // Strategy validation settings
  private final int maxVersions;
  private final boolean enableValidation;

  /**
   * Strategy metadata for version tracking
   */
  public static class StrategyMetadata {

    public final String version;
    public final LocalDateTime timestamp;
    public final long fileSize;
    public final String checksum;
    public final int numPlayers;
    public final int numActions;
    public final long numInfosets;
    public final String description;

    public StrategyMetadata(String version, LocalDateTime timestamp, long fileSize,
        String checksum, int numPlayers, int numActions,
        long numInfosets, String description) {
      this.version = version;
      this.timestamp = timestamp;
      this.fileSize = fileSize;
      this.checksum = checksum;
      this.numPlayers = numPlayers;
      this.numActions = numActions;
      this.numInfosets = numInfosets;
      this.description = description;
    }

    @Override
    public String toString() {
      return String.format("Version %s (%s): %d players, %d actions, %d infosets, %.2f MB",
          version, timestamp.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
          numPlayers, numActions, numInfosets, fileSize / (1024.0 * 1024.0));
    }
  }

  /**
   * Initialize strategy version manager
   *
   * @param baseDirectory    Directory to store strategy versions
   * @param maxVersions      Maximum number of versions to keep
   * @param enableValidation Whether to enable strategy validation
   */
  public StrategyVersionManager(Path baseDirectory, int maxVersions, boolean enableValidation) {
    this.baseDirectory = baseDirectory;
    this.maxVersions = maxVersions;
    this.enableValidation = enableValidation;
    this.versionMetadata = new ConcurrentHashMap<>();
    this.lock = new ReentrantReadWriteLock();

    try {
      Files.createDirectories(baseDirectory);
      loadExistingVersions();
      log.info("StrategyVersionManager initialized: {} versions, max {}, validation {}",
          versionMetadata.size(), maxVersions, enableValidation);
    } catch (IOException e) {
      throw new RuntimeException("Failed to initialize StrategyVersionManager", e);
    }
  }

  /**
   * Save a new strategy version with automatic versioning
   *
   * @param store       InfosetStore to save
   * @param description Description of this version
   * @return Version identifier
   */
  public String saveVersionedStrategy(InfosetStore store, String description) {
    String version = generateVersionId();
    return saveVersionedStrategy(store, version, description);
  }

  /**
   * Save a strategy version with specific version identifier
   *
   * @param store       InfosetStore to save
   * @param version     Version identifier
   * @param description Description of this version
   * @return Version identifier
   */
  public String saveVersionedStrategy(InfosetStore store, String version, String description) {
    lock.writeLock().lock();
    try {
      log.info("Saving strategy version: {}", version);

      // Validate strategy if enabled
      if (enableValidation && !validateStrategy(store)) {
        throw new IllegalArgumentException("Strategy validation failed for version: " + version);
      }

      Path versionFile = getVersionFile(version);
      long startTime = System.currentTimeMillis();

      // Save strategy with fast compression for production use
      try (FileOutputStream fos = new FileOutputStream(versionFile.toFile())) {
        StrategySerializer.saveFastCompression(store, fos);
      }

      long saveTime = System.currentTimeMillis() - startTime;
      long fileSize = Files.size(versionFile);
      String checksum = calculateChecksum(versionFile);
      long numInfosets = countInfosets(store);

      // Create metadata
      StrategyMetadata metadata = new StrategyMetadata(
          version, LocalDateTime.now(), fileSize, checksum,
          store.getNumPlayers(), store.getNumActions(), numInfosets, description
      );

      versionMetadata.put(version, metadata);
      currentVersion = version;

      // Save metadata
      saveMetadata();

      // Cleanup old versions if needed
      cleanupOldVersions();

      log.info("Strategy version {} saved successfully in {}ms: {}",
          version, saveTime, metadata);

      return version;

    } catch (IOException e) {
      throw new RuntimeException("Failed to save strategy version: " + version, e);
    } finally {
      lock.writeLock().unlock();
    }
  }

  /**
   * Load a specific strategy version
   *
   * @param version Version identifier
   * @return Loaded InfosetStore
   */
  public InfosetStore loadVersionedStrategy(String version) {
    lock.readLock().lock();
    try {
      if (!versionMetadata.containsKey(version)) {
        throw new IllegalArgumentException("Version not found: " + version);
      }

      log.info("Loading strategy version: {}", version);

      Path versionFile = getVersionFile(version);
      if (!Files.exists(versionFile)) {
        throw new IllegalStateException("Version file not found: " + versionFile);
      }

      long startTime = System.currentTimeMillis();
      InfosetStore store;

      try (FileInputStream fis = new FileInputStream(versionFile.toFile())) {
        store = StrategySerializer.loadFastDecompression(fis);
      }

      long loadTime = System.currentTimeMillis() - startTime;

      // Validate loaded strategy if enabled
      if (enableValidation && !validateStrategy(store)) {
        throw new IllegalStateException(
            "Loaded strategy validation failed for version: " + version);
      }

      log.info("Strategy version {} loaded successfully in {}ms", version, loadTime);
      return store;

    } catch (IOException e) {
      throw new RuntimeException("Failed to load strategy version: " + version, e);
    } finally {
      lock.readLock().unlock();
    }
  }

  /**
   * Rollback to a previous strategy version
   *
   * @param version Version to rollback to
   * @return Loaded InfosetStore for the rollback version
   */
  public InfosetStore rollbackToVersion(String version) {
    lock.writeLock().lock();
    try {
      log.info("Rolling back to strategy version: {}", version);

      InfosetStore store = loadVersionedStrategy(version);
      currentVersion = version;

      log.info("Successfully rolled back to version: {}", version);
      return store;

    } finally {
      lock.writeLock().unlock();
    }
  }

  /**
   * Get list of available strategy versions
   *
   * @return List of version identifiers sorted by timestamp (newest first)
   */
  public List<String> getAvailableVersions() {
    lock.readLock().lock();
    try {
      return versionMetadata.values().stream()
          .sorted((a, b) -> b.timestamp.compareTo(a.timestamp))
          .map(m -> m.version)
          .toList();
    } finally {
      lock.readLock().unlock();
    }
  }

  /**
   * Get metadata for a specific version
   *
   * @param version Version identifier
   * @return Strategy metadata
   */
  public StrategyMetadata getVersionMetadata(String version) {
    lock.readLock().lock();
    try {
      return versionMetadata.get(version);
    } finally {
      lock.readLock().unlock();
    }
  }

  /**
   * Get current active version
   *
   * @return Current version identifier
   */
  public String getCurrentVersion() {
    return currentVersion;
  }

  /**
   * Delete a specific strategy version
   *
   * @param version Version to delete
   * @return True if deleted successfully
   */
  public boolean deleteVersion(String version) {
    if (version.equals(currentVersion)) {
      throw new IllegalArgumentException("Cannot delete current active version: " + version);
    }

    lock.writeLock().lock();
    try {
      Path versionFile = getVersionFile(version);
      boolean deleted = Files.deleteIfExists(versionFile);

      if (deleted) {
        versionMetadata.remove(version);
        saveMetadata();
        log.info("Strategy version {} deleted successfully", version);
      }

      return deleted;

    } catch (IOException e) {
      log.error("Failed to delete strategy version: {}", version, e);
      return false;
    } finally {
      lock.writeLock().unlock();
    }
  }

  // ===== PRIVATE HELPER METHODS =====

  private String generateVersionId() {
    // Use nanosecond precision to ensure unique version IDs
    LocalDateTime now = LocalDateTime.now();
    String baseId = "v" + now.format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));

    // Add nanosecond suffix to ensure uniqueness
    int nanos = now.getNano();
    String uniqueId = baseId + "_" + String.format("%09d", nanos);

    // If by some chance this ID already exists, add a counter
    int counter = 1;
    String finalId = uniqueId;
    while (versionMetadata.containsKey(finalId)) {
      finalId = uniqueId + "_" + counter;
      counter++;
    }

    return finalId;
  }

  private Path getVersionFile(String version) {
    return baseDirectory.resolve(version + ".strategy");
  }

  private Path getMetadataFile() {
    return baseDirectory.resolve("versions.metadata");
  }

  private void loadExistingVersions() throws IOException {
    Path metadataFile = getMetadataFile();
    if (!Files.exists(metadataFile)) {
      return;
    }

    try (BufferedReader reader = Files.newBufferedReader(metadataFile)) {
      String line;
      while ((line = reader.readLine()) != null) {
        if (line.trim().isEmpty() || line.startsWith("#")) {
          continue;
        }

        // Parse metadata line: version,timestamp,fileSize,checksum,players,actions,infosets,description
        String[] parts = line.split(",", 8);
        if (parts.length >= 7) {
          StrategyMetadata metadata = new StrategyMetadata(
              parts[0], LocalDateTime.parse(parts[1]), Long.parseLong(parts[2]),
              parts[3], Integer.parseInt(parts[4]), Integer.parseInt(parts[5]),
              Long.parseLong(parts[6]), parts.length > 7 ? parts[7] : ""
          );
          versionMetadata.put(parts[0], metadata);

          // Set most recent as current
          if (currentVersion == null || metadata.timestamp.isAfter(
              versionMetadata.get(currentVersion).timestamp)) {
            currentVersion = parts[0];
          }
        }
      }
    }

    log.info("Loaded {} existing strategy versions", versionMetadata.size());
  }

  private void saveMetadata() throws IOException {
    Path metadataFile = getMetadataFile();
    try (BufferedWriter writer = Files.newBufferedWriter(metadataFile)) {
      writer.write("# Strategy Version Metadata\n");
      writer.write(
          "# Format: version,timestamp,fileSize,checksum,players,actions,infosets,description\n");

      for (StrategyMetadata metadata : versionMetadata.values()) {
        writer.write(String.format("%s,%s,%d,%s,%d,%d,%d,%s\n",
            metadata.version, metadata.timestamp, metadata.fileSize, metadata.checksum,
            metadata.numPlayers, metadata.numActions, metadata.numInfosets, metadata.description));
      }
    }
  }

  private void cleanupOldVersions() throws IOException {
    if (versionMetadata.size() <= maxVersions) {
      return;
    }

    List<String> sortedVersions = versionMetadata.values().stream()
        .sorted((a, b) -> a.timestamp.compareTo(b.timestamp)) // Oldest first
        .map(m -> m.version)
        .toList();

    int toDelete = versionMetadata.size() - maxVersions;
    for (int i = 0; i < toDelete; i++) {
      String version = sortedVersions.get(i);
      if (!version.equals(currentVersion)) {
        deleteVersion(version);
      }
    }
  }

  private boolean validateStrategy(InfosetStore store) {
    // Basic validation checks
    if (store.getNumPlayers() <= 0) {
      log.warn("Strategy validation failed: Invalid number of players: {}", store.getNumPlayers());
      return false;
    }

    if (store.getNumActions() <= 0) {
      log.warn("Strategy validation failed: Invalid number of actions: {}", store.getNumActions());
      return false;
    }

    // Check for reasonable number of infosets
    long totalInfosets = countInfosets(store);
    if (totalInfosets == 0) {
      log.warn("Strategy validation failed: No infosets found in store");
      return false;
    }

    log.info("Strategy validation passed: {} players, {} actions, {} infosets",
        store.getNumPlayers(), store.getNumActions(), totalInfosets);
    return true;
  }

  private long countInfosets(InfosetStore store) {
    long total = 0;
    for (int p = 0; p < store.getNumPlayers(); p++) {
      // Count both regular infosets and abstraction infosets
      long regularInfosets = store.getMapForPlayer(p).size();
      long abstractionInfosets = store.getAbstractionMapForPlayer(p).size();

      total += regularInfosets;
      total += abstractionInfosets;

      log.debug("Player {}: {} regular infosets, {} abstraction infosets",
          p, regularInfosets, abstractionInfosets);
    }
    log.debug("Total infosets counted: {}", total);
    return total;
  }

  private String calculateChecksum(Path file) throws IOException {
    // Simple checksum based on file size and modification time
    long size = Files.size(file);
    long modified = Files.getLastModifiedTime(file).toMillis();
    return String.format("%08x", (int) (size ^ modified));
  }
}
