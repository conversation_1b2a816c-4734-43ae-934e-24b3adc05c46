package com.example.texasholdem.strategy.train;

import com.example.texasholdem.model.BettingRound;
import com.example.texasholdem.model.Card;
import com.example.texasholdem.model.Player;
import com.example.texasholdem.service.GameService;
import com.example.texasholdem.strategy.model.AbstractAction;
import com.example.texasholdem.strategy.model.ActionTrace;
import com.example.texasholdem.strategy.model.CardState;
import com.example.texasholdem.strategy.model.Infoset;
import com.example.texasholdem.strategy.model.InfosetStore;
import com.example.texasholdem.strategy.model.InfosetValue;
import com.example.texasholdem.strategy.model.ThreadSafeInfosetStore;
import com.example.texasholdem.strategy.train.MultiThreadedMCCFRTrainer.TrainingResult;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Monte Carlo CFR implementation following Pluribus algorithm Key differences from standard CFR: 1.
 * Uses Monte Carlo sampling instead of full tree traversal 2. Tracks strategy only on preflop
 * (first betting round) 3. Implements negative regret pruning with -300M threshold 4. Uses Linear
 * CFR discounting after 400 iterations
 */
@Component
@Slf4j
public class ParallelMCCFRTrainer {

  private final ThreadSafeInfosetStore store;
  private final GameService gameService;

  private final ExecutorService trainingExecutor;
  private final int numThreads;
  private final boolean parallelTrainingEnabled;

  private final ThreadLocal<GameService> threadLocalGameService;

  // Thread-local random number generators
  private final ThreadLocal<Random> threadLocalRandom;

  // Pluribus algorithm constants
  private static final int STRATEGY_INTERVAL = 100;
  private static final int PRUNE_THRESHOLD = 200000;
  private static final double PRUNING_PROBABILITY = 0.95;
  private static final int LCFR_THRESHOLD = 400;
  private static final int DISCOUNT_INTERVAL = 10;
  private static final float REGRET_THRESHOLD = -300000000f;
  private static final int MEMORY_CHECK_INTERVAL = 10000;
  private static final long MEMORY_THRESHOLD_BYTES = 8L * 1024 * 1024 * 1024; // 8GB

  // Thread-safe performance monitoring
  private final AtomicLong totalIterationsCompleted;
  private final AtomicLong totalTrainingTimeMs;
  private final AtomicInteger activeThreads;
  private final ConcurrentHashMap<Integer, Long> threadIterationCounts;
  private final ConcurrentHashMap<Integer, Long> threadTrainingTimes;

  // Training configuration
  private volatile boolean trainingInProgress;
  private volatile boolean shouldStopTraining;

  // Training configuration
  private volatile boolean trainingInProgress;
  private volatile boolean shouldStopTraining;

  private List<CardState> cardStateList=new ArrayList<>(new ArrayList<>());

  /**
   * Constructor for multi-threaded MCCFR trainer
   *
   * @param store                  Thread-safe InfosetStore
   * @param gameService            Template GameService for creating thread-local instances
   * @param numThreads             Number of training threads (recommended: 4-8)
   * @param enableParallelTraining Enable parallel training mode
   */
  public ParallelMCCFRTrainer(ThreadSafeInfosetStore store, GameService gameService,
      int numThreads, boolean enableParallelTraining) {
    this.store = store;
    this.gameService = gameService;
    this.numThreads = Math.max(1, numThreads);
    this.parallelTrainingEnabled = enableParallelTraining;

    // Initialize thread pool
    this.trainingExecutor = enableParallelTraining ?
        Executors.newFixedThreadPool(this.numThreads, r -> {
          Thread t = new Thread(r, "MCCFR-Worker-" + System.currentTimeMillis());
          t.setDaemon(true);
          return t;
        }) : null;

    // Initialize thread-local storage
    this.threadLocalGameService = ThreadLocal.withInitial(this::createThreadLocalGameService);
    this.threadLocalRandom = ThreadLocal.withInitial(() -> new Random(System.nanoTime()));

    // Initialize performance monitoring
    this.totalIterationsCompleted = new AtomicLong(0);
    this.totalTrainingTimeMs = new AtomicLong(0);
    this.activeThreads = new AtomicInteger(0);
    this.threadIterationCounts = new ConcurrentHashMap<>();
    this.threadTrainingTimes = new ConcurrentHashMap<>();

    // Initialize training state
    this.trainingInProgress = false;
    this.shouldStopTraining = false;

    log.info("MultiThreadedMCCFRTrainer initialized with {} threads, parallel training: {}",
        this.numThreads, enableParallelTraining);
  }

  /**
   * Main multi-threaded MCCFR training method
   *
   * @param iterations  Total number of training iterations
   * @param playerCount Number of players in the game
   */
  public void train(int iterations, int playerCount) {
    if (trainingInProgress) {
      log.warn("Training already in progress, ignoring new training request");
      return;
    }

    trainingInProgress = true;
    shouldStopTraining = false;
    long trainingStartTime = System.currentTimeMillis();

    try {
      log.info("Starting multi-threaded MCCFR training: {} iterations, {} players, {} threads",
          iterations, playerCount, parallelTrainingEnabled ? numThreads : 1);

      // Initialize players in template GameService
      initializePlayersInTemplate(playerCount);

      if (parallelTrainingEnabled && numThreads > 1) {
        trainParallel(iterations, playerCount);
      } else {
        trainSequential(iterations, playerCount);
      }

      long trainingEndTime = System.currentTimeMillis();
      long totalTime = trainingEndTime - trainingStartTime;
      totalTrainingTimeMs.addAndGet(totalTime);

      log.info("MCCFR training completed: {} iterations in {}ms ({:.2f} iterations/sec)",
          iterations, totalTime, (double) iterations / (totalTime / 1000.0));

      // Print final performance statistics
      printTrainingStatistics();

    } catch (Exception e) {
      log.error("Error during MCCFR training", e);
      throw new RuntimeException("MCCFR training failed", e);
    } finally {
      trainingInProgress = false;
      shouldStopTraining = false;
    }
  }

  /////////////////////////////////////////////////////////////////

  private void trainParallel(int iterations, int playerCount) {
    int iterationsPerThread = iterations / numThreads;
    int remainingIterations = iterations % numThreads;

    CountDownLatch completionLatch = new CountDownLatch(numThreads);
    List<Future<TrainingResult>> futures = new ArrayList<>();

    log.info("Distributing {} iterations across {} threads ({} per thread, {} remainder)",
        iterations, numThreads, iterationsPerThread, remainingIterations);

    // Submit training tasks to thread pool
    for (int threadId = 0; threadId < numThreads; threadId++) {
      final int finalThreadId = threadId;
      final int threadIterations = iterationsPerThread + (threadId < remainingIterations ? 1 : 0);
      final int startIteration =
          threadId * iterationsPerThread + Math.min(threadId, remainingIterations) + 1;

      Future<TrainingResult> future = trainingExecutor.submit(() -> {
        try {
          return trainWorker(threadIterations, playerCount, finalThreadId, startIteration);
        } finally {
          completionLatch.countDown();
        }
      });

      futures.add(future);
    }

    try {
      // Wait for all threads to complete
      completionLatch.await();

      // Collect results from all threads
      long totalWorkerIterations = 0;
      long totalWorkerTime = 0;

      for (int i = 0; i < futures.size(); i++) {
        try {
          TrainingResult result = futures.get(i).get();
          totalWorkerIterations += result.iterationsCompleted;
          totalWorkerTime += result.trainingTimeMs;

          log.info("Thread {} completed: {} iterations in {}ms ({:.2f} iter/sec)",
              i, result.iterationsCompleted, result.trainingTimeMs,
              (double) result.iterationsCompleted / (result.trainingTimeMs / 1000.0));

        } catch (ExecutionException e) {
          log.error("Training thread {} failed", i, e.getCause());
        }
      }

      totalIterationsCompleted.addAndGet(totalWorkerIterations);
      log.info("Parallel training completed: {} total iterations, {} total time",
          totalWorkerIterations, totalWorkerTime);

    } catch (InterruptedException e) {
      log.error("Training interrupted", e);
      Thread.currentThread().interrupt();
      shouldStopTraining = true;
    }
  }

  private void trainSequential(int iterations, int playerCount) {
    log.info("Running sequential MCCFR training with {} iterations", iterations);

    TrainingResult result = trainWorker(iterations, playerCount, 0, 1);
    totalIterationsCompleted.addAndGet(result.iterationsCompleted);

    log.info("Sequential training completed: {} iterations in {}ms",
        result.iterationsCompleted, result.trainingTimeMs);
  }


  public TrainingResult trainWorker(int iterations, int playerCount, int threadId, int startIteration) {
    long workerStartTime = System.currentTimeMillis();
    activeThreads.incrementAndGet();

    try{
      GameService gameService = threadLocalGameService.get();
      Random random = threadLocalRandom.get();

      log.debug("Worker thread {} starting: {} iterations from {}", threadId, iterations,
          startIteration);

      initializePlayersForWorker(gameService, playerCount, threadId);
      int completedIterations = 0;

      for (int t = startIteration; t < startIteration + iterations && !shouldStopTraining; t++) {
        for(Player player : gameService.getPlayers()){
          if (shouldStopTraining) {
            break;
          }

          int mainPlayerIndex = player.getPlayerIndex();

          if (t % STRATEGY_INTERVAL == 0) {
            for (CardState cardStateExplored : cardStateList) {
              for (Player playerAddHoleCards : gameService.getPlayers()) {
                playerAddHoleCards.addCardToHand(
                    cardStateExplored.getPlayerHoleCards(playerAddHoleCards.getPlayerIndex()).get(0));
                playerAddHoleCards.addCardToHand(
                    cardStateExplored.getPlayerHoleCards(playerAddHoleCards.getPlayerIndex()).get(1));
              }
              updateStrategy(new ArrayList<>(), mainPlayerIndex, gameService, random);
            }
          }

        }
      }
    }
    for (int t = 1; t <= iterations; t++) {
      for (Player player : gameService.getPlayers()) {
        int mainPlayerIndex = player.getPlayerIndex();

        // Update strategy on interval (only for preflop)
        if (t % STRATEGY_INTERVAL == 0) {
          for (CardState cardStateExplored : cardStateList) {
            for (Player playerAddHoleCards : gameService.getPlayers()) {
              playerAddHoleCards.addCardToHand(
                  cardStateExplored.getPlayerHoleCards(playerAddHoleCards.getPlayerIndex()).get(0));
              playerAddHoleCards.addCardToHand(
                  cardStateExplored.getPlayerHoleCards(playerAddHoleCards.getPlayerIndex()).get(1));
            }
            updateStrategy(new ArrayList<>(), mainPlayerIndex);
          }
        }

        // Choose traversal method based on pruning
        if (t > PRUNE_THRESHOLD) {
          double q = random.nextDouble();
          if (q < PRUNING_PROBABILITY) {
            traverseMCCFR(new ArrayList<>(), mainPlayerIndex, t);
          } else {
            traverseMCCFRP(new ArrayList<>(), mainPlayerIndex, t);
          }
        } else {
          traverseMCCFR(new ArrayList<>(), mainPlayerIndex, t);
        }
      }

      // Apply Linear CFR discounting
      if (t < LCFR_THRESHOLD && t % DISCOUNT_INTERVAL == 0) {
        float discount = (float) (t / DISCOUNT_INTERVAL) / ((float) (t / DISCOUNT_INTERVAL) + 1f);
        store.applyDiscount(t, DISCOUNT_INTERVAL, discount);
        log.debug("Applied Linear CFR discount {} at iteration {}", discount, t);
      }

      // MEMORY OPTIMIZATION: Periodic memory management
      if (t % MEMORY_CHECK_INTERVAL == 0) {
        performMemoryManagement(t);
      }

      // Progress logging with memory usage
      if (t % 1000 == 0) {
        long currentMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
        log.info("MCCFR training progress: {}/{} iterations completed, memory usage: {:.1f} MB", t,
            iterations, currentMemory / (1024.0 * 1024.0));
      }
    }

    log.info("MCCFR training completed after {} iterations", iterations);

    // MEMORY OPTIMIZATION: Final memory report
    printMemoryStatistics();
  }

  /**
   * MEMORY OPTIMIZATION: Perform memory management during training
   */
  private void performMemoryManagement(int currentIteration) {
    Runtime runtime = Runtime.getRuntime();
    long totalMemory = runtime.totalMemory();
    long freeMemory = runtime.freeMemory();
    long usedMemory = totalMemory - freeMemory;

    // Check if memory usage exceeds threshold
    if (usedMemory > MEMORY_THRESHOLD_BYTES) {
      log.warn("Memory usage ({:.1f} MB) exceeds threshold ({:.1f} MB) at iteration {}",
          usedMemory / (1024.0 * 1024.0), MEMORY_THRESHOLD_BYTES / (1024.0 * 1024.0),
          currentIteration);

      // Strategy 1: Prune infrequent infosets
      pruneInfrequentInfosets(3); // Remove infosets with < 3 visits

      // Strategy 2: Force garbage collection
      System.gc();

      // Check memory after cleanup
      long newUsedMemory = runtime.totalMemory() - runtime.freeMemory();
      long memoryFreed = usedMemory - newUsedMemory;

      if (memoryFreed > 0) {
        log.info("Memory cleanup freed {:.1f} MB", memoryFreed / (1024.0 * 1024.0));
      }
    }

    lastMemoryCheck = currentIteration;
  }

  /**
   * MEMORY OPTIMIZATION: Prune infrequent infosets to reduce memory usage
   */
  private void pruneInfrequentInfosets(int minVisits) {
    int totalPruned = 0;

    for (int p = 0; p < store.getNumPlayers(); p++) {
      var playerMap = store.getMapForPlayer(p);
      int sizeBefore = playerMap.size();

      // Remove infosets with low visit counts
      playerMap.values().removeIf(infoset -> infoset.getVisitCount() < minVisits);

      int sizeAfter = playerMap.size();
      int pruned = sizeBefore - sizeAfter;
      totalPruned += pruned;

      if (pruned > 0) {
        log.debug("Pruned {} infrequent infosets for player {} (visits < {})", pruned, p,
            minVisits);
      }
    }

    if (totalPruned > 0) {
      log.info("Memory optimization: pruned {} infrequent infosets total", totalPruned);
    }
  }

  /**
   * MEMORY OPTIMIZATION: Print comprehensive memory statistics
   */
  private void printMemoryStatistics() {
    Runtime runtime = Runtime.getRuntime();
    long totalMemory = runtime.totalMemory();
    long freeMemory = runtime.freeMemory();
    long usedMemory = totalMemory - freeMemory;
    long maxMemory = runtime.maxMemory();

    log.info("=== MEMORY USAGE STATISTICS ===");
    log.info("Used memory: {:.1f} MB", usedMemory / (1024.0 * 1024.0));
    log.info("Total memory: {:.1f} MB", totalMemory / (1024.0 * 1024.0));
    log.info("Max memory: {:.1f} MB", maxMemory / (1024.0 * 1024.0));
    log.info("Memory utilization: {:.1f}%", 100.0 * usedMemory / maxMemory);

    // InfosetStore statistics
    int totalInfosets = 0;
    for (int p = 0; p < store.getNumPlayers(); p++) {
      int playerInfosets = store.getMapForPlayer(p).size();
      totalInfosets += playerInfosets;
      log.info("Player {} infosets: {}", p, playerInfosets);
    }

    log.info("Total infosets created: {}", totalInfosets);
    log.info("Estimated InfosetStore memory: {:.1f} MB",
        StrategySerializer.estimateMemoryUsage(store) / (1024.0 * 1024.0));
  }

  /**
   * Standard MCCFR traversal
   */
  private double traverseMCCFR(List<ActionTrace> history, int playerId, int iteration) {
    gameService.initializeGameForCFR();

    // CRITICAL FIX: Initialize betting round before any actions
    initializeBettingRound();

    gameService.dealHoleCards();

    // CRITICAL FIX: Store initial hole cards for consistent restoration
    storeInitialGameState();

    // Post blinds at the start of each hand
    postBlinds();

    return traverseGame(history, playerId, iteration, false);
  }

  /**
   * MCCFR with pruning (MCCFR-P)
   */
  private double traverseMCCFRP(List<ActionTrace> history, int playerId, int iteration) {
    gameService.initializeGameForCFR();

    // CRITICAL FIX: Initialize betting round before any actions
    initializeBettingRound();

    gameService.dealHoleCards();

    // CRITICAL FIX: Store initial hole cards for consistent restoration
    storeInitialGameState();

    // Post blinds at the start of each hand
    postBlinds();

    return traverseGame(history, playerId, iteration, true);
  }

  /**
   * Core game tree traversal with Monte Carlo sampling
   */
  private double traverseGame(List<ActionTrace> history, int playerId, int iteration,
      boolean usePruning) {
    // FIXED: Check for round completion and advance if needed
    checkAndAdvanceRoundIfCompleted(history);

    if (isTerminal(history)) {
      if (!cardStateList.contains(new CardState(initialHoleCards, initialCommunityCards))) {
        cardStateList.add(new CardState(initialHoleCards, initialCommunityCards));
      }
      return getUtility(playerId, history);
    }

    if (!isPlayerInHand(playerId, history)) {
      // ENHANCED: Return contribution-based utility for folded players (consistent with
      // GameService.calculateUtility)
      return getFoldedPlayerUtility(playerId);
    }

    // Handle chance nodes (card dealing)
    if (isChanceNode(history)) {
      return handleChanceNode(history, playerId, iteration, usePruning);
    }

    int currentPlayer = getCurrentPlayer(history);

    if (currentPlayer == playerId) {
      return handlePlayerNode(history, playerId, iteration, usePruning);
    } else {
      return handleOpponentNode(history, playerId, iteration, usePruning);
    }
  }

  /**
   * Handle player's decision node
   */
  private double handlePlayerNode(List<ActionTrace> history, int playerId, int iteration,
      boolean usePruning) {
    Player player = getPlayerSafely(playerId);
    if (player == null) {
      log.warn("Player {} not found in handlePlayerNode, returning 0 utility", playerId);
      return 0.0;
    }

    // Create information set
    // CRITICAL FIX: Use getHand() which contains the player's hole cards in this implementation
    Infoset infoset = Infoset.of(player.getHand(), gameService.getCommunityCards(),
        getCurrentRound(history), player, gameService.getCurrentRoundBetAmount(), history);

    InfosetValue infosetValue = store.getOrCreate(playerId,
        InfosetStoreKey.abstractInfoset(infoset));
    float[] strategy = infosetValue.calculateStrategy();
    float[] regrets = infosetValue.getRegret();

    double nodeUtility = 0.0;
    int numActions = Math.min(strategy.length, AbstractAction.values().length);
    double[] actionUtilities = new double[numActions];
    boolean[] explored = new boolean[numActions];

    // Explore actions
    for (int i = 0; i < numActions; i++) {
      // Apply pruning if enabled
      if (usePruning && regrets[i] <= REGRET_THRESHOLD) {
        explored[i] = false;
        continue;
      }

      AbstractAction action = AbstractAction.values()[i];

      // Check if action is legal for this player
      if (!isActionLegal(player, action)) {
        explored[i] = false;
        continue;
      }

      // Sample this action
      List<ActionTrace> newHistory = new ArrayList<>(history);
      int actingPlayerIndex = getCurrentActingPlayer();
      newHistory.add(
          new ActionTrace(playerId, action, getCurrentRound(history).ordinal(), history.size(),
              actingPlayerIndex));

      // Execute action in game state
      executeAction(player, action);

      // Recursively traverse
      double actionUtility = traverseGame(newHistory, playerId, iteration, usePruning);

      actionUtilities[i] = actionUtility;
      nodeUtility += strategy[i] * actionUtility;
      explored[i] = true;

      // Restore game state
      restoreGameState(history);
    }

    // Update regrets for explored actions
    for (int i = 0; i < numActions; i++) {
      if (explored[i]) {
        float regretDelta = (float) (actionUtilities[i] - nodeUtility);
        infosetValue.addRegret(i, regretDelta);
      }
    }

    return nodeUtility;
  }

  /**
   * Handle opponent's decision node - sample according to their strategy
   */
  private double handleOpponentNode(List<ActionTrace> history, int playerId, int iteration,
      boolean usePruning) {
    int currentPlayer = getCurrentPlayer(history);
    Player opponent = getPlayerSafely(currentPlayer);
    if (opponent == null) {
      log.warn("Opponent {} not found in handleOpponentNode, returning 0 utility", currentPlayer);
      return 0.0;
    }

    // Create opponent's information set
    // CRITICAL FIX: Use getHand() which contains the player's hole cards in this implementation
    Infoset opponentInfoset = Infoset.of(opponent.getHand(), gameService.getCommunityCards(),
        getCurrentRound(history), opponent, gameService.getCurrentRoundBetAmount(), history);

    InfosetValue opponentInfosetValue = store.getOrCreate(currentPlayer,
        InfosetStoreKey.abstractInfoset(opponentInfoset));
    float[] opponentStrategy = opponentInfosetValue.calculateStrategy();

    // Sample opponent's action
    AbstractAction opponentAction = sampleAction(opponentStrategy);

    // Execute opponent's action
    executeAction(opponent, opponentAction);

    // Continue traversal
    List<ActionTrace> newHistory = new ArrayList<>(history);
    // CRITICAL FIX: Opponent action should be attributed to the opponent, not the main player
    newHistory.add(new ActionTrace(playerId, opponentAction, getCurrentRound(history).ordinal(),
        history.size(), currentPlayer));

    return traverseGame(newHistory, playerId, iteration, usePruning);
  }

  /**
   * Update strategy following Pluribus UPDATE-STRATEGY algorithm Only tracks strategy on preflop
   * (betting_round == 0)
   * <p>
   * Based on Algorithm from pluribus_supplementary.md: Function UPDATE-STRATEGY(h, Pi): if h is
   * terminal or Pi not in hand or betting_round(h) > 0: return  // Track average strategy only on
   * first betting round if h is a chance node: a ∼ σ(h) UPDATE-STRATEGY(h · a, Pi) else if P(h) ==
   * Pi: Ii ← Ii(h) σ(Ii) ← CALCULATE-STRATEGY(R(Ii), Ii) a ∼ σ(Ii) φ(Ii, a) ← φ(Ii, a) + 1
   * UPDATE-STRATEGY(h · a, Pi) else: for a ∈ A(h): UPDATE-STRATEGY(h · a, Pi) STACKOVERFLOW FIX:
   * Added comprehensive recursion protection
   */
  private void updateStrategy(List<ActionTrace> history, int playerId) {
    // STACKOVERFLOW FIX: Prevent infinite recursion with depth tracking
    checkAndAdvanceRoundIfCompleted(history);
    updateStrategyRecursionDepth++;

    if(getCurrentRound(history)==null){
      storeInitialGameState();
      gameService.setCurrentRound(BettingRound.PREFLOP);
      postBlinds();
    }

    try {
      // Check for excessive recursion depth
      if (updateStrategyRecursionDepth > MAX_UPDATE_STRATEGY_RECURSION_DEPTH) {
        log.warn(
            "🚨 STACKOVERFLOW PREVENTION: updateStrategy() recursion depth exceeded {} for player {}, history size: {}",
            MAX_UPDATE_STRATEGY_RECURSION_DEPTH, playerId, history.size());
        log.warn("🔍 Terminating recursion to prevent StackOverflowError");
        return;
      }

      // Additional safety check for circular recursion
      if (updateStrategyInProgress
          && updateStrategyRecursionDepth > 20) { // INCREASED: Allow more legitimate recursion
        log.warn(
            "🚨 CIRCULAR RECURSION DETECTED: updateStrategy() called recursively {} times for player {}",
            updateStrategyRecursionDepth, playerId);
        log.warn("🔍 Breaking recursion cycle to prevent infinite loop");
        return;
      }

      updateStrategyInProgress = true;

      // Terminal check or player not in hand
      if (isTerminal(history) || !isPlayerInHand(playerId, history)) {
        log.debug("✅ updateStrategy() terminating: terminal={}, playerInHand={}, depth={}",
            isTerminal(history), isPlayerInHand(playerId, history), updateStrategyRecursionDepth);
        return;
      }
      // Handle chance nodes (card dealing)
      if (isChanceNode(history)) {
        log.debug("🎲 updateStrategy() handling chance node, depth={}",
            updateStrategyRecursionDepth);
        // Sample action from chance distribution and continue
        gameService.nextRoundAndDealNextCards();
        updateStrategy(history, playerId); // RECURSIVE CALL 1
        return;
      }

      // Pluribus: Only track strategy on preflop (betting_round == 0)
      BettingRound currentRound = getCurrentRound(history);
      if (currentRound != BettingRound.PREFLOP) {
        log.debug("✅ updateStrategy() terminating: not PREFLOP round ({}), depth={}", currentRound,
            updateStrategyRecursionDepth);
        return;
      }

      int currentPlayer = getCurrentActingPlayer();
      log.debug("🎯 updateStrategy() processing player turn: current={}, target={}, depth={}",
          currentPlayer, playerId, updateStrategyRecursionDepth);

      if (currentPlayer == playerId) {
        // Player's turn - update strategy accumulation
        updateStrategyForPlayer(history, playerId); // Contains RECURSIVE CALL 2
      } else {
        // Opponent's turn - explore all actions
        updateStrategyForOpponent(history, playerId); // Contains RECURSIVE CALL 3
      }
    } finally {
      // STACKOVERFLOW FIX: Always decrement depth and clear flag
      updateStrategyRecursionDepth--;
      if (updateStrategyRecursionDepth == 0) {
        updateStrategyInProgress = false;
        log.debug("✅ updateStrategy() recursion completed, depth reset to 0");
      }
    }
  }

  /**
   * Update strategy when it's the target player's turn
   */
  private void updateStrategyForPlayer(List<ActionTrace> history, int playerId) {
    Player player = getPlayerSafely(playerId);
    if (player == null) {
      log.warn("Player {} not found in updateStrategyForPlayer, skipping", playerId);
      return;
    }
    BettingRound currentRound = getCurrentRound(history);

    // Create information set
    // CRITICAL FIX: Use getHand() which contains the player's hole cards in this implementation
    Infoset infoset = Infoset.of(player.getHand(), gameService.getCommunityCards(), currentRound,
        player, gameService.getCurrentRoundBetAmount(), history);

    InfosetValue infosetValue = store.getOrCreate(playerId,
        InfosetStoreKey.abstractInfoset(infoset));

    // Calculate current strategy using regret matching
    float[] strategy = infosetValue.calculateStrategy();

    // Sample action according to current strategy
    AbstractAction sampledAction = sampleAction(strategy);

    // Accumulate strategy: φ(Ii, a) ← φ(Ii, a) + 1
    float[] actionCounter = infosetValue.getActionCounter();
    actionCounter[sampledAction.ordinal()] += 1.0f;
    infosetValue.setActionCounter(actionCounter);

    // Execute the sampled action and continue recursion
    executeAction(player, sampledAction);

    List<ActionTrace> newHistory = new ArrayList<>(history);
    int actingPlayerIndex = getCurrentActingPlayer();
    // CRITICAL FIX: Action should be attributed to the acting player, not the main player
    newHistory.add(new ActionTrace(playerId, sampledAction, currentRound.ordinal(), history.size(),
        playerId));

    updateStrategy(newHistory, playerId);

    // Restore game state for next iteration
//    restoreGameState(history);
  }

  /**
   * Update strategy when it's an opponent's turn Explores all possible opponent actions
   */
  private void updateStrategyForOpponent(List<ActionTrace> history, int playerId) {
    int currentPlayerId = getCurrentActingPlayer();
    Player opponent = getPlayerSafely(currentPlayerId);
    if (opponent == null) {
      log.warn("Opponent {} not found in updateStrategyForOpponent, skipping", currentPlayerId);
      return;
    }
    BettingRound currentRound = getCurrentRound(history);

    // Get all legal actions for the opponent
    List<AbstractAction> legalActions = getLegalActions(opponent);

    // Explore all opponent actions
    for (AbstractAction action : legalActions) {
      // Execute opponent action
      executeAction(opponent, action);

      // CRITICAL FIX: DO NOT call advanceToNextPlayer() here!
      // executeAction() already handles turn advancement through updateActivePlayersList()
      // Additional advancement causes double-advancement bug (Bob folds → skips Charlie → goes to Alice)

      List<ActionTrace> newHistory = new ArrayList<>(history);
      // CRITICAL FIX: Opponent action should be attributed to the opponent, not the main player
      newHistory.add(
          new ActionTrace(playerId, action, currentRound.ordinal(), history.size(), currentPlayerId));

      // Continue recursion with this action
      updateStrategy(newHistory, playerId);

      // Restore game state for next action
      restoreGameState(history);
    }
  }

  /**
   * Get legal actions for a player in the current game state
   */
  private List<AbstractAction> getLegalActions(Player player) {
    List<AbstractAction> legalActions = new ArrayList<>();
    int currentRoundBetAmount = gameService.getCurrentRoundBetAmount();
    int playerChips = player.getChips();
    int playerCurrentBet = player.getCurrentBet();

    // Fold is always legal
    legalActions.add(AbstractAction.FOLD);
    legalActions.add(AbstractAction.CHECK_OR_CALL);

    // Betting/Raising logic - only if player has chips beyond call amount
    int callAmount = Math.max(0, currentRoundBetAmount - playerCurrentBet);
    if (playerChips > callAmount) {
      // Add bet/raise actions based on available chips and pot size
      int potSize = gameService.getTotalPotAmount();

      if (canAffordBet(player, potSize, 0.3f, callAmount)) {
        legalActions.add(AbstractAction.BET_OR_RAISE_30);
      }
      if (canAffordBet(player, potSize, 0.6f, callAmount)) {
        legalActions.add(AbstractAction.BET_OR_RAISE_60);
      }
      if (canAffordBet(player, potSize, 1.0f, callAmount)) {
        legalActions.add(AbstractAction.BET_OR_RAISE_100);
      }
      if (canAffordBet(player, potSize, 2.0f, callAmount)) {
        legalActions.add(AbstractAction.BET_OR_RAISE_200);
      }
      if (canAffordBet(player, potSize, 5.0f, callAmount)) {
        legalActions.add(AbstractAction.BET_OR_RAISE_500);
      }
    }

    return legalActions;
  }

  /**
   * Check if player can afford a bet with given multiplier
   */
  private boolean canAffordBet(Player player, int potSize, float multiplier, int callAmount) {
    int raiseAmount = (int) (potSize * multiplier);
    int totalRequired = callAmount + raiseAmount;
    return player.getChips() >= totalRequired && totalRequired > callAmount;
  }

  /**
   * Check if an action is legal for a player in the current game state
   */
  private boolean isActionLegal(Player player, AbstractAction action) {

    List<AbstractAction> legalActions = getLegalActions(player);
    return legalActions.contains(action);
  }

  // Helper methods
  private AbstractAction sampleAction(float[] strategy) {
    double cumulative = 0.0;
    double random = this.random.nextDouble();

    for (int i = 0; i < strategy.length; i++) {
      cumulative += strategy[i];
      if (random < cumulative) {
        return AbstractAction.values()[i];
      }
    }
    return AbstractAction.values()[strategy.length - 1];
  }

  private boolean isTerminal(List<ActionTrace> history) {
    // STACKOVERFLOW FIX: Add safety check to prevent infinite terminal loops
    try {
      // Check if game has reached terminal state
      boolean isComplete = gameService.isGameComplete();
      log.debug("🏁 isTerminal() check: gameComplete={}, historySize={}, depth={}", isComplete,
          history.size(), updateStrategyRecursionDepth);

      // Safety check: if history is getting too long, force terminal
      if (history.size() > 100) {
        log.warn("🚨 TERMINAL SAFETY: History size {} exceeded limit, forcing terminal=true",
            history.size());
        return true;
      }

      return isComplete;
    } catch (Exception e) {
      log.error("🚨 Exception in isTerminal(): {}, returning true to prevent recursion",
          e.getMessage());
      return true; // Force termination on exception
    }
  }

  private boolean isPlayerInHand(int playerId, List<ActionTrace> history) {
    // STACKOVERFLOW FIX: Add safety check to prevent infinite player state loops
    try {
      Player player = getPlayerSafely(playerId);
      boolean inHand = player != null && player.isActive();
      log.debug("👤 isPlayerInHand() check: player={}, active={}, inHand={}, depth={}", playerId,
          player != null ? player.isActive() : "null", inHand, updateStrategyRecursionDepth);
      return inHand;
    } catch (Exception e) {
      log.error("🚨 Exception in isPlayerInHand(): {}, returning false to prevent recursion",
          e.getMessage());
      return false; // Force player out of hand on exception
    }
  }

  private boolean isChanceNode(List<ActionTrace> history) {
    // STACKOVERFLOW FIX: Add safety check to prevent infinite chance node loops
    try {
      // Determine if we need to deal cards
      boolean needsCards = gameService.needsToDealCards();
      log.debug("🎲 isChanceNode() check: needsCards={}, historySize={}, depth={}", needsCards,
          history.size(), updateStrategyRecursionDepth);

      // Safety check: if we've been in chance nodes too many times, force termination
      if (needsCards && updateStrategyRecursionDepth
          > 50) { // INCREASED: Allow more legitimate chance node processing
        log.warn(
            "🚨 CHANCE NODE LOOP PREVENTION: Too many chance node calls at depth {}, forcing false",
            updateStrategyRecursionDepth);
        return false;
      }

      return needsCards;
    } catch (Exception e) {
      log.error("🚨 Exception in isChanceNode(): {}, returning false to prevent recursion",
          e.getMessage());
      return false;
    }
  }

  private double handleChanceNode(List<ActionTrace> history, int playerId, int iteration,
      boolean usePruning) {
    // Deal cards and continue
    gameService.nextRoundAndDealNextCards();
    return traverseGame(history, playerId, iteration, usePruning);
  }

  /**
   * Get current player from history or turn management system CRITICAL FIX: Properly implemented to
   * support MCCFR algorithm
   */
  private int getCurrentPlayer(List<ActionTrace> history) {
    // Use the turn management system for current player
    return getCurrentActingPlayer();
  }

  private BettingRound getCurrentRound(List<ActionTrace> history) {
    return gameService.getCurrentBettingRound();
  }

  private void executeAction(Player player, AbstractAction action) {
    // CRITICAL FIX: Add null safety at the beginning of executeAction
    if (player == null) {
      log.error("🚨 CRITICAL: executeAction() called with NULL player for action {}", action);
      log.error("🔍 This indicates a bug in the calling method - action will be skipped");
      Thread.dumpStack(); // Print stack trace to identify the source
      return; // Gracefully skip the action to prevent crashes
    }

    if (action == null) {
      log.error("🚨 CRITICAL: executeAction() called with NULL action for player {}",
          player.getName());
      return;
    }

    log.debug("Executing action {} for player {} (chips: {}, bet: {})", action, player.getName(),
        player.getChips(), player.getCurrentBet());

    // CRITICAL FIX: Add null safety for getCurrentBettingRound()
    BettingRound currentRound = gameService.getCurrentBettingRound();
    if (currentRound == null) {
      log.warn("Current betting round is null, initializing PREFLOP for action execution");
      initializeBettingRound();
      currentRound = gameService.getCurrentBettingRound();

      if (currentRound == null) {
        log.error("Failed to initialize betting round, defaulting to PREFLOP for ActionTrace");
        currentRound = BettingRound.PREFLOP;
      }
    }

    // Use the GameService's executeAbstractAction method via restoreGameState
    // For now, create a simple action trace and let GameService handle it
    List<ActionTrace> singleAction = List.of(
        new ActionTrace(player.getPlayerIndex(), action, currentRound.ordinal(), 0,
            player.getPlayerIndex()));

    // CRITICAL FIX: Track that this player has acted in the current round
    if (currentRound != null) {
      playersActedInRound.computeIfAbsent(currentRound, k -> new HashSet<>())
          .add(player.getPlayerIndex());
      log.debug("Tracked action for player {} in round {}", player.getPlayerIndex(), currentRound);
    }

    // Use pot-based betting
    // FIXED: Correct betting logic for raises
    float multiplier = getMultiplierForAction(action);
    int potSize = gameService.getTotalPotAmount();
    int currentRoundBetAmount = gameService.getCurrentRoundBetAmount();

    // Calculate proper bet amount: call amount + raise amount
    int callAmount = currentRoundBetAmount - player.getCurrentBet();
    int raiseAmount = (int) (potSize * multiplier);
    int totalBetAmount;
    float allInThreshold;

    // Execute the action directly using GameService methods
    switch (action) {
      case FOLD:
        gameService.foldPlayer(player);
        break;
      case CHECK_OR_CALL:
        if (gameService.getCurrentRoundBetAmount() == 0) {
          // Check - no action needed
        } else {
          // Call
          if (callAmount <= player.getChips()) {
            gameService.placeBet(player, callAmount);
          } else {
            gameService.placeBet(player, player.getChips()); // All-in
          }
        }
        break;
      case BET_OR_RAISE_30:
      case BET_OR_RAISE_60:
      case BET_OR_RAISE_100:
      case BET_OR_RAISE_200:
      case BET_OR_RAISE_500:
        totalBetAmount = callAmount + raiseAmount;
        allInThreshold = getAllInThreshold(action);

        if (totalBetAmount > player.getChips() || (player.getChips() <= allInThreshold * potSize)) {
          totalBetAmount = player.getChips();
        }

        gameService.placeBet(player, totalBetAmount);
        break;

      case SMALL_BLIND:
        gameService.placeBet(player, SMALL_BLIND_AMOUNT);
        break;
      case BIG_BLIND:
        gameService.placeBet(player, BIG_BLIND_AMOUNT);
        break;

    }

    // Update active players list if player folded or went all-in
    if (action == AbstractAction.FOLD) {
      updateActivePlayersList(); // This already handles turn advancement correctly
    } else if (action == AbstractAction.CHECK_OR_CALL && player.getChips() < callAmount) {
      player.setAllIn(true);
      updateActivePlayersList();

    } else if ((action == AbstractAction.BET_OR_RAISE_30 || action == AbstractAction.BET_OR_RAISE_60
        || action == AbstractAction.BET_OR_RAISE_100 || action == AbstractAction.BET_OR_RAISE_200
        || action == AbstractAction.BET_OR_RAISE_500) && (player.getChips()
        <= getAllInThreshold(action) * potSize)) {
      player.setAllIn(true);
      updateActivePlayersList();

    } else if (action == AbstractAction.SMALL_BLIND || action == AbstractAction.BIG_BLIND){
      updateActivePlayersList();
    }else {
      // For non-fold, non-all-in actions, advance to next player normally
      advanceToNextPlayer();
    }

    // CRITICAL FIX: Only advance turn for non-fold, non-all-in actions
    // For fold and all-in actions, updateActivePlayersList() already handles turn advancement
    // Additional advanceToNextPlayer() call was causing double-advancement bug
  }

  private float getAllInThreshold(AbstractAction action) {
    return switch (action) {
      case BET_OR_RAISE_30 -> 0.45f;
      case BET_OR_RAISE_60 -> 0.8f;
      case BET_OR_RAISE_100 -> 1.5f;
      case BET_OR_RAISE_200 -> 3.5f;
      case BET_OR_RAISE_500 -> 7.0f;
      default -> 0.0f; // I set 0.0f for non-bet actions
    };
  }

  private float getMultiplierForAction(AbstractAction action) {
    return switch (action) {
      case BET_OR_RAISE_30 -> 0.3f;
      case BET_OR_RAISE_60 -> 0.6f;
      case BET_OR_RAISE_100 -> 1.0f;
      case BET_OR_RAISE_200 -> 2.0f;
      case BET_OR_RAISE_500 -> 5.0f;
      default -> 1.0f;
    };
  }

  /**
   * Restore game state from action history with comprehensive blind handling CRITICAL FIX: Properly
   * handles blind posting and maintains consistent hole cards ENHANCED: Synchronizes GameService
   * betting round state with action history STACKOVERFLOW FIX: Added recursion protection for game
   * state restoration
   */
  private void restoreGameState(List<ActionTrace> history) {
    // CRITICAL FIX: Prevent infinite recursion in game state restoration
    gameStateRestorationDepth++;

    try {
      // Check for excessive restoration depth
      if (gameStateRestorationDepth > MAX_GAME_STATE_RESTORATION_DEPTH) {
        log.warn(
            "🚨 GAME STATE RESTORATION LIMIT: Restoration depth exceeded {} for history size {}",
            MAX_GAME_STATE_RESTORATION_DEPTH, history.size());
        log.warn("🔍 Skipping restoration to prevent infinite recursion");
        return;
      }

      // Additional safety check for circular restoration
      if (restoringGameState) {
        log.warn(
            "🚨 CIRCULAR GAME STATE RESTORATION DETECTED: Already restoring game state, depth={}",
            gameStateRestorationDepth);
        log.warn("🔍 Skipping nested restoration to prevent infinite loop");
        return;
      }

      restoringGameState = true;

      log.debug("Restoring game state from {} actions (depth={})", history.size(),
          gameStateRestorationDepth);

      // Step 1: Reset to initial game state
      gameService.initializeGameForCFR();
      initializeBettingRound();

      // CRITICAL FIX: Restore original hole cards instead of dealing new ones
      restoreInitialGameState();

      // Step 2: Reset MCCFRTrainer internal state
      resetInternalState();

      // Step 3: Replay action history with proper blind handling
      replayActionHistory(history);

      log.debug("✅ Game state restoration completed successfully (depth={})",
          gameStateRestorationDepth);
    } finally {
      // CRITICAL FIX: Always clear flags and decrement depth
      restoringGameState = false;
      gameStateRestorationDepth--;
      if (gameStateRestorationDepth == 0) {
        log.debug("✅ All game state restorations completed, depth reset to 0");
      }
    }

    // Step 4: CRITICAL FIX - Synchronize GameService betting round with action history
    synchronizeBettingRoundState(history);

    log.debug("Game state restoration completed with synchronized betting round");
  }

  /**
   * Reset MCCFRTrainer internal state for restoration CRITICAL FIX: Ensures consistent internal
   * state during restoration ENHANCED: Resets betting round action tracking to prevent premature
   * round advancement
   */
  private void resetInternalState() {
    // Reset dealer button to initial position
    dealerButtonPosition = 0;

    // Reset player turn management
    initializePlayerTurnManagement();

    // Reset current acting player to first player
    currentActingPlayer = 0;

    // CRITICAL FIX: Reset betting round action tracking
    playersActedInRound.clear();
    currentTraversalRound = BettingRound.PREFLOP;

    log.debug("Reset internal state: dealer at {}, acting player {}, cleared action tracking",
        dealerButtonPosition, currentActingPlayer);
  }

  /**
   * Replay action history with proper blind handling and state management CRITICAL FIX: Handles
   * blind posting and maintains consistent game state ENHANCED: Rebuilds action tracking state to
   * prevent betting round regression
   */
  private void replayActionHistory(List<ActionTrace> history) {
    if (history.isEmpty()) {
      // No history to replay, but we still need to post blinds for initial state
      postBlinds();
      return;
    }

    log.debug("Replaying {} actions from history", history.size());

    // Step 1: Rebuild action tracking state from history
    rebuildActionTrackingFromHistory(history);

    // Step 2: Post blinds (they may not be in history)
    boolean blindsInHistory = hasBlindActionsInHistory(history);
    if (!blindsInHistory) {
      log.debug("No blind actions in history, posting blinds first");
      postBlinds();
    }

    // Step 3: Replay each action in sequence
    for (int i = 0; i < history.size(); i++) {
      ActionTrace actionTrace = history.get(i);
      replayAction(actionTrace, i);
    }

    log.debug("Action history replay completed with rebuilt action tracking");
  }

  /**
   * Rebuild action tracking state from action history CRITICAL FIX: Prevents betting round
   * regression during state restoration
   */
  private void rebuildActionTrackingFromHistory(List<ActionTrace> history) {
    log.debug("Rebuilding action tracking from {} actions", history.size());

    // Clear existing tracking
    playersActedInRound.clear();

    // Rebuild tracking by analyzing each action in history
    for (ActionTrace actionTrace : history) {
      int roundOrdinal = actionTrace.getRound();
      int actingPlayerIndex = actionTrace.getActingPlayerIndex();

      // Convert round ordinal to BettingRound enum
      BettingRound round = BettingRound.values()[Math.min(roundOrdinal,
          BettingRound.values().length - 1)];

      // Track that this player acted in this round
      playersActedInRound.computeIfAbsent(round, k -> new HashSet<>()).add(actingPlayerIndex);

      log.debug("Rebuilt tracking: Player {} acted in round {} (ordinal {})", actingPlayerIndex,
          round, roundOrdinal);
    }

    // Log final state of action tracking
    if (log.isDebugEnabled()) {
      for (Map.Entry<BettingRound, Set<Integer>> entry : playersActedInRound.entrySet()) {
        log.debug("Action tracking rebuilt - Round {}: players {} have acted", entry.getKey(),
            entry.getValue());
      }
    }

    // Set current traversal round based on the latest action in history
    if (!history.isEmpty()) {
      ActionTrace lastAction = history.get(history.size() - 1);
      int lastRoundOrdinal = lastAction.getRound();
      currentTraversalRound = BettingRound.values()[Math.min(lastRoundOrdinal,
          BettingRound.values().length - 1)];
      log.debug("Set current traversal round to {} based on last action", currentTraversalRound);
    }
  }

  /**
   * Synchronize GameService betting round state with action history CRITICAL FIX: Ensures
   * GameService betting round matches the round from action history
   */
  private void synchronizeBettingRoundState(List<ActionTrace> history) {
    if (history.isEmpty()) {
      log.debug("No history to synchronize, GameService remains at PREFLOP");
      return;
    }

    // Determine the target betting round from the last action in history
    ActionTrace lastAction = history.get(history.size() - 1);
    int targetRoundOrdinal = lastAction.getRound();
    BettingRound targetRound = BettingRound.values()[Math.min(targetRoundOrdinal,
        BettingRound.values().length - 1)];

    // Get current GameService betting round
    BettingRound currentGameServiceRound = gameService.getCurrentBettingRound();

    log.debug("Synchronizing betting round - Target: {} (ordinal {}), Current GameService: {}",
        targetRound, targetRoundOrdinal, currentGameServiceRound);

    // If GameService round doesn't match target, advance it step by step
    if (currentGameServiceRound != targetRound) {
      advanceGameServiceToTargetRound(currentGameServiceRound, targetRound);
    }

    // Verify synchronization
    BettingRound finalGameServiceRound = gameService.getCurrentBettingRound();
    if (finalGameServiceRound == targetRound) {
      log.debug("Betting round synchronization successful - GameService: {}, MCCFRTrainer: {}",
          finalGameServiceRound, currentTraversalRound);
    } else {
      log.warn(
          "Betting round synchronization failed - GameService: {}, Target: {}, MCCFRTrainer: {}",
          finalGameServiceRound, targetRound, currentTraversalRound);
    }
  }

  /**
   * Advance GameService betting round to match target round CRITICAL FIX: Ensures proper round
   * progression with card dealing
   */
  private void advanceGameServiceToTargetRound(BettingRound currentRound,
      BettingRound targetRound) {
    if (currentRound == null) {
      currentRound = BettingRound.PREFLOP;
    }

    log.debug("Advancing GameService from {} to {}", currentRound, targetRound);

    // Advance step by step to ensure proper card dealing
    BettingRound workingRound = currentRound;
    while (workingRound != targetRound && workingRound != BettingRound.SHOWDOWN) {
      BettingRound nextRound = workingRound.getNextRound();

      log.debug("Advancing GameService from {} to {}", workingRound, nextRound);

      // Use GameService's dealNextCards() which handles round advancement and card dealing
      try {
        gameService.nextRoundAndDealNextCards();
        workingRound = gameService.getCurrentBettingRound();

        log.debug("GameService advanced to {}", workingRound);

        if (workingRound == targetRound) {
          break;
        }
      } catch (Exception e) {
        log.error("Failed to advance GameService betting round from {} to {}: {}", workingRound,
            nextRound, e.getMessage());
        break;
      }
    }

    // Final verification
    BettingRound finalRound = gameService.getCurrentBettingRound();
    if (finalRound != targetRound) {
      log.warn("Failed to advance GameService to target round {} - final round: {}", targetRound,
          finalRound);
    }
  }

  /**
   * Check if history contains blind actions CRITICAL FIX: Use consistent ActionTrace method calls
   */
  private boolean hasBlindActionsInHistory(List<ActionTrace> history) {
    return history.stream().anyMatch(
        trace -> trace.getActionAbstract() == AbstractAction.SMALL_BLIND
            || trace.getActionAbstract() == AbstractAction.BIG_BLIND);
  }

  /**
   * Replay a single action from history with proper state management CRITICAL FIX: Handles blind
   * actions and maintains turn order
   */
  private void replayAction(ActionTrace actionTrace, int actionIndex) {
    AbstractAction action = actionTrace.getActionAbstract();
    int playerId = actionTrace.getActingPlayerIndex();

    log.debug("Replaying action {}: Player {} performs {}", actionIndex, playerId, action);

    // Get the player who performed this action
    Player player = getPlayerSafely(playerId);
    if (player == null) {
      log.error("Cannot replay action {}: Player {} not found", actionIndex, playerId);
      return;
    }

    // Handle blind actions specially during restoration
    if (action == AbstractAction.SMALL_BLIND || action == AbstractAction.BIG_BLIND) {
      replayBlindAction(player, action, actionIndex);
    } else {
      // Regular action replay
      replayRegularAction(player, action, actionIndex);
    }
  }

  /**
   * Replay blind action with proper dealer button and turn management CRITICAL FIX: Maintains
   * dealer button position during blind restoration
   */
  private void replayBlindAction(Player player, AbstractAction action, int actionIndex) {
    log.debug("Replaying blind action: {} posts {}", player.getName(), action);

    // Execute the blind action directly (don't use executeAction to avoid turn advancement)
    if (action == AbstractAction.SMALL_BLIND) {
      gameService.placeBet(player, SMALL_BLIND_AMOUNT);
      log.debug("Restored small blind: {} ({} chips)", player.getName(), SMALL_BLIND_AMOUNT);
    } else if (action == AbstractAction.BIG_BLIND) {
      gameService.placeBet(player, BIG_BLIND_AMOUNT);
      log.debug("Restored big blind: {} ({} chips)", player.getName(), BIG_BLIND_AMOUNT);
    }

    // Update dealer button position based on blind positions
    updateDealerButtonFromBlindAction(player, action);
  }

  /**
   * Replay regular (non-blind) action with proper turn management
   */
  private void replayRegularAction(Player player, AbstractAction action, int actionIndex) {
    log.debug("Replaying regular action: {} performs {}", player.getName(), action);

    // Set current acting player to match the action
    setCurrentActingPlayer(player.getPlayerIndex());

    // Execute the action (this will advance turn automatically)
    executeActionForReplay(player, action);
  }

  /**
   * Update dealer button position based on blind action during restoration
   */
  private void updateDealerButtonFromBlindAction(Player player, AbstractAction action) {
    List<Player> allPlayers = gameService.getPlayers();
    int playerPosition = player.getPlayerIndex();

    if (action == AbstractAction.SMALL_BLIND) {
      // Small blind is dealer+1 (or dealer in heads-up)
      if (allPlayers.size() == 2) {
        dealerButtonPosition = playerPosition; // Dealer posts small blind in heads-up
      } else {
        dealerButtonPosition = (playerPosition - 1 + allPlayers.size()) % allPlayers.size();
      }
    } else if (action == AbstractAction.BIG_BLIND) {
      // Big blind is dealer+2 (or dealer+1 in heads-up)
      if (allPlayers.size() == 2) {
        dealerButtonPosition = (playerPosition - 1 + allPlayers.size()) % allPlayers.size();
      } else {
        dealerButtonPosition = (playerPosition - 2 + allPlayers.size()) % allPlayers.size();
      }
    }

    log.debug("Updated dealer button to position {} based on {} from player {}",
        dealerButtonPosition, action, player.getName());
  }

  /**
   * Set current acting player for action replay
   */
  private void setCurrentActingPlayer(int playerId) {
    // Find the player in the active players list
    for (int i = 0; i < activePlayers.size(); i++) {
      if (activePlayers.get(i).getPlayerIndex() == playerId) {
        currentActingPlayer = i;
        log.debug("Set current acting player to {} ({})", playerId, activePlayers.get(i).getName());
        return;
      }
    }
    log.warn("Could not find player {} in active players list for turn setting", playerId);
  }

  /**
   * Execute action during replay with all-in handling and chip validation CRITICAL FIX: Handles
   * insufficient chips and all-in scenarios
   */
  private void executeActionForReplay(Player player, AbstractAction action) {
    // Execute the action logic without calling advanceToNextPlayer()
    switch (action) {
      case FOLD:
        gameService.foldPlayer(player);
        log.debug("Player {} folded during replay", player.getName());
        break;
      case CHECK_OR_CALL:
        int callAmount = Math.max(0,
            gameService.getCurrentRoundBetAmount() - player.getCurrentBet());
        if (callAmount > 0) {
          // CRITICAL FIX: Handle all-in scenario for call
          int actualCallAmount = Math.min(callAmount, player.getChips());
          if (actualCallAmount < callAmount) {
            log.debug("Player {} going all-in with {} chips (call amount was {})", player.getName(),
                actualCallAmount, callAmount);
          }
          gameService.placeBet(player, actualCallAmount);
          log.debug("Player {} called {} during replay", player.getName(), actualCallAmount);
        } else {
          log.debug("Player {} checked during replay", player.getName());
        }
        break;
      case BET_OR_RAISE_30:
      case BET_OR_RAISE_60:
      case BET_OR_RAISE_100:
      case BET_OR_RAISE_200:
      case BET_OR_RAISE_500:
        float multiplier = getMultiplierForAction(action);
        int potSize = gameService.getTotalPotAmount();
        int raiseAmount = (int) (potSize * multiplier);
        int totalBet = Math.max(0, gameService.getCurrentRoundBetAmount() - player.getCurrentBet())
            + raiseAmount;
        float allInThreshold = getAllInThreshold(action);

        if (player.getChips() < totalBet || player.getChips() <= allInThreshold * potSize) {
          totalBet = player.getChips();
          log.debug("Player {} going all-in with {} chips (bet amount was {})", player.getName(),
              player.getChips(), totalBet);
        }

        gameService.placeBet(player, totalBet);
        break;
      default:
        log.warn("Unknown action {} during replay for player {}", action, player.getName());
    }

    // Update active players if needed
    if (action == AbstractAction.FOLD) {
      updateActivePlayersList(); // This already handles turn advancement correctly
    } else {
      // For non-fold actions during replay, advance to next player normally
      advanceToNextPlayer();
    }

    // CRITICAL FIX: Only advance turn for non-fold actions during replay
    // For fold actions, updateActivePlayersList() already handles turn advancement
  }

  private double getUtility(int playerId, List<ActionTrace> history) {
    // CRITICAL FIX: Collect bets into pots before calculating utility
    // This ensures that pot amounts are properly accumulated for pot-proportional utility calculation
    gameService.collectBetsIntoPot();

    log.debug("Collecting bets into pot before utility calculation for player {}", playerId);

    // Calculate utility for terminal node with proper pot amounts
    return gameService.calculateUtility(playerId);
  }

  private double getFoldedPlayerUtility(int playerId) {
    // Get player's total contribution from GameService tracking
    int playerContribution = gameService.getPlayerTotalContribution(playerId);

    // Apply same normalization as GameService.calculateUtility()
    double foldUtility = -normalizeUtilityForMCCFR(playerContribution);

    log.debug("Folded player {} utility - Contribution: {}, Normalized: {}", playerId,
        playerContribution, foldUtility);

    return foldUtility;
  }

  /**
   * Normalize utility values for MCCFR training (consistent with GameService normalization)
   * ENHANCED: Maintains consistency with pot-proportional utility calculation
   */
  private double normalizeUtilityForMCCFR(double rawUtility) {
    // Use same normalization approach as GameService.normalizeUtility()
    // Get average starting chip stack as normalization factor
    List<Player> players = gameService.getPlayers();
    double avgStartingChips = players.stream()
        .mapToInt(p -> 10000) // Default starting chips - could be made configurable
        .average().orElse(10000.0);

    // Normalize to roughly [-1.0, 1.0] range for CFR compatibility
    return rawUtility / avgStartingChips;
  }

  /**
   * Check if current round is completed and advance to next round if needed FIXED: Addresses round
   * progression TODO
   */
  private void checkAndAdvanceRoundIfCompleted(List<ActionTrace> history) {
    if (history.isEmpty()) {
      return;
    }

    BettingRound currentRound = gameService.getCurrentBettingRound();
    List<Player> activePlayers = gameService.getPlayers().stream().filter(Player::isActive)
        .toList();

    // Check if all active players have acted and betting is complete
    boolean roundComplete = isRoundComplete(activePlayers);

    if (roundComplete && currentRound != BettingRound.SHOWDOWN) {
      // Advance to next round
      gameService.nextRoundAndDealNextCards();
    }
  }

  /**
   * Check if the current betting round is complete CRITICAL FIX: Tracks actual player actions in
   * current round to prevent premature advancement ENHANCED: Improved two-player scenario handling
   * and comprehensive logging
   */
  private boolean isRoundComplete(List<Player> activePlayers) {
    if (activePlayers.size() <= 1) {
      log.debug("Round complete - only {} active players remaining", activePlayers.size());
      return true;
    }

    BettingRound currentRound = gameService.getCurrentBettingRound();
    if (currentRound == null) {
      currentRound = BettingRound.PREFLOP;
    }

    // Get players who have acted in this round during this traversal
    Set<Integer> playersActedThisRound = playersActedInRound.getOrDefault(currentRound,
        new HashSet<>());

    // Get active player indices for comparison
    Set<Integer> activePlayerIndices = activePlayers.stream().map(Player::getPlayerIndex)
        .collect(Collectors.toSet());

    // Check if all active players have acted in this round
    boolean allPlayersActed = activePlayers.stream()
        .allMatch(p -> playersActedThisRound.contains(p.getPlayerIndex()));

    // Enhanced logging for debugging
    log.debug("Round {} completion check - Active players: {}, Players acted: {}, All acted: {}",
        currentRound, activePlayerIndices, playersActedThisRound, allPlayersActed);

    if (!allPlayersActed) {
      // Detailed logging for two-player scenarios
      if (activePlayers.size() == 2) {
        log.debug("Two-player scenario - Active: {}, Acted: {}, Missing: {}", activePlayerIndices,
            playersActedThisRound,
            activePlayerIndices.stream().filter(id -> !playersActedThisRound.contains(id))
                .collect(Collectors.toSet()));
      }
      return false;
    }

    // Additionally check bet amounts match (traditional poker rule)
    int currentRoundBetAmount = gameService.getCurrentRoundBetAmount();
    boolean betsMatch = activePlayers.stream()
        .allMatch(p -> p.getCurrentBet() == currentRoundBetAmount || p.getChips() == 0);

    // Enhanced logging for bet matching
    if (log.isDebugEnabled()) {
      for (Player player : activePlayers) {
        log.debug("Player {} bet check - Current bet: {}, Round bet: {}, Chips: {}, Match: {}",
            player.getPlayerIndex(), player.getCurrentBet(), currentRoundBetAmount,
            player.getChips(),
            (player.getCurrentBet() == currentRoundBetAmount || player.getChips() == 0));
      }
    }

    log.debug("Round {} completion final result - All acted: {}, Bets match: {}, Complete: {}",
        currentRound, allPlayersActed, betsMatch, betsMatch);

    return betsMatch;
  }

  // ========================================
  // PLAYER TURN MANAGEMENT SYSTEM
  // ========================================

  /**
   * Initialize player turn management for a new game CRITICAL FIX: Properly track both active
   * players and current acting player index STACKOVERFLOW FIX: Added recursion prevention flag
   */
  private void initializePlayerTurnManagement() {
    // CRITICAL FIX: Set flag to prevent infinite recursion
    initializingPlayerTurnManagement = true;

    try {
      activePlayers = gameService.getPlayers().stream().filter(Player::isActive)
          .filter(p -> !p.isFolded()).collect(Collectors.toList());
      currentActingPlayer = 0;

      // Set current acting player index to first active player's actual index
      if (!activePlayers.isEmpty()) {
        currentActingPlayerIndex = activePlayers.get(0).getPlayerIndex();
      } else {
        currentActingPlayerIndex = 0;
      }

      log.debug("Initialized player turn management with {} active players, first to act: P{}",
          activePlayers.size(), currentActingPlayerIndex);
    } finally {
      // CRITICAL FIX: Always clear the flag, even if an exception occurs
      initializingPlayerTurnManagement = false;
    }
  }

  /**
   * Get the current acting player CRITICAL FIX: Return the actual player index, not activePlayers
   * index STACKOVERFLOW FIX: Added safety check to prevent infinite recursion
   */
  private int getCurrentActingPlayer() {
    if (activePlayers == null || activePlayers.isEmpty()) {
      // CRITICAL FIX: Prevent infinite recursion by checking if we're already initializing
      if (initializingPlayerTurnManagement) {
        log.warn(
            "Preventing infinite recursion in getCurrentActingPlayer() - returning default player 0");
        return 0;
      }
      initializePlayerTurnManagement();
    }

    // Return the actual player index, not the activePlayers list index
    return currentActingPlayerIndex;
  }

  /**
   * Advance to the next active player CRITICAL FIX: Properly advance through active players using
   * actual player indices
   */
  private void advanceToNextPlayer() {
    if (activePlayers.isEmpty()) {
      initializePlayerTurnManagement();
      return;
    }

    List<Player> allPlayers = gameService.getPlayers();
    if (allPlayers == null || allPlayers.isEmpty()) {
      log.error("Cannot advance to next player - no players available");
      return;
    }

    int attempts = 0;
    int nextPlayerIndex = currentActingPlayerIndex;

    do {
      // Advance to next player in the full player list
      nextPlayerIndex = (nextPlayerIndex + 1) % allPlayers.size();
      attempts++;

      // Safety check to prevent infinite loop
      if (attempts > allPlayers.size()) {
        log.warn("Could not find next active player after {} attempts, reinitializing", attempts);
        initializePlayerTurnManagement();
        return;
      }

      Player nextPlayer = getPlayerSafely(nextPlayerIndex);
      if (nextPlayer != null && nextPlayer.isActive() && !nextPlayer.isFolded()) {
        // Found the next active player
        currentActingPlayerIndex = nextPlayerIndex;

        // Update currentActingPlayer index in activePlayers list
        for (int i = 0; i < activePlayers.size(); i++) {
          if (activePlayers.get(i).getPlayerIndex() == nextPlayerIndex) {
            currentActingPlayer = i;
            break;
          }
        }

        log.debug("Advanced to next player: {} (P{})", nextPlayer.getName(), nextPlayerIndex);
        return;
      }
    } while (attempts <= allPlayers.size());

    log.warn("No active players found after advancing, reinitializing turn management");
    initializePlayerTurnManagement();
  }

  /**
   * Get the next active player after the given player index ENHANCED: Uses safe player retrieval
   */
  private int getNextActivePlayer(int currentPlayerIndex) {
    List<Player> allPlayers = gameService.getPlayers();
    if (allPlayers == null || allPlayers.isEmpty()) {
      log.error("Cannot get next active player - player list is null or empty");
      return currentPlayerIndex;
    }

    int nextIndex = (currentPlayerIndex + 1) % allPlayers.size();
    int attempts = 0;

    // CRITICAL FIX: Find next player who is not folded AND not all-in
    while (attempts < allPlayers.size()) {
      Player nextPlayer = getPlayerSafely(nextIndex);
      if (nextPlayer != null && !nextPlayer.isFolded() && !nextPlayer.isAllIn()
          && nextPlayer.isActive()) {
        return nextIndex;
      }
      nextIndex = (nextIndex + 1) % allPlayers.size();
      attempts++;
    }

    // If no active players found, return current player
    log.warn("No active players found, returning current player index: {}", currentPlayerIndex);
    return currentPlayerIndex;
  }

  /**
   * Update active players list (call when players fold or go all-in) CRITICAL FIX: Properly
   * maintain current acting player after list updates ENHANCED: Fixed turn advancement logic to
   * maintain correct poker sequence STACKOVERFLOW FIX: Added recursion prevention
   */
  private void updateActivePlayersList() {
    // STACKOVERFLOW FIX: Prevent infinite recursion
    if (updatingActivePlayersList) {
      log.warn("Preventing infinite recursion in updateActivePlayersList() - returning early");
      return;
    }

    updatingActivePlayersList = true;

    try {
      List<Player> previousActivePlayers = new ArrayList<>(activePlayers);
      int previousActingPlayerIndex = currentActingPlayerIndex;

      // Update the active players list with safe stream operations
      List<Player> allPlayers = gameService.getPlayers();
      if (allPlayers == null || allPlayers.isEmpty()) {
        log.warn("Cannot update active players list - no players available");
        return;
      }

      // CRITICAL FIX: Exclude both folded AND all-in players from active betting
      activePlayers = allPlayers.stream().filter(Objects::nonNull).filter(Player::isActive)
          .filter(p -> !p.isFolded() && !p.isAllIn()).collect(Collectors.toList());

      // Check if current acting player is still in the active list
      boolean currentPlayerStillActive = activePlayers.stream()
          .anyMatch(p -> p.getPlayerIndex() == currentActingPlayerIndex);

      if (!currentPlayerStillActive) {
        // Current player folded, find the next active player in sequence
        log.debug("Current acting player P{} is no longer active, finding next player in sequence",
            currentActingPlayerIndex);

        // CRITICAL FIX: Find next active player starting from the folded player's position
        int nextActivePlayerIndex = findNextActivePlayerFromPosition(previousActingPlayerIndex);

        if (nextActivePlayerIndex != -1) {
          // ENHANCED FIX: Robust index synchronization with validation
          boolean indexUpdateSuccess = updateCurrentActingPlayerIndices(nextActivePlayerIndex);

          if (indexUpdateSuccess) {
            Player nextPlayer = getPlayerSafely(nextActivePlayerIndex);
            log.debug("Successfully advanced to next active player: {} (P{})",
                nextPlayer != null ? nextPlayer.getName() : "Unknown", nextActivePlayerIndex);
          } else {
            log.error(
                "Failed to update acting player indices for P{}, reinitializing turn management",
                nextActivePlayerIndex);
            initializePlayerTurnManagement();
          }
        } else {
          log.warn("No active players found after fold, reinitializing turn management");
          initializePlayerTurnManagement();
        }
      } else {
        // Current player still active, update their index in the new activePlayers list
        boolean indexUpdateSuccess = updateCurrentActingPlayerIndices(currentActingPlayerIndex);

        if (!indexUpdateSuccess) {
          log.error("Failed to update acting player indices for current player P{}, reinitializing",
              currentActingPlayerIndex);
          initializePlayerTurnManagement();
        }
      }

      log.debug("Updated active players list: {} players remaining, current acting: P{}",
          activePlayers.size(), currentActingPlayerIndex);

      // Log the active players for debugging
      if (log.isDebugEnabled()) {
        String activePlayerNames = activePlayers.stream()
            .map(p -> p.getName() + "(P" + p.getPlayerIndex() + ")")
            .collect(Collectors.joining(", "));
        log.debug("Active players: {}", activePlayerNames);
      }
    } finally {
      // STACKOVERFLOW FIX: Always clear the flag
      updatingActivePlayersList = false;
    }
  }

  /**
   * Update both currentActingPlayerIndex and currentActingPlayer with robust validation CRITICAL
   * FIX: Addresses race conditions and index synchronization issues
   */
  private boolean updateCurrentActingPlayerIndices(int targetPlayerIndex) {
    try {
      // Validate that the target player exists in the game
      Player targetPlayer = getPlayerSafely(targetPlayerIndex);
      if (targetPlayer == null) {
        log.error("Cannot update indices: target player P{} does not exist", targetPlayerIndex);
        return false;
      }

      // Validate that the target player is active and not folded
      if (!targetPlayer.isActive() || targetPlayer.isFolded()) {
        log.error("Cannot update indices: target player P{} ({}) is not active or has folded",
            targetPlayerIndex, targetPlayer.getName());
        return false;
      }

      // Update the actual player index
      currentActingPlayerIndex = targetPlayerIndex;

      // Find the corresponding index in the activePlayers list with validation
      boolean foundInActiveList = false;
      for (int i = 0; i < activePlayers.size(); i++) {
        Player activePlayer = activePlayers.get(i);
        if (activePlayer != null && activePlayer.getPlayerIndex() == targetPlayerIndex) {
          currentActingPlayer = i;
          foundInActiveList = true;
          log.debug(
              "Updated indices: currentActingPlayerIndex={}, currentActingPlayer={}, player={}",
              currentActingPlayerIndex, currentActingPlayer, activePlayer.getName());
          break;
        }
      }

      if (!foundInActiveList) {
        log.error("CRITICAL: Player P{} ({}) not found in activePlayers list after update",
            targetPlayerIndex, targetPlayer.getName());

        // Log current active players for debugging
        if (log.isDebugEnabled()) {
          String activePlayersList = activePlayers.stream()
              .map(p -> p.getName() + "(P" + p.getPlayerIndex() + ")")
              .collect(Collectors.joining(", "));
          log.debug("Current activePlayers list: [{}]", activePlayersList);
        }

        // Attempt to rebuild activePlayers list
        log.debug("Attempting to rebuild activePlayers list to include P{}", targetPlayerIndex);
        rebuildActivePlayersList();

        // Try again after rebuild
        for (int i = 0; i < activePlayers.size(); i++) {
          Player activePlayer = activePlayers.get(i);
          if (activePlayer != null && activePlayer.getPlayerIndex() == targetPlayerIndex) {
            currentActingPlayer = i;
            foundInActiveList = true;
            log.debug("Found player P{} in rebuilt activePlayers list at index {}",
                targetPlayerIndex, i);
            break;
          }
        }
      }

      return foundInActiveList;

    } catch (Exception e) {
      log.error("Exception while updating acting player indices for P{}: {}", targetPlayerIndex,
          e.getMessage(), e);
      return false;
    }
  }

  /**
   * Rebuild the activePlayers list to ensure consistency CRITICAL FIX: Addresses timing issues in
   * list updates STACKOVERFLOW FIX: Added recursion prevention
   */
  private void rebuildActivePlayersList() {
    // STACKOVERFLOW FIX: Prevent infinite recursion
    if (rebuildingActivePlayersList) {
      log.warn("Preventing infinite recursion in rebuildActivePlayersList() - returning early");
      return;
    }

    rebuildingActivePlayersList = true;

    try {
      List<Player> allPlayers = gameService.getPlayers();
      if (allPlayers == null || allPlayers.isEmpty()) {
        log.error("Cannot rebuild activePlayers list - no players available");
        return;
      }

      // CRITICAL FIX: Exclude both folded AND all-in players from active betting
      List<Player> newActivePlayers = allPlayers.stream().filter(Objects::nonNull)
          .filter(Player::isActive).filter(p -> !p.isFolded() && !p.isAllIn())
          .collect(Collectors.toList());

      log.debug("Rebuilt activePlayers list: {} players (was {} players)", newActivePlayers.size(),
          activePlayers.size());

      activePlayers = newActivePlayers;

    } catch (Exception e) {
      log.error("Exception while rebuilding activePlayers list: {}", e.getMessage(), e);
    } finally {
      // STACKOVERFLOW FIX: Always clear the flag
      rebuildingActivePlayersList = false;
    }
  }

  /**
   * Find the next active player starting from a specific position CRITICAL FIX: Ensures correct
   * turn sequence after fold actions
   */
  private int findNextActivePlayerFromPosition(int startingPosition) {
    List<Player> allPlayers = gameService.getPlayers();
    if (allPlayers == null || allPlayers.isEmpty()) {
      log.error("Cannot find next active player - no players available");
      return -1;
    }

    int nextIndex = (startingPosition + 1) % allPlayers.size();
    int attempts = 0;

    // CRITICAL FIX: Find next active player who is not folded AND not all-in
    while (attempts < allPlayers.size()) {
      Player nextPlayer = getPlayerSafely(nextIndex);
      if (nextPlayer != null && nextPlayer.isActive() && !nextPlayer.isFolded()
          && !nextPlayer.isAllIn()) {
        log.debug("Found next active player: {} (P{}) after P{} (excluding folded and all-in)",
            nextPlayer.getName(), nextIndex, startingPosition);
        return nextIndex;
      }
      nextIndex = (nextIndex + 1) % allPlayers.size();
      attempts++;
    }

    log.warn("No active players found starting from position {}", startingPosition);
    return -1;
  }

  /**
   * Get current player from history (fallback method)
   */
//  private int getCurrentPlayer(List<ActionTrace> history) {
//    if (history.isEmpty()) {
//      return getCurrentActingPlayer();
//    }
//
//    // Use the turn management system instead of relying on history
//    return getCurrentActingPlayer();
//  }

  /**
   * Safely retrieve a player by ID with comprehensive error handling ENHANCED: Addresses null
   * player parameter issue
   */
  private Player getPlayerSafely(int playerId) {
    try {
      List<Player> allPlayers = gameService.getPlayers();

      if (allPlayers == null || allPlayers.isEmpty() || playerId < 0
          || playerId >= allPlayers.size() || allPlayers.get(playerId) == null) {
        log.error("Cannot retrieve player: invalid player ID {}", playerId);
        return null;
      }
      Player player = allPlayers.get(playerId);

      log.debug("Successfully retrieved player {} ({})", playerId, player.getName());
      return player;

    } catch (Exception e) {
      log.error("Exception while retrieving player {}: {}", playerId, e.getMessage(), e);
      return null;
    }
  }

  // ========================================
  // GAME STATE STORAGE AND RESTORATION SYSTEM
  // ========================================

  /**
   * Store initial game state (hole cards, community cards, and player state) for consistent
   * restoration CRITICAL FIX: Ensures complete game state remains consistent across state
   * restorations
   */
  private void storeInitialGameState() {
    try {
      // Clear all storage maps
      initialHoleCards.clear();
      initialPlayerChips.clear();
      initialPlayerBets.clear();
      initialPlayerFoldedState.clear();
      initialPlayerAllInState.clear();

      // Store complete player state for each player
      List<Player> allPlayers = gameService.getPlayers();
      for (Player player : allPlayers) {
        int playerId = player.getPlayerIndex();

        // Store hole cards
        if (player.getHand() != null && !player.getHand().isEmpty()) {
          initialHoleCards.put(playerId, new ArrayList<>(player.getHand()));
          log.debug("Stored hole cards for player {} (P{}): {}", player.getName(), playerId,
              player.getHand());
        }

        // CRITICAL FIX: Store complete player state
        initialPlayerChips.put(playerId, player.getChips());
        initialPlayerBets.put(playerId, player.getCurrentBet());
        initialPlayerFoldedState.put(playerId, player.isFolded());
        initialPlayerAllInState.put(playerId, player.isAllIn());

        log.debug(
            "Stored complete state for player {} (P{}): chips={}, bet={}, folded={}, allIn={}",
            player.getName(), playerId, player.getChips(), player.getCurrentBet(),
            player.isFolded(), player.isAllIn());
      }

      // Store community cards
      initialCommunityCards.clear();
      List<Card> communityCards = gameService.getCommunityCards();
      if (communityCards != null) {
        initialCommunityCards.addAll(communityCards);
        log.debug("Stored {} community cards: {}", communityCards.size(), communityCards);
      }

      log.debug(
          "Complete initial game state stored: {} players with full state, {} community cards",
          initialHoleCards.size(), initialCommunityCards.size());
    } catch (Exception e) {
      log.warn("Failed to store initial game state: {}", e.getMessage());
    }
  }

  /**
   * Restore initial game state (hole cards, community cards, and player state) during state
   * restoration CRITICAL FIX: Maintains consistent complete game state across MCCFR tree traversal
   */
  private void restoreInitialGameState() {
    try {
      // Restore complete player state for each player
      List<Player> allPlayers = gameService.getPlayers();
      for (Player player : allPlayers) {
        int playerId = player.getPlayerIndex();

        // Restore hole cards
        List<Card> storedHoleCards = initialHoleCards.get(playerId);
        if (storedHoleCards != null && !storedHoleCards.isEmpty()) {
          try {
            player.clearHand();
            for (Card card : storedHoleCards) {
              player.addCardToHand(card);
            }
            log.debug("Restored hole cards for player {} (P{}): {}", player.getName(), playerId,
                storedHoleCards);
          } catch (Exception e) {
            log.error("Failed to restore hole cards for player {} (P{}): {}", player.getName(),
                playerId, e.getMessage());
          }
        } else {
          log.warn("No stored hole cards found for player {} (P{})", player.getName(), playerId);
        }

        // CRITICAL FIX: Restore complete player state
        Integer storedChips = initialPlayerChips.get(playerId);
        Integer storedBet = initialPlayerBets.get(playerId);
        Boolean storedFolded = initialPlayerFoldedState.get(playerId);
        Boolean storedAllIn = initialPlayerAllInState.get(playerId);

        if (storedChips != null) {
          player.setChips(storedChips);
          log.debug("Restored chips for player {} (P{}): {}", player.getName(), playerId,
              storedChips);
        }

        if (storedBet != null) {
          player.setCurrentBet(storedBet);
          log.debug("Restored bet for player {} (P{}): {}", player.getName(), playerId, storedBet);
        }

        if (storedFolded != null) {
          player.setFolded(storedFolded);
          log.debug("Restored folded state for player {} (P{}): {}", player.getName(), playerId,
              storedFolded);
        }

        if (storedAllIn != null) {
          player.setAllIn(storedAllIn);
          log.debug("Restored all-in state for player {} (P{}): {}", player.getName(), playerId,
              storedAllIn);
        }

        log.debug(
            "Restored complete state for player {} (P{}): chips={}, bet={}, folded={}, allIn={}",
            player.getName(), playerId, player.getChips(), player.getCurrentBet(),
            player.isFolded(), player.isAllIn());
      }

      // Restore community cards
      if (!initialCommunityCards.isEmpty()) {
        try {
          gameService.setCommunityCards(new ArrayList<>(initialCommunityCards));
          log.debug("Restored {} community cards: {}", initialCommunityCards.size(),
              initialCommunityCards);
        } catch (Exception e) {
          log.warn("Failed to restore community cards: {}", e.getMessage());
        }
      }

      log.debug(
          "Complete initial game state restored: {} players with full state, {} community cards",
          initialHoleCards.size(), initialCommunityCards.size());
    } catch (Exception e) {
      log.warn("Failed to restore initial game state: {}", e.getMessage());
      // Fallback: deal new hole cards if restoration fails
      gameService.dealHoleCards();
      log.debug("Fallback: dealt new hole cards due to restoration failure");
    }
  }

  // ========================================
  // GAMESERVICE INITIALIZATION SYSTEM
  // ========================================

  /**
   * Initialize betting round for real GameService compatibility CRITICAL FIX: Real GameService
   * requires explicit betting round setup
   */
  private void initializeBettingRound() {
    try {
      // Start with PREFLOP betting round
      gameService.startBettingRound(BettingRound.PREFLOP);
      log.debug("Initialized betting round: PREFLOP");
    } catch (Exception e) {
      log.warn("Failed to initialize betting round: {}", e.getMessage());
      // Continue execution - mocks may not implement this method
    }
  }

  // ========================================
  // BLIND POSTING SYSTEM
  // ========================================

  /**
   * Post small blind and big blind at the start of each hand Follows standard poker blind posting
   * rules with dealer button rotation ENHANCED: Now includes realistic dealer button rotation
   */
  private void postBlinds() {
    List<Player> allPlayers = gameService.getPlayers();
    if (allPlayers.size() < 2) {
      log.warn("Cannot post blinds with less than 2 players");
      return;
    }

    // Use rotating dealer button for realistic multi-hand simulation
    int dealerPosition = dealerButtonPosition % allPlayers.size();
    int smallBlindPosition = (dealerPosition + 1) % allPlayers.size();
    int bigBlindPosition = (dealerPosition + 2) % allPlayers.size();

    // For heads-up, adjust positions (dealer posts small blind)
    if (allPlayers.size() == 2) {
      smallBlindPosition = dealerPosition;
      bigBlindPosition = (dealerPosition + 1) % allPlayers.size();
    }

    log.debug("Dealer button at position {}, SB at {}, BB at {}", dealerPosition,
        smallBlindPosition, bigBlindPosition);

    // CRITICAL FIX: Use safe player retrieval for blind posting
    Player smallBlindPlayer = getPlayerSafely(smallBlindPosition);
    Player bigBlindPlayer = getPlayerSafely(bigBlindPosition);

    // Post small blind with null safety
    if (smallBlindPlayer == null) {
      log.error("🚨 CRITICAL: Cannot retrieve small blind player at position {}",
          smallBlindPosition);
      return; // Cannot continue without valid players
    }

    if (smallBlindPlayer.getChips() >= SMALL_BLIND_AMOUNT) {
      executeAction(smallBlindPlayer, AbstractAction.SMALL_BLIND);
      log.info("Posted small blind: {} ({} chips)", smallBlindPlayer.getName(), SMALL_BLIND_AMOUNT);
    } else {
      log.warn("Player {} cannot afford small blind", smallBlindPlayer.getName());
    }

    // Post big blind with null safety
    if (bigBlindPlayer == null) {
      log.error("🚨 CRITICAL: Cannot retrieve big blind player at position {}", bigBlindPosition);
      return; // Cannot continue without valid players
    }

    if (bigBlindPlayer.getChips() >= BIG_BLIND_AMOUNT) {
      executeAction(bigBlindPlayer, AbstractAction.BIG_BLIND);
    } else {
      log.warn("Player {} cannot afford big blind", bigBlindPlayer.getName());
    }

    // CRITICAL FIX: Set the current acting player to the player after big blind
    int firstToActIndex = (bigBlindPosition + 1) % allPlayers.size();
    currentActingPlayerIndex = firstToActIndex;

    // Update currentActingPlayer index in activePlayers list
    initializePlayerTurnManagement(); // Refresh active players list
    for (int i = 0; i < activePlayers.size(); i++) {
      if (activePlayers.get(i).getPlayerIndex() == firstToActIndex) {
        currentActingPlayer = i;
        break;
      }
    }

    Player firstToActPlayer = getPlayerSafely(firstToActIndex);
    if (firstToActPlayer != null) {
      log.debug("Blinds posted. First to act: {} (P{})", firstToActPlayer.getName(),
          firstToActIndex);
    } else {
      log.debug("Blinds posted. First to act: P{}", firstToActIndex);
    }

    // Advance dealer button for next hand (realistic rotation)
    dealerButtonPosition = (dealerButtonPosition + 1) % allPlayers.size();
    log.debug("Dealer button advanced to position {} for next hand", dealerButtonPosition);
  }
}
