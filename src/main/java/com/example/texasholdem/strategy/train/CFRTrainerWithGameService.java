package com.example.texasholdem.strategy.train;


import com.example.texasholdem.model.Action;
import com.example.texasholdem.model.ActionInstance;
import com.example.texasholdem.model.Card;
import com.example.texasholdem.service.GameService;
import com.example.texasholdem.service.HandEvaluator;
import com.example.texasholdem.strategy.model.Infoset;
import com.example.texasholdem.strategy.model.InfosetValue;
import lombok.extern.slf4j.Slf4j;
import com.example.texasholdem.model.BettingRound;
import com.example.texasholdem.model.Player;
import com.example.texasholdem.model.SidePot;
import com.example.texasholdem.strategy.model.AbstractAction;
import com.example.texasholdem.strategy.model.ActionTrace;
import com.example.texasholdem.strategy.model.InfosetStore;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import lombok.Getter;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CFRTrainerWithGameService {

  @Getter
  private final InfosetStore store;
  private final int iterations;
  private GameService gameService;
  private final int playerCount;
  private final Random randomNumGenerator = new Random();
  private static final int PRUNE_THRESHOLD = 200;
  private static final int MAX_RECURSION_DEPTH = 20;
  private static final int SMALL_BLIND = 5;
  private static final int BIG_BLIND = 10;
  // Pluribus-specific constants
  private static final int REGRET_THRESHOLD = -300_000_000; // Pluribus pruning threshold
  private static final int STRATEGY_INTERVAL = 10000;
  private static final double PRUNING_PROBABILITY = 0.05;
  private int newGame = 1;
  @Getter
  private final List<SidePot> sidePots = new ArrayList<>();
  @Getter
  private SidePot mainPot = new SidePot();
  private int dealerIndex = 0;
  private int mainPlayer;
  private List<ActionTrace> historyForUpdate = new ArrayList<>();
  private int firstPlayerIndex = -1;

  private List<ActionTrace> actionHistory = new ArrayList<>();

  private double resultUtility;

  private int foldedPlayersCount = 0;
  private int lastBettingPlayerIndex = -1;


  public CFRTrainerWithGameService(InfosetStore store, int iterations, int playerCount,
      GameService gameService) {
    this.store = store;
    this.iterations = iterations;
    this.playerCount = playerCount;
    this.gameService = gameService;
  }

  public void train() {
    // train with sb/bb: 5/10. Player stack = 10k
    int currentIteration = 1;
    for (int i = 0; i < playerCount; i++) {
      gameService.addPlayer(new Player("Player" + i, i, 10000));
    }
    while (currentIteration <= iterations) {

      gameService.initializeGameForCFR();
      gameService.dealHoleCards();

      for (Player player : gameService.getPlayers()) {
        mainPlayer = player.getPlayerIndex();
        actionHistory = new ArrayList<>();

        if (currentIteration % STRATEGY_INTERVAL == 0) {
          historyForUpdate = new ArrayList<>();
        }

        // Pluribus MCCFR-P sampling logic
        boolean usePruning;
        if (currentIteration > PRUNE_THRESHOLD) {
          double q = randomNumGenerator.nextDouble();
          usePruning = q >= PRUNING_PROBABILITY; // Use pruning 95% of the time after threshold
        } else {
          usePruning = false; // No pruning before threshold
        }

        startBettingRoundForTraining(BettingRound.PREFLOP, mainPlayer,
            usePruning,
            currentIteration % STRATEGY_INTERVAL == 0, actionHistory, 0);

        dealerIndex = (dealerIndex + 1) % gameService.getPlayers().size();
        gameService.initializeGameForCFR();

      }

      int discountInterval = 10;
      int LCFRThreshold = 400;
      if (currentIteration < LCFRThreshold && currentIteration % discountInterval == 0) {
        store.applyDiscount(currentIteration, discountInterval,
            (float) currentIteration / discountInterval / (
                (float) currentIteration / discountInterval + 1f));
      }

      currentIteration++;
    }
  }

  public double startBettingRoundForTraining(BettingRound round, int mainPlayer, boolean usePruning,
      boolean needToUpdateStrategy, List<ActionTrace> actionHistory, int turnIndex) {
    log.info("Starting {} betting round.", round.name());
    gameService.startBettingRound(round);

    List<Player> players = gameService.getPlayers();
    int smallBlindIndex = (dealerIndex + 1) % players.size();
    int bigBlindIndex = (dealerIndex + 2) % players.size();
    int initialPlayersCount = (int) players.stream().filter(Player::isActive).count();
    lastBettingPlayerIndex = -1;
    int currentPlayerIndex = gameService.getFirstToActIndex();
    foldedPlayersCount = 0;
    int totalPlayers = players.size();

    if (round == BettingRound.PREFLOP) {
      Player smallBlindPlayer = players.get(smallBlindIndex);
      Player bigBlindPlayer = players.get(bigBlindIndex);

      gameService.placeBet(smallBlindPlayer, SMALL_BLIND);
      gameService.placeBet(bigBlindPlayer, Math.min(bigBlindPlayer.getChips(), BIG_BLIND));

      actionHistory.add(
          new ActionTrace(mainPlayer, AbstractAction.SMALL_BLIND,
              BettingRound.PREFLOP.ordinal(), 0, smallBlindPlayer.getPlayerIndex()));
      actionHistory.add(new ActionTrace(mainPlayer, AbstractAction.BIG_BLIND,
          BettingRound.PREFLOP.ordinal(), 1, bigBlindPlayer.getPlayerIndex()));

      gameService.setFirstToAct((bigBlindIndex + 1) % players.size());
      lastBettingPlayerIndex = bigBlindIndex;

      resultUtility = runCfrForOneRound(true, players, round, mainPlayer, initialPlayersCount,
          lastBettingPlayerIndex, gameService.getFirstToActIndex(), foldedPlayersCount,
          totalPlayers,
          actionHistory, usePruning, needToUpdateStrategy, 2);

      return resultUtility;
    } else {
      gameService.setFirstToAct(smallBlindIndex);
      if (round == BettingRound.SHOWDOWN || checkForWinnerInTraining()) {
        log.info(
            "Game ended in showdown or early due to only one active player. There are community cards: {}",
            gameService.getCommunityCards());

        gameService.distributeWinnings();
        gameService.completeCommunityCardsIfNeeded();
        return leafUtility(mainPlayer, players);
      }

      if (round == BettingRound.FLOP) {
        gameService.dealCommunityCards(3);
      } else if (round == BettingRound.TURN || round == BettingRound.RIVER) {
        gameService.dealCommunityCards(1);
      }

      resultUtility = runCfrForOneRound(false, players, round, mainPlayer, initialPlayersCount,
          lastBettingPlayerIndex, currentPlayerIndex, foldedPlayersCount, totalPlayers,
          actionHistory, usePruning, needToUpdateStrategy, turnIndex);

      return resultUtility;
    }
  }

  private boolean checkForWinnerInTraining() {
    long activePlayers = gameService.getPlayers().stream().filter(Player::isActive).count();

    if (activePlayers <= 1) {
      log.info("Game ended early due to only one active player.");
      return true;
    }

    return false;
  }

  public double runCfrForOneRound(boolean smallBlindAndBigBlind, List<Player> players,
      BettingRound currentRound, int mainPlayer, int initialPlayersCount,
      int lastBettingPlayerIndex, int currentPlayerIndex, int foldedPlayersCount, int totalPlayers,
      List<ActionTrace> actionHistory, boolean usePruning, boolean needToUpdateStrategy,
      int turnIndex) {

    Player player = players.get(currentPlayerIndex);

    if (!player.isActive()) {
      currentPlayerIndex = (currentPlayerIndex + 1) % totalPlayers;

      return runCfrForOneRound(smallBlindAndBigBlind, players, currentRound, mainPlayer,
          initialPlayersCount, lastBettingPlayerIndex, currentPlayerIndex, foldedPlayersCount,
          totalPlayers, actionHistory, usePruning, needToUpdateStrategy, turnIndex + 1);
    }

    if (smallBlindAndBigBlind) {
      firstPlayerIndex = currentPlayerIndex;
      smallBlindAndBigBlind = false;
    }

    if (lastBettingPlayerIndex == currentPlayerIndex) {
      log.info("Last aggressor found.");
      gameService.collectBetsIntoPot();
      resultUtility = startBettingRoundForTraining(currentRound.getNextRound(), mainPlayer,
          usePruning,
          needToUpdateStrategy, actionHistory, turnIndex);

//      getCurrentGameState();
      return resultUtility;
    }

    if (initialPlayersCount - foldedPlayersCount == 1) {
      log.info("onePlayerLeft");
      gameService.collectBetsIntoPot();
      resultUtility = startBettingRoundForTraining(currentRound.getNextRound(), mainPlayer,
          usePruning,
          needToUpdateStrategy, actionHistory, turnIndex);
      return resultUtility;
    }

    if (player.getPlayerIndex() == mainPlayer) {

      Infoset key = Infoset.of(player.getHand(), gameService.getCommunityCards(), currentRound,
          player, gameService.getCurrentRoundBetAmount(), actionHistory);
      // OPTIMIZATION OPPORTUNITY: Dynamic action space based on game state
      // Currently creates 7 actions every time - could optimize by filtering legal actions
      InfosetValue infosetValue = store.getOrCreate(player.getPlayerIndex(),
          InfosetStoreKey.abstractInfoset(key));

      float[] myStrategyNow = infosetValue.calculateStrategy();
      float[] regrets = infosetValue.getRegret();

      // Pluribus: Track strategy only on preflop (first betting round)
      if (needToUpdateStrategy && equals(actionHistory, historyForUpdate) &&
          currentRound == BettingRound.PREFLOP) {
        AbstractAction action = sampleAction(myStrategyNow);
        historyForUpdate.add(
            new ActionTrace(mainPlayer, action, currentRound.ordinal(),
                turnIndex, player.getPlayerIndex()));

        float[] accumulatedStrategyCount = infosetValue.getActionCounter();
        accumulatedStrategyCount[action.ordinal()] += 1;
        infosetValue.setActionCounter(accumulatedStrategyCount);
      }

      double[] actionUtilities = new double[myStrategyNow.length];
      double nodeUtility = 0.0;
      boolean[] explored = new boolean[myStrategyNow.length];

      for (int i = 0; i < myStrategyNow.length && (i < 7); i++) {
        if (usePruning && regrets[i] <= REGRET_THRESHOLD) {
          explored[i] = false;
        } else {

          player.placeBet(
              getPlayerBetAmountThisAction(i, player, gameService.getCurrentRoundBetAmount()));

          List<ActionTrace> newHistory = new ArrayList<>(actionHistory);

          actionHistory.add(
              new ActionTrace(mainPlayer, AbstractAction.values()[i], currentRound.ordinal(),
                  turnIndex, player.getPlayerIndex()));

          try {
            performActionInTraining(player, AbstractAction.values()[i],
                gameService.getCurrentRoundBetAmount());

            if (AbstractAction.values()[i] == AbstractAction.FOLD) {
              foldedPlayersCount++;
            } else if (AbstractAction.values()[i] == AbstractAction.BET_OR_RAISE_30
                || AbstractAction.values()[i] == AbstractAction.BET_OR_RAISE_60
                || AbstractAction.values()[i] == AbstractAction.BET_OR_RAISE_100
                || AbstractAction.values()[i] == AbstractAction.BET_OR_RAISE_200
                || AbstractAction.values()[i] == AbstractAction.BET_OR_RAISE_500
                || AbstractAction.values()[i] == AbstractAction.SMALL_BLIND
                || AbstractAction.values()[i] == AbstractAction.BIG_BLIND) {
              lastBettingPlayerIndex = currentPlayerIndex;
            }
          } catch (IllegalArgumentException e) {
            log.error("Invalid action from player {}, defaulting to fold: {}", player.getName(),
                e.getMessage());
            throw e;
          }

          double actionUtility = runCfrForOneRound(smallBlindAndBigBlind, players, currentRound,
              mainPlayer, initialPlayersCount, lastBettingPlayerIndex,
              (currentPlayerIndex + 1) % players.size(), foldedPlayersCount, totalPlayers,
              actionHistory, usePruning, needToUpdateStrategy, turnIndex + 1);

          getCurrentGameState(newHistory, players);
          turnIndex = actionHistory.getLast().getTurnIndex();
          currentRound = BettingRound.values()[actionHistory.getLast().getRound()];
          currentPlayerIndex = actionHistory.getLast().getActingPlayerIndex() + 1;

          actionUtilities[i] = actionUtility;
          nodeUtility += myStrategyNow[i] * actionUtility;

          explored[i] = true;
        }
      }

      for (int i = 0; i < myStrategyNow.length; i++) {
        if (explored[i]) {
          regrets[i] += (float) (actionUtilities[i] - nodeUtility);
          infosetValue.updateRegret(regrets, i);
        }
      }
      return nodeUtility;

    } else {
      Infoset opponentKey = Infoset.of(player.getHand(), gameService.getCommunityCards(),
          currentRound, player.getPlayerIndex(), gameService.getCurrentRoundBetAmount(),
          actionHistory);
      InfosetValue opponentInfoset = store.getOrCreate(player.getPlayerIndex(),
          InfosetStoreKey.abstractInfoset(opponentKey));

      AbstractAction currentAction = sampleAction(opponentInfoset.calculateStrategy());

//          return cfr(holeCards, communityCards, currentRound, player,
//              currentRoundBetAmount + getPlayerBetAmountThisAction(opponentAction.ordinal(),
//                  players.get(actionHistory.size() % players.size()), currentRoundBetAmount, potSize),
//              newHistory, probSelf,
//              probOthers * opponentInfoset.calculateStrategy()[opponentAction.ordinal()], usePruning,
//              players, potSize + getPlayerBetAmountThisAction(opponentAction.ordinal(),
//                  players.get(actionHistory.size() % players.size()), currentRoundBetAmount, potSize),
//              depth + 1, smallBetCount, bigBetCount, needToUpdateStrategy);

      try {
        performActionInTraining(player, currentAction, gameService.getCurrentRoundBetAmount());

        actionHistory.add(
            new ActionTrace(mainPlayer, currentAction, currentRound.ordinal(), turnIndex,
                player.getPlayerIndex()));

        if (currentAction == AbstractAction.FOLD) {
          foldedPlayersCount++;
        } else if (currentAction == AbstractAction.BET_OR_RAISE_30
            || currentAction == AbstractAction.BET_OR_RAISE_60
            || currentAction == AbstractAction.BET_OR_RAISE_100
            || currentAction == AbstractAction.BET_OR_RAISE_200
            || currentAction == AbstractAction.BET_OR_RAISE_500
            || currentAction == AbstractAction.SMALL_BLIND
            || currentAction == AbstractAction.BIG_BLIND) {
          lastBettingPlayerIndex = currentPlayerIndex;
        }
      } catch (IllegalArgumentException e) {
        log.error("Invalid action from player {}, defaulting to fold: {}", player.getName(),
            e.getMessage());
        throw e;
      }

      return runCfrForOneRound(smallBlindAndBigBlind, players, currentRound, mainPlayer,
          initialPlayersCount, lastBettingPlayerIndex, (currentPlayerIndex + 1) % players.size(),
          foldedPlayersCount, totalPlayers, actionHistory, usePruning, needToUpdateStrategy,
          turnIndex + 1);

    }
  }

  private void getCurrentGameState(List<ActionTrace> newHistory, List<Player> players) {
    log.info("Initial game state:");
    log.info("Main pot: {}", mainPot.getAmount());
    for (SidePot sidePot : sidePots) {
      log.info("Side pot: {}", sidePot.getAmount());
    }
    log.info("Action history: {}", actionHistory);

    actionHistory = new ArrayList<>(newHistory);
    gameService.initializeGameForGameStateRecovery();

    for (ActionTrace actionTrace : actionHistory) {
      Player player = players.get(actionTrace.getActingPlayerIndex());
      AbstractAction action = actionTrace.getActionAbstract();
      int currentRoundBetAmount = gameService.getCurrentRoundBetAmount();

      performActionInTraining(player, action, currentRoundBetAmount);

      if (action == AbstractAction.FOLD) {
        foldedPlayersCount++;
      } else if (action == AbstractAction.BET_OR_RAISE_30
          || action == AbstractAction.BET_OR_RAISE_60
          || action == AbstractAction.BET_OR_RAISE_100
          || action == AbstractAction.BET_OR_RAISE_200
          || action == AbstractAction.BET_OR_RAISE_500
          || action == AbstractAction.SMALL_BLIND
          || action == AbstractAction.BIG_BLIND) {
        lastBettingPlayerIndex = actionTrace.getPlayerIndex();
      }
    }

    log.info("Recovered game state:");
    log.info("Main pot: {}", mainPot.getAmount());
    for (SidePot sidePot : sidePots) {
      log.info("Side pot: {}", sidePot.getAmount());
    }
    log.info("Action history: {}", actionHistory);
    log.info("Community cards: {}", gameService.getCommunityCards());
    log.info("Players:");
    for (Player player : players) {
      log.info("Player {}: chips: {}, current bet: {}, hand: {}, active: {}, folded: {}",
          player.getName(), player.getChips(), player.getCurrentBet(), player.getHand(),
          player.isActive(), player.isFolded());
    }

  }

  private boolean equals(List<ActionTrace> actionHistory, List<ActionTrace> historyForUpdate) {
    if (actionHistory.size() != historyForUpdate.size()) {
      return false;
    }
    for (int i = 0; i < actionHistory.size(); i++) {
      if (!actionHistory.get(i).equals(historyForUpdate.get(i))) {
        return false;
      }
    }
    return true;
  }

  public AbstractAction sampleAction(float[] strategy) {
    double cumulative = 0.0;
    for (int i = 0; i < strategy.length; i++) {
      cumulative += strategy[i];
      if (randomNumGenerator.nextDouble() < cumulative) {
        return AbstractAction.values()[i];
      }
    }
    return AbstractAction.values()[strategy.length - 1];
  }

  public void performActionInTraining(Player player, AbstractAction action,
      int currentRoundBetAmount) {
    String playerName = player.getName();
    float[] BET_SIZE_MULTIPLIERS = {0.3f, 0.6f, 1.0f, 2.0f, 5.0f};

    switch (action) {
      case CHECK_OR_CALL:
        if (currentRoundBetAmount == 0) {
          // Player checks
          log.info("Player {} checked.", playerName);
          return;
        } else {
          // Player calls
          int callAmount = currentRoundBetAmount - player.getCurrentBet();
          if (callAmount < player.getChips()) {
            // normal call
            player.placeBet(callAmount);
            log.info("Player {} called with {} chips, total bet amount: {}.", playerName,
                callAmount, player.getCurrentBet());
          } else {
            // Player goes all-in to call
            int allInAmount = player.getChips();
            player.placeBet(allInAmount);
            log.info("Player {} called all-in with {} chips, total bet amount: {}.", playerName,
                allInAmount, player.getCurrentBet());
          }
          break;
        }

      case BET_OR_RAISE_30:
        if (currentRoundBetAmount ==0){
          // for the case currentRoundBetAmount =0
        }
        if (getPlayerBetAmountThisAction(2, player, currentRoundBetAmount)
            <= currentRoundBetAmount - player.getCurrentBet()) {
          throw new IllegalArgumentException(
              "Bet or raise must exceed the current bet. Action amount is "
                  + getPlayerBetAmountThisAction(2, player, currentRoundBetAmount)
                  + " but current bet is " + currentRoundBetAmount + ", player's previous bet is "
                  + player.getCurrentBet());
        }
        gameService.placeBet(player,
            getPlayerBetAmountThisAction(2, player, currentRoundBetAmount));
        log.info("Player {} {} with {} chips, total bet amount: {}.", playerName,
            action.name().toLowerCase(),
            getPlayerBetAmountThisAction(2, player, currentRoundBetAmount),
            player.getCurrentBet());
        break;

      case BET_OR_RAISE_60:
        if (getPlayerBetAmountThisAction(3, player, currentRoundBetAmount)
            <= currentRoundBetAmount - player.getCurrentBet()) {
          throw new IllegalArgumentException(
              "Bet or raise must exceed the current bet. Action amount is "
                  + getPlayerBetAmountThisAction(3, player, currentRoundBetAmount)
                  + " but current bet is " + currentRoundBetAmount + ", player's previous bet is "
                  + player.getCurrentBet());
        }
        gameService.placeBet(player,
            getPlayerBetAmountThisAction(3, player, currentRoundBetAmount));
        log.info("Player {} {} with {} chips, total bet amount: {}.", playerName,
            action.name().toLowerCase(),
            getPlayerBetAmountThisAction(3, player, currentRoundBetAmount),
            player.getCurrentBet());
        break;

      case BET_OR_RAISE_100:
        if (getPlayerBetAmountThisAction(4, player, currentRoundBetAmount)
            <= currentRoundBetAmount - player.getCurrentBet()) {
          throw new IllegalArgumentException(
              "Bet or raise must exceed the current bet. Action amount is "
                  + getPlayerBetAmountThisAction(4, player, currentRoundBetAmount)
                  + " but current bet is " + currentRoundBetAmount + ", player's previous bet is "
                  + player.getCurrentBet());
        }
        gameService.placeBet(player,
            getPlayerBetAmountThisAction(4, player, currentRoundBetAmount));
        log.info("Player {} {} with {} chips, total bet amount: {}.", playerName,
            action.name().toLowerCase(),
            getPlayerBetAmountThisAction(4, player, currentRoundBetAmount),
            player.getCurrentBet());
        break;

      case BET_OR_RAISE_200:
        if (getPlayerBetAmountThisAction(5, player, currentRoundBetAmount)
            <= currentRoundBetAmount - player.getCurrentBet()) {
          throw new IllegalArgumentException(
              "Bet or raise must exceed the current bet. Action amount is "
                  + getPlayerBetAmountThisAction(5, player, currentRoundBetAmount)
                  + " but current bet is " + currentRoundBetAmount + ", player's previous bet is "
                  + player.getCurrentBet());
        }
        gameService.placeBet(player,
            getPlayerBetAmountThisAction(5, player, currentRoundBetAmount));
        log.info("Player {} {} with {} chips, total bet amount: {}.", playerName,
            action.name().toLowerCase(),
            getPlayerBetAmountThisAction(5, player, currentRoundBetAmount),
            player.getCurrentBet());
        break;

      case BET_OR_RAISE_500:
        if (getPlayerBetAmountThisAction(6, player, currentRoundBetAmount)
            <= currentRoundBetAmount - player.getCurrentBet()) {
          throw new IllegalArgumentException(
              "Bet or raise must exceed the current bet. Action amount is "
                  + getPlayerBetAmountThisAction(6, player, currentRoundBetAmount)
                  + " but current bet is " + currentRoundBetAmount + ", player's previous bet is "
                  + player.getCurrentBet());
        }
        gameService.placeBet(player,
            getPlayerBetAmountThisAction(6, player, currentRoundBetAmount));
        log.info("Player {} {} with {} chips, total bet amount: {}.", playerName,
            action.name().toLowerCase(),
            getPlayerBetAmountThisAction(6, player, currentRoundBetAmount),
            player.getCurrentBet());
        break;

      case FOLD:
        gameService.foldPlayer(player);
        break;

      case BIG_BLIND:
        gameService.placeBet(player, BIG_BLIND);
        break;

      case SMALL_BLIND:
        gameService.placeBet(player, SMALL_BLIND);
        break;

      default:
        throw new IllegalArgumentException("Invalid action.");
    }
  }

  private int getPlayerBetAmountThisAction(int actionAbstractIndex, Player player,
      int currentRoundBetAmount) {
    if (AbstractAction.values()[actionAbstractIndex] == AbstractAction.BET_OR_RAISE_30) {
      if (currentRoundBetAmount == 0) {
        return (int) Math.min(player.getChips(), sidePots.isEmpty() ? mainPot.getAmount()
            : sidePots.get(sidePots.size() - 1).getAmount() * 1.3f);
      }
      return (int) Math.min(player.getChips(), currentRoundBetAmount * 1.3f);
    } else if (AbstractAction.values()[actionAbstractIndex] == AbstractAction.BET_OR_RAISE_60) {
      if (currentRoundBetAmount == 0) {
        return (int) Math.min(player.getChips(), sidePots.isEmpty() ? mainPot.getAmount()
            : sidePots.get(sidePots.size() - 1).getAmount() * 1.6f);
      }
      return (int) Math.min(player.getChips(), currentRoundBetAmount * 1.6f);
    } else if (AbstractAction.values()[actionAbstractIndex] == AbstractAction.BET_OR_RAISE_100) {
      if (currentRoundBetAmount == 0) {
        return (int) Math.min(player.getChips(), sidePots.isEmpty() ? mainPot.getAmount()
            : sidePots.get(sidePots.size() - 1).getAmount() * 2.0f);
      }
      return (int) Math.min(player.getChips(), currentRoundBetAmount * 2.0f);
    } else if (AbstractAction.values()[actionAbstractIndex] == AbstractAction.BET_OR_RAISE_200) {
      if (currentRoundBetAmount == 0) {
        return (int) Math.min(player.getChips(), sidePots.isEmpty() ? mainPot.getAmount()
            : sidePots.get(sidePots.size() - 1).getAmount() * 3.0f);
      }
      return (int) Math.min(player.getChips(), currentRoundBetAmount * 3.0f);
    } else if (AbstractAction.values()[actionAbstractIndex] == AbstractAction.BET_OR_RAISE_500) {
      if (currentRoundBetAmount == 0) {
        return (int) Math.min(player.getChips(), sidePots.isEmpty() ? mainPot.getAmount()
            : sidePots.get(sidePots.size() - 1).getAmount() * 6.0f);
      }
      return (int) Math.min(player.getChips(), currentRoundBetAmount * 6.0f);
    } else if (AbstractAction.values()[actionAbstractIndex] == AbstractAction.CHECK_OR_CALL) {
      if (currentRoundBetAmount == 0) {
        return 0;
      } else {
        return Math.min(player.getChips(), currentRoundBetAmount - player.getCurrentBet());
      }
    } else {
      return 0;
    }
  }

  public double leafUtility(int playerIndex,
      List<Player> players) {

    List<Card> community = gameService.getCommunityCards();
    List<Card> myHand = players.get(playerIndex).getHand();

    int myScore = HandEvaluator.evaluateBestOf(concat(myHand, community));
    double totalUtility = 0.0;

    for (int i = 0; i < playerCount; i++) {
      if (i == playerIndex) {
        continue;
      }
      int oppScore = HandEvaluator.evaluateBestOf(concat(players.get(i).getHand(), community));
      totalUtility += myScore < oppScore ? 1.0 : (myScore == oppScore ? 0.0 : -1.0);
    }

    return totalUtility / (playerCount - 1);
  }

  private List<Card> concat(List<Card> hand, List<Card> board) {
    List<Card> combined = new ArrayList<>(hand);
    combined.addAll(board);
    return combined;
  }


}
