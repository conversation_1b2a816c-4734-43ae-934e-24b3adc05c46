package com.example.texasholdem.strategy.train;

import com.example.texasholdem.model.Card;
import com.example.texasholdem.strategy.model.Infoset;
import com.example.texasholdem.strategy.model.InfosetStore;
import com.example.texasholdem.strategy.model.InfosetValue;
import com.example.texasholdem.strategy.train.InfosetStoreKey;
import com.example.texasholdem.service.HandEvaluator;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Performance-optimized caching system for MCCFR training
 * PERFORMANCE OPTIMIZATION: Provides LRU caching for expensive computations
 * Target: 10x speedup for information set and hand evaluation operations
 */
@Slf4j
public class PerformanceOptimizedCaches {
    
    // Cache configuration
    private static final int INFOSET_CACHE_SIZE = 50000;
    private static final int HAND_STRENGTH_CACHE_SIZE = 100000;
    private static final int INFOSET_KEY_CACHE_SIZE = 25000;
    
    // Information Set Caching
    private final LRUCache<Long, InfosetValue> infosetCache;
    private final LRUCache<Long, Integer> infosetKeyCache;
    
    // Hand Evaluation Caching
    private final LRUCache<Long, Short> handStrengthCache;
    
    // Performance monitoring
    private final AtomicLong infosetCacheHits = new AtomicLong(0);
    private final AtomicLong infosetCacheMisses = new AtomicLong(0);
    private final AtomicLong handStrengthCacheHits = new AtomicLong(0);
    private final AtomicLong handStrengthCacheMisses = new AtomicLong(0);
    
    public PerformanceOptimizedCaches() {
        this.infosetCache = new LRUCache<>(INFOSET_CACHE_SIZE);
        this.infosetKeyCache = new LRUCache<>(INFOSET_KEY_CACHE_SIZE);
        this.handStrengthCache = new LRUCache<>(HAND_STRENGTH_CACHE_SIZE);
        
        log.info("Initialized performance caches - InfoSet: {}, HandStrength: {}, InfoSetKey: {}",
                INFOSET_CACHE_SIZE, HAND_STRENGTH_CACHE_SIZE, INFOSET_KEY_CACHE_SIZE);
    }
    
    /**
     * Get or create cached InfosetValue
     * PERFORMANCE: Reduces expensive infoset creation and lookup operations
     */
    public InfosetValue getOrCreateCachedInfoset(int playerId, Infoset infoset,
                                                InfosetStore store) {
        long key = infoset.toLongKey();

        InfosetValue cached = infosetCache.get(key);
        if (cached != null) {
            infosetCacheHits.incrementAndGet();
            log.debug("InfoSet cache HIT for player {} (key: {})", playerId, key);
            return cached;
        }

        // Cache miss - create new InfosetValue using InfosetStore's direct method
        infosetCacheMisses.incrementAndGet();
        InfosetValue newValue = store.getOrCreate(playerId, infoset);

        // Cache the result
        infosetCache.put(key, newValue);
        log.debug("InfoSet cache MISS for player {} (key: {}) - created and cached", playerId, key);

        return newValue;
    }
    
    /**
     * Get or create cached abstraction key (for compatibility with existing code)
     * PERFORMANCE: Reduces expensive infoset key creation operations
     */
    public int getOrCreateCachedAbstractionKey(Infoset infoset) {
        long key = infoset.toLongKey();

        Integer cached = infosetKeyCache.get(key);
        if (cached != null) {
            return cached;
        }

        // Cache miss - create new abstraction key
        int newKey = InfosetStoreKey.abstractInfoset(infoset);
        infosetKeyCache.put(key, newKey);

        return newKey;
    }
    
    /**
     * Get cached hand strength evaluation
     * PERFORMANCE: Reduces expensive HandEvaluator.evaluateBestOf() calls
     */
    public short getCachedHandStrength(List<Card> cards) {
        long cardKey = encodeCardsToBits(cards);
        
        Short cached = handStrengthCache.get(cardKey);
        if (cached != null) {
            handStrengthCacheHits.incrementAndGet();
            log.debug("Hand strength cache HIT for {} cards (key: {})", cards.size(), cardKey);
            return cached;
        }
        
        // Cache miss - evaluate hand strength
        handStrengthCacheMisses.incrementAndGet();
        short strength = HandEvaluator.evaluateBestOf(cards);
        
        // Cache the result
        handStrengthCache.put(cardKey, strength);
        log.debug("Hand strength cache MISS for {} cards (key: {}) - evaluated: {}", 
                 cards.size(), cardKey, strength);
        
        return strength;
    }
    
    /**
     * Encode cards to a unique bit representation for caching
     * PERFORMANCE: Fast card encoding for cache keys
     */
    private long encodeCardsToBits(List<Card> cards) {
        long encoded = 0;
        for (int i = 0; i < Math.min(cards.size(), 7); i++) { // Max 7 cards (2 hole + 5 community)
            Card card = cards.get(i);
            int cardValue = card.getRank().ordinal() * 4 + card.getSuit().ordinal();
            encoded |= ((long) cardValue) << (i * 8); // 8 bits per card
        }
        return encoded;
    }
    
    /**
     * Get cache performance statistics
     * PERFORMANCE MONITORING: Track cache effectiveness
     */
    public CacheStatistics getStatistics() {
        long totalInfosetRequests = infosetCacheHits.get() + infosetCacheMisses.get();
        long totalHandStrengthRequests = handStrengthCacheHits.get() + handStrengthCacheMisses.get();
        
        double infosetHitRate = totalInfosetRequests > 0 ? 
            (double) infosetCacheHits.get() / totalInfosetRequests : 0.0;
        double handStrengthHitRate = totalHandStrengthRequests > 0 ? 
            (double) handStrengthCacheHits.get() / totalHandStrengthRequests : 0.0;
        
        return new CacheStatistics(
            infosetCacheHits.get(), infosetCacheMisses.get(), infosetHitRate,
            handStrengthCacheHits.get(), handStrengthCacheMisses.get(), handStrengthHitRate,
            infosetCache.size(), handStrengthCache.size()
        );
    }
    
    /**
     * Clear all caches
     * PERFORMANCE: Reset caches for new training sessions
     */
    public void clearCaches() {
        infosetCache.clear();
        infosetKeyCache.clear();
        handStrengthCache.clear();
        
        infosetCacheHits.set(0);
        infosetCacheMisses.set(0);
        handStrengthCacheHits.set(0);
        handStrengthCacheMisses.set(0);
        
        log.info("Cleared all performance caches");
    }
    
    /**
     * Cache statistics data class
     */
    public static class CacheStatistics {
        public final long infosetHits;
        public final long infosetMisses;
        public final double infosetHitRate;
        public final long handStrengthHits;
        public final long handStrengthMisses;
        public final double handStrengthHitRate;
        public final int infosetCacheSize;
        public final int handStrengthCacheSize;
        
        public CacheStatistics(long infosetHits, long infosetMisses, double infosetHitRate,
                              long handStrengthHits, long handStrengthMisses, double handStrengthHitRate,
                              int infosetCacheSize, int handStrengthCacheSize) {
            this.infosetHits = infosetHits;
            this.infosetMisses = infosetMisses;
            this.infosetHitRate = infosetHitRate;
            this.handStrengthHits = handStrengthHits;
            this.handStrengthMisses = handStrengthMisses;
            this.handStrengthHitRate = handStrengthHitRate;
            this.infosetCacheSize = infosetCacheSize;
            this.handStrengthCacheSize = handStrengthCacheSize;
        }
        
        @Override
        public String toString() {
            return String.format(
                "CacheStats{InfoSet: %d hits, %d misses (%.2f%% hit rate, %d cached), " +
                "HandStrength: %d hits, %d misses (%.2f%% hit rate, %d cached)}",
                infosetHits, infosetMisses, infosetHitRate * 100,
                infosetCacheSize, handStrengthHits, handStrengthMisses, 
                handStrengthHitRate * 100, handStrengthCacheSize
            );
        }
    }
    
    /**
     * Simple LRU Cache implementation
     * PERFORMANCE: Efficient caching with automatic eviction
     */
    private static class LRUCache<K, V> {
        private final int maxSize;
        private final ConcurrentHashMap<K, Node<K, V>> cache;
        private Node<K, V> head;
        private Node<K, V> tail;
        
        public LRUCache(int maxSize) {
            this.maxSize = maxSize;
            this.cache = new ConcurrentHashMap<>();
            this.head = new Node<>(null, null);
            this.tail = new Node<>(null, null);
            head.next = tail;
            tail.prev = head;
        }
        
        public synchronized V get(K key) {
            Node<K, V> node = cache.get(key);
            if (node == null) {
                return null;
            }
            
            // Move to head (most recently used)
            moveToHead(node);
            return node.value;
        }
        
        public synchronized void put(K key, V value) {
            Node<K, V> existing = cache.get(key);
            if (existing != null) {
                existing.value = value;
                moveToHead(existing);
                return;
            }
            
            Node<K, V> newNode = new Node<>(key, value);
            cache.put(key, newNode);
            addToHead(newNode);
            
            if (cache.size() > maxSize) {
                Node<K, V> tail = removeTail();
                cache.remove(tail.key);
            }
        }
        
        public synchronized void clear() {
            cache.clear();
            head.next = tail;
            tail.prev = head;
        }
        
        public int size() {
            return cache.size();
        }
        
        private void addToHead(Node<K, V> node) {
            node.prev = head;
            node.next = head.next;
            head.next.prev = node;
            head.next = node;
        }
        
        private void removeNode(Node<K, V> node) {
            node.prev.next = node.next;
            node.next.prev = node.prev;
        }
        
        private void moveToHead(Node<K, V> node) {
            removeNode(node);
            addToHead(node);
        }
        
        private Node<K, V> removeTail() {
            Node<K, V> lastNode = tail.prev;
            removeNode(lastNode);
            return lastNode;
        }
        
        private static class Node<K, V> {
            K key;
            V value;
            Node<K, V> prev;
            Node<K, V> next;
            
            Node(K key, V value) {
                this.key = key;
                this.value = value;
            }
        }
    }
}
