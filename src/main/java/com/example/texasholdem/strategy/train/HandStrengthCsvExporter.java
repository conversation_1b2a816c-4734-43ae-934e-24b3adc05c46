package com.example.texasholdem.strategy.train;

import com.example.texasholdem.model.Card;
import com.example.texasholdem.service.HandEvaluator;
import org.springframework.stereotype.Service;

import java.io.FileWriter;
import java.io.IOException;
import java.util.*;

public class HandStrengthCsvExporter {
  public static void main(String[] args) throws IOException {
    String outputFile = "hand_strength_kmeans_raw.csv";
    FileWriter writer = new FileWriter(outputFile);
    writer.write("handStrength\n");

    List<Card> deck = generateDeck();
    Set<String> seenCombos = new HashSet<>();

    for (int i = 0; i < deck.size(); i++) {
      for (int j = i + 1; j < deck.size(); j++) {
        Card card1 = deck.get(i);
        Card card2 = deck.get(j);

        String key = card1.toString() + "-" + card2.toString();
        String reverseKey = card2.toString() + "-" + card1.toString();
        if (seenCombos.contains(key) || seenCombos.contains(reverseKey)) continue;
        seenCombos.add(key);


        for (int s = 0; s < 50; s++) {
          List<Card> community = generateRandomCommunityCards(card1, card2);
          List<Card> allCards = new ArrayList<>();
          allCards.add(card1);
          allCards.add(card2);
          allCards.addAll(community);

          short strength = HandEvaluator.evaluateBestOf(allCards);
          writer.write(strength + "\n");
        }
      }
    }

    writer.close();
    System.out.println("Exported hand strength data to " + outputFile);
  }

  private static List<Card> generateDeck() {
    List<Card> deck = new ArrayList<>();
    for (Card.Suit suit : Card.Suit.values()) {
      for (Card.Rank rank : Card.Rank.values()) {
        deck.add(new Card(rank, suit));
      }
    }
    return deck;
  }

  private static List<Card> generateRandomCommunityCards(Card... excluded) {
    Set<Card> excludeSet = new HashSet<>(Arrays.asList(excluded));
    List<Card> deck = generateDeck();
    deck.removeAll(excludeSet);
    Collections.shuffle(deck);
    return deck.subList(0, 5);
  }
}
