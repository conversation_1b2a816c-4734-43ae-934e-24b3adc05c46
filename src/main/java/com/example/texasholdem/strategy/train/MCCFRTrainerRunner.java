package com.example.texasholdem.strategy.train;

import com.example.texasholdem.service.GameService;
import com.example.texasholdem.strategy.BlueprintStrategy;
import com.example.texasholdem.strategy.model.InfosetStore;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;

public class MCCFRTrainerRunner {

  public static void main(String[] args) throws IOException {
    int iterations = 100_000;
    int playerCount = 6;
    int numActions = 7;

    String checkpointPath = "checkpoint.gz";
    String outputPath = "final_strategy.gz";
    boolean useShardedStore = false;
    boolean outputReadableStrategy = false;

    InfosetStore store = new InfosetStore(playerCount, numActions);

    File checkpoint = new File(checkpointPath);
    if (checkpoint.exists()) {
      System.out.println("Resuming from checkpoint...");
      store = StrategySerializer.loadCompressed(new FileInputStream(checkpoint));
    }

    GameService gameService = new GameService();
    MCCFRTrainer trainer = new MCCFRTrainer(store, gameService);

    long startTime = System.currentTimeMillis();
    trainer.train(iterations, playerCount);
    long endTime = System.currentTimeMillis();

    System.out.printf("✅ Training completed in %.2f seconds\n",
        (endTime - startTime) / 1000.0);

    StrategySerializer.saveCompressed(store, new FileOutputStream("final_strategy.gz"));

    if (outputReadableStrategy) {
      for(int i=0; i<playerCount; i++){
        StrategySerializer.saveReadable(i,store, new FileOutputStream("readable_strategy.txt"));
      }

    }

    StrategySerializer.printCompressionStats(store);
  }
}