package com.example.texasholdem.strategy.train;

import com.example.texasholdem.model.ActionInstance;
import com.example.texasholdem.model.Card;
import com.example.texasholdem.service.HandEvaluator;
import com.example.texasholdem.strategy.model.Infoset;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class InfosetStoreKey {

  private static final int[] HAND_STRENGTH_CLUSTERS = new int[7463];
  private static final int NUM_HISTORY_BUCKETS = 8;
  private static final Map<String, Integer> actionPatternToBucket = new HashMap<>();
  private static final int MAX_ACTIONS_ENCODED = 4;


  static {
    InputStream in = InfosetStoreKey.class.getResourceAsStream("/hand_strength_kmeans_100.csv");
    if (in != null) {
      try (BufferedReader reader = new BufferedReader(new InputStreamReader(in))) {
        String line;
        while ((line = reader.readLine()) != null) {
          String[] parts = line.split(",");
          HAND_STRENGTH_CLUSTERS[Integer.parseInt(parts[0])] = Integer.parseInt(parts[1]);
        }
      } catch (IOException error) {
        throw new RuntimeException("Failed to load hand strength clusters", error);
      }
    } else {
      Arrays.fill(HAND_STRENGTH_CLUSTERS, 0);
    }
  }


  static {
    actionPatternToBucket.put("C", 0);
    actionPatternToBucket.put("R", 1);
    actionPatternToBucket.put("CR", 2);
    actionPatternToBucket.put("RR", 3);
    actionPatternToBucket.put("CC", 4);
    actionPatternToBucket.put("CRR", 5);
    actionPatternToBucket.put("CRC", 6);
  }

  public static int abstractInfoset(Infoset infoset) {
    return (
        infoset.getRound() << 12)
        | (handStrengthBucket(
        HandEvaluator.evaluateBestOf(decodeCardsFromBits(infoset.getHoleAndCommunityBits()))) << 5)
        // 1-7462
        | (bettingBucket(infoset.getCurrentRoundBetAmount(), 20) << 3)
        | (positionBucket(infoset.getPlayerIndex(), 6) << 2)
        | actionHistoryBucket(infoset.getActionHistory());
  }

  private static int handStrengthBucket(short handStrength) {
    return HAND_STRENGTH_CLUSTERS[handStrength];
  }

  private static int bettingBucket(int betAmount, int bigBlind) {
    if (betAmount == 0) {
      return 0;
    }

    double betBucket = (double) betAmount / bigBlind;
    if (betBucket <= 0.5) {
      return 1;
    } else if (betBucket <= 1.5) {
      return 2;
    } else if (betBucket <= 4) {
      return 3;
    } else if (betBucket <= 10) {
      return 4;
    } else {
      return 5;
    }
  }

  private static int positionBucket(int playerIndex, int numPlayers) {
    if (numPlayers < 6) {
      return playerIndex;
    }

    switch (playerIndex) {
      case 0:
      case 1:
        return 0; // UTG, UTG+1
      case 2:
      case 3:
        return 1; // HJ, CO
      case 4:
        return 2; // BTN
      case 5:
        return 3; // SB
      default:
        return 4; // BB or fallback
    }
  }

  private static int actionHistoryBucket(long actionBits) {
    return switch ((int) actionBits) {
      case 0b01 -> 0;       // C
      case 0b10 -> 1;       // R
      case 0b01_10 -> 2;    // C-R
      case 0b10_10 -> 3;    // R-R
      case 0b01_01 -> 4;    // C-C
      case 0b01_10_10 -> 5; // C-R-R
      case 0b01_10_01 -> 6; // C-R-C
      case 0b01_10_00 -> 7; // C-R-F
      default -> Math.floorMod(Long.hashCode(actionBits), NUM_HISTORY_BUCKETS);
    };
  }

  private static String decodeActionPattern(long hash) {
    return "";
  }

  public static long encodeActionTrace(List<ActionInstance> actions) {
    long encoded = 0;
    for (int i = 0; i < Math.min(actions.size(), MAX_ACTIONS_ENCODED); i++) {
      int code = switch (actions.get(
          actions.size() - Math.min(actions.size(), MAX_ACTIONS_ENCODED) + i).getAction()) {
        case FOLD -> 0b00;
        case CALL -> 0b01;
        case RAISE -> 0b10;
        default -> 0b00;
      };
      encoded = (encoded << 2) | code;
    }
    return encoded;
  }

  private static List<Card> decodeCardsFromBits(long bits) {
    List<Card> cards = new ArrayList<>();
    for (int i = 0; i < 7; i++) {
      int cardIndex = (int) ((bits >> (i * 6)) & 0x3F); // 6 bits per card
      if (cardIndex >= 52) {
        break;
      }
      int suit = cardIndex / 13;
      int rank = cardIndex % 13;
      cards.add(new Card(Card.Rank.values()[rank], fromSuitIndex(suit)));
    }
    return cards;
  }

  private static Card.Suit fromSuitIndex(int index) {
    for (Card.Suit suit : Card.Suit.values()) {
      if (suit.getSuitIndex() == index) {
        return suit;
      }
    }
    throw new IllegalArgumentException("Invalid suit index: " + index);
  }
}
