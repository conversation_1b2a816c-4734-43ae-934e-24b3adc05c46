package com.example.texasholdem.strategy;

import com.example.texasholdem.model.*;

import com.example.texasholdem.strategy.model.ActionTrace;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class StrategyManager {

  private final Map<String, Strategy> playerStrategies = new HashMap<>();

  public StrategyManager(List<Player> players) {
    for (Player player : players) {
      if (player.getName().contains("Pluribus")) {
        //playerStrategies.put(player.getName(), new BlueprintStrategy());
      } else {
        playerStrategies.put(player.getName(), new RandomStrategy());
      }
    }
  }

  public ActionInstance decideAction(
      List<Card> holeCards,
      List<Card> communityCards,
      BettingRound currentRound,
      Player player,
      int currentRoundBetAmount,
      List<ActionTrace> actionHistory,
      int potSize
  ) {
    if (playerStrategies.get(player.getName()) == null) {
      throw new IllegalStateException("No strategy assigned for player: " + player.getName());
    }

    return playerStrategies.get(player.getName()).decideAction(
        holeCards,
        communityCards,
        currentRound,
        player,
        currentRoundBetAmount,
        actionHistory,
        potSize
    );
  }
}