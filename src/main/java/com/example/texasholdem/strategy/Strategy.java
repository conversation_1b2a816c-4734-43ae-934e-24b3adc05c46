package com.example.texasholdem.strategy;

import com.example.texasholdem.model.ActionInstance;
import com.example.texasholdem.model.Card;
import com.example.texasholdem.model.BettingRound;

import com.example.texasholdem.model.Player;
import com.example.texasholdem.strategy.model.ActionTrace;
import java.util.List;

public interface Strategy {
    ActionInstance decideAction(
        List<Card> holeCards,
        List<Card> communityCards,
        BettingRound currentRound,
        Player player,
        int currentRoundBetAmount,
        List<ActionTrace> actionHistory,
        int potSize
    );
}