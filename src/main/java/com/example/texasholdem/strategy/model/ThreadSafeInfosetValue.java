package com.example.texasholdem.strategy.model;

import java.util.Arrays;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReferenceArray;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * Thread-safe implementation of InfosetValue for concurrent MCCFR training
 * <p>
 * Provides thread-safe access to regret values, action counters, and strategy calculation while
 * maintaining performance and API compatibility with existing MCCFRTrainer.
 * <p>
 * Key features: - Atomic operations for regret and action counter updates - Read-write locks for
 * consistent strategy calculation - Thread-safe visit count and last updated turn tracking -
 * Backward compatibility with existing InfosetValue API - Optimized for high-frequency concurrent
 * access during MCCFR training
 */
@Slf4j
public class ThreadSafeInfosetValue {

  // Thread-safe regret storage using AtomicReferenceArray
  private final AtomicReferenceArray<Float> regret;

  // Thread-safe action counter storage
  private final AtomicReferenceArray<Float> actionCounter;

  // Thread-safe visit count and turn tracking
  private final AtomicInteger visitCount;
  private final AtomicInteger lastUpdatedTurn;

  // Read-write lock for strategy calculation consistency
  private final ReentrantReadWriteLock strategyLock;

  // Number of actions for this infoset
  @Getter
  private final int numActions;

  // Performance monitoring
  private final AtomicInteger regretUpdateCount;
  private final AtomicInteger strategyCalculationCount;

  /**
   * Constructor for thread-safe InfosetValue
   *
   * @param numActions Number of actions available at this information set
   */
  public ThreadSafeInfosetValue(int numActions) {
    this.numActions = numActions;
    this.regret = new AtomicReferenceArray<>(numActions);
    this.actionCounter = new AtomicReferenceArray<>(numActions);
    this.visitCount = new AtomicInteger(0);
    this.lastUpdatedTurn = new AtomicInteger(-1);
    this.strategyLock = new ReentrantReadWriteLock();
    this.regretUpdateCount = new AtomicInteger(0);
    this.strategyCalculationCount = new AtomicInteger(0);

    // Initialize arrays with zero values
    for (int i = 0; i < numActions; i++) {
      this.regret.set(i, 0.0f);
      this.actionCounter.set(i, 0.0f);
    }

    log.debug("ThreadSafeInfosetValue created with {} actions", numActions);
  }

  /**
   * Thread-safe regret update for a single action
   *
   * @param actionIndex Index of the action to update
   * @param delta       Regret delta to add
   */
  public void addRegret(int actionIndex, float delta) {
    if (actionIndex < 0 || actionIndex >= numActions) {
      log.warn("Invalid action index {} for addRegret (max: {})", actionIndex, numActions - 1);
      return;
    }

    // Atomic update using compareAndSet loop
    Float current;
    Float updated;
    do {
      current = regret.get(actionIndex);
      updated = current + delta;
    } while (!regret.compareAndSet(actionIndex, current, updated));

    regretUpdateCount.incrementAndGet();
    log.trace("Updated regret for action {}: {} -> {} (delta: {})",
        actionIndex, current, updated, delta);
  }

  /**
   * Thread-safe regret array update (for compatibility)
   *
   * @param regrets     New regret values
   * @param actionIndex Specific action index to update
   */
  public void updateRegret(float[] regrets, int actionIndex) {
    if (actionIndex < 0 || actionIndex >= numActions || regrets.length != numActions) {
      log.warn(
          "Invalid parameters for updateRegret: actionIndex={}, regrets.length={}, numActions={}",
          actionIndex, regrets.length, numActions);
      return;
    }

    regret.set(actionIndex, regrets[actionIndex]);
    regretUpdateCount.incrementAndGet();
  }

  /**
   * Thread-safe action counter update
   *
   * @param strategy Strategy array to add to action counters
   */
  public void updateActionCounter(float[] strategy) {
    if (strategy.length != numActions) {
      log.warn("Strategy length {} does not match numActions {}", strategy.length, numActions);
      return;
    }

    strategyLock.writeLock().lock();
    try {
      for (int i = 0; i < strategy.length; i++) {
        Float current = actionCounter.get(i);
        actionCounter.set(i, current + strategy[i]);
      }
      visitCount.incrementAndGet();
      log.trace("Updated action counters, visit count now: {}", visitCount.get());
    } finally {
      strategyLock.writeLock().unlock();
    }
  }

  /**
   * Thread-safe strategy calculation using regret matching Ensures consistent read of all regret
   * values during calculation
   *
   * @return Normalized strategy array
   */
  public float[] calculateStrategy() {
    strategyLock.readLock().lock();
    try {
      strategyCalculationCount.incrementAndGet();

      float[] strategy = new float[numActions];
      float sum = 0.0f;

      // Calculate positive regret sum with consistent reads
      for (int i = 0; i < numActions; i++) {
        Float regretValue = regret.get(i);
        sum += Math.max(regretValue, 0.0f);
      }

      // Normalize strategy
      if (sum > 1e-6f) {
        for (int i = 0; i < strategy.length; i++) {
          Float regretValue = regret.get(i);
          strategy[i] = Math.max(regretValue, 0.0f) / sum;
        }
      } else {
        // Uniform strategy when no positive regrets
        Arrays.fill(strategy, 1.0f / numActions);
      }

      log.trace("Calculated strategy with regret sum: {}", sum);
      return strategy;

    } finally {
      strategyLock.readLock().unlock();
    }
  }

  /**
   * Thread-safe average strategy calculation
   *
   * @return Average strategy based on action counters
   */
  public float[] getAverageStrategy() {
    strategyLock.readLock().lock();
    try {
      float[] avgStrategy = new float[numActions];
      float sum = 0.0f;

      // Calculate sum of action counters with consistent reads
      for (int i = 0; i < numActions; i++) {
        Float counterValue = actionCounter.get(i);
        sum += counterValue;
      }

      // Normalize average strategy
      if (sum > 1e-6f) {
        for (int i = 0; i < avgStrategy.length; i++) {
          Float counterValue = actionCounter.get(i);
          avgStrategy[i] = counterValue / sum;
        }
      } else {
        // Uniform strategy when no action counters
        Arrays.fill(avgStrategy, 1.0f / numActions);
      }

      return avgStrategy;

    } finally {
      strategyLock.readLock().unlock();
    }
  }

  /**
   * Thread-safe discount application for Linear CFR
   *
   * @param currentTurn       Current training iteration
   * @param thresholdInterval Interval for applying discount
   * @param discount          Discount factor to apply
   */
  public void applyDiscountIfStale(int currentTurn, int thresholdInterval, float discount) {
    int lastTurn = lastUpdatedTurn.get();

    if (currentTurn - lastTurn >= thresholdInterval) {
      strategyLock.writeLock().lock();
      try {
        // Apply discount to regrets
        for (int i = 0; i < numActions; i++) {
          Float currentRegret = regret.get(i);
          regret.set(i, currentRegret * discount);
        }

        // Apply discount to action counters
        for (int i = 0; i < numActions; i++) {
          Float currentCounter = actionCounter.get(i);
          actionCounter.set(i, currentCounter * discount);
        }

        lastUpdatedTurn.set(currentTurn);
        log.trace("Applied discount {} at turn {} (last update: {})",
            discount, currentTurn, lastTurn);

      } finally {
        strategyLock.writeLock().unlock();
      }
    }
  }

  /**
   * Thread-safe reset of all values
   */
  public void reset() {
    strategyLock.writeLock().lock();
    try {
      for (int i = 0; i < numActions; i++) {
        regret.set(i, 0.0f);
        actionCounter.set(i, 0.0f);
      }
      visitCount.set(0);
      lastUpdatedTurn.set(-1);
      log.debug("Reset ThreadSafeInfosetValue");
    } finally {
      strategyLock.writeLock().unlock();
    }
  }

  /**
   * Thread-safe pruning check
   *
   * @param threshold Pruning threshold
   * @return True if this infoset should be pruned
   */
  public boolean shouldPrune(int threshold) {
    strategyLock.readLock().lock();
    try {
      float sum = 0.0f;
      for (int i = 0; i < numActions; i++) {
        Float regretValue = regret.get(i);
        sum += regretValue;
      }
      return sum < threshold;
    } finally {
      strategyLock.readLock().unlock();
    }
  }

  // Compatibility methods for existing API

  /**
   * Get regret array (thread-safe copy)
   *
   * @return Copy of current regret values
   */
  public float[] getRegret() {
    strategyLock.readLock().lock();
    try {
      float[] regretCopy = new float[numActions];
      for (int i = 0; i < numActions; i++) {
        regretCopy[i] = regret.get(i);
      }
      return regretCopy;
    } finally {
      strategyLock.readLock().unlock();
    }
  }

  /**
   * Get action counter array (thread-safe copy)
   *
   * @return Copy of current action counter values
   */
  public float[] getActionCounter() {
    strategyLock.readLock().lock();
    try {
      float[] counterCopy = new float[numActions];
      for (int i = 0; i < numActions; i++) {
        counterCopy[i] = actionCounter.get(i);
      }
      return counterCopy;
    } finally {
      strategyLock.readLock().unlock();
    }
  }

  /**
   * Set action counter array (thread-safe)
   *
   * @param actionCounter New action counter values
   */
  public void setActionCounter(float[] actionCounter) {
    if (actionCounter.length != numActions) {
      log.warn("Action counter length {} does not match numActions {}",
          actionCounter.length, numActions);
      return;
    }

    strategyLock.writeLock().lock();
    try {
      for (int i = 0; i < numActions; i++) {
        this.actionCounter.set(i, actionCounter[i]);
      }
    } finally {
      strategyLock.writeLock().unlock();
    }
  }

  /**
   * Set regret array (thread-safe)
   *
   * @param regrets New regret values
   */
  public void setRegret(float[] regrets) {
    if (regrets.length != numActions) {
      log.warn("Regret array length {} does not match numActions {}",
          regrets.length, numActions);
      return;
    }

    strategyLock.writeLock().lock();
    try {
      for (int i = 0; i < numActions; i++) {
        this.regret.set(i, regrets[i]);
      }
    } finally {
      strategyLock.writeLock().unlock();
    }
  }

  /**
   * Get visit count (thread-safe)
   *
   * @return Current visit count
   */
  public int getVisitCount() {
    return visitCount.get();
  }

  /**
   * Get last updated turn (thread-safe)
   *
   * @return Last updated turn number
   */
  public int getLastUpdatedTurn() {
    return lastUpdatedTurn.get();
  }

  /**
   * Get performance statistics
   *
   * @return Performance statistics string
   */
  public String getPerformanceStats() {
    return String.format(
        "ThreadSafeInfosetValue[actions=%d, visits=%d, regretUpdates=%d, strategyCalcs=%d]",
        numActions, visitCount.get(), regretUpdateCount.get(), strategyCalculationCount.get());
  }
}
