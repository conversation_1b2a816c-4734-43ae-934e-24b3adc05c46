package com.example.texasholdem.strategy;

import com.example.texasholdem.model.ActionInstance;
import com.example.texasholdem.model.Card;
import com.example.texasholdem.model.BettingRound;
import com.example.texasholdem.model.Action;
import com.example.texasholdem.model.Player;
import com.example.texasholdem.strategy.model.ActionTrace;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class RandomStrategy implements Strategy {

  private final Random random = new Random();
  private static final int RAISE_AMOUNT = 20;

  @Override
  public ActionInstance decideAction(
      List<Card> holeCards,
      List<Card> communityCards,
      BettingRound currentRound,
      Player player,
      int currentRoundBetAmount,
      List<ActionTrace> actionHistory,
      int potSize
  ) {
    List<Action> possibleActions = generateLegalActionsNew(player, currentRoundBetAmount, holeCards,
        currentRound);
    //Action bestAction = possibleActions.get(bestIndex);
    //int betAmount = (bestAction == Action.BET || bestAction == Action.RAISE) ? currentBet * 2 : 0;

    //return new ActionInstance(bestAction, betAmount);

    if (possibleActions.isEmpty()) {
      return new ActionInstance(Action.FOLD, 0);
    }

    Action randomAction = possibleActions.get(random.nextInt(possibleActions.size()));

    if (randomAction == Action.BET) {
      return new ActionInstance(randomAction, RAISE_AMOUNT);
    } else if (randomAction == Action.RAISE) {
      return new ActionInstance(randomAction,
          currentRoundBetAmount - player.getCurrentBet() + RAISE_AMOUNT);
    } else if (randomAction == Action.CALL) {
      return new ActionInstance(randomAction, currentRoundBetAmount - player.getCurrentBet());
    } else {
      return new ActionInstance(randomAction, 0);
    }
  }

  private List<Action> generateLegalActionsNew(Player player, int currentRoundBetAmount,
      List<Card> holeCards, BettingRound currentRound) {
    List<Action> actions = new ArrayList<>();
    if (holeCards.isEmpty() || currentRound == null || player.getChips() == 0
        || player.isFolded()) {
      actions.add(Action.FOLD);
      return actions;
    }
    if (currentRoundBetAmount == 0) {
      ActionInstance bet = new ActionInstance(Action.BET, 0);
      bet.setAmount(RAISE_AMOUNT);
      actions.add(bet.getAction());
      actions.add(Action.CHECK);
      return actions;
    } else {
      if (player.getChips() <= currentRoundBetAmount) {
        actions.add(Action.FOLD);
        actions.add(Action.CALL);
        return actions;
      } else {
        ActionInstance raise = new ActionInstance(Action.RAISE, 0);
        int onTopAmount = currentRoundBetAmount - player.getCurrentBet();
        raise.setAmount(onTopAmount + RAISE_AMOUNT);
        actions.add(raise.getAction());
        actions.add(Action.CALL);
        actions.add(Action.FOLD);
        return actions;
      }
    }
  }
}