package com.example.texasholdem.strategy;

import com.example.texasholdem.model.*;
import com.example.texasholdem.service.HandEvaluator;
import com.example.texasholdem.strategy.model.*;
import com.example.texasholdem.strategy.train.InfosetStoreKey;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Pluribus-style strategy implementation with two-stage approach: 1. Blueprint strategy
 * (precomputed via MCCFR) 2. Real-time search (limited depth subgame solving)
 */
@Slf4j
public class PluribusStrategy implements Strategy {

  // HOT-SWAPPING CAPABILITY: Thread-safe blueprint store management
  private final AtomicReference<InfosetStore> blueprintStore;
  private final ReentrantReadWriteLock swapLock;
  private volatile String currentStrategyVersion;
  private final Random random = new Random();

  // Real-time search parameters
  private static final int SEARCH_DEPTH = 3;
  private static final int MAX_ACTIONS_TO_CONSIDER = 4;
  private static final long SEARCH_TIMEOUT_MS = 300; // 300ms for real-time play
  private static final int MIN_SEARCH_DEPTH = 1; // Minimum depth before timeout
  private static final double BLUEPRINT_BLEND_FACTOR = 0.3; // 30% blueprint, 70% search

  public PluribusStrategy(InfosetStore blueprintStore) {
    this.blueprintStore = new AtomicReference<>(blueprintStore);
    this.swapLock = new ReentrantReadWriteLock();
    this.currentStrategyVersion = "initial";
    log.info("PluribusStrategy initialized with hot-swapping capability");
  }

  // ===== HOT-SWAPPING CAPABILITY METHODS =====

  /**
   * Hot-swap the blueprint strategy without interrupting gameplay
   *
   * @param newBlueprintStore New strategy to use
   * @param version           Version identifier for the new strategy
   * @return True if swap was successful
   */
  public boolean swapBlueprintStrategy(InfosetStore newBlueprintStore, String version) {
    if (newBlueprintStore == null) {
      log.error("Cannot swap to null blueprint strategy");
      return false;
    }

    swapLock.writeLock().lock();
    try {
      long startTime = System.currentTimeMillis();

      // Validate new strategy before swapping
      if (!validateBlueprintStrategy(newBlueprintStore)) {
        log.error("Blueprint strategy validation failed for version: {}", version);
        return false;
      }

      InfosetStore oldStore = this.blueprintStore.get();
      this.blueprintStore.set(newBlueprintStore);
      this.currentStrategyVersion = version;

      long swapTime = System.currentTimeMillis() - startTime;
      log.info("Blueprint strategy hot-swapped successfully in {}ms: {} -> {}",
          swapTime, getCurrentStrategyVersion(), version);

      // Enable caching on new strategy for optimal performance
      if (newBlueprintStore.getCacheStatistics() != null) {
        newBlueprintStore.setCachingEnabled(true);
        log.debug("Strategy caching enabled for new blueprint: {}", version);
      }

      return true;

    } catch (Exception e) {
      log.error("Failed to hot-swap blueprint strategy to version: {}", version, e);
      return false;
    } finally {
      swapLock.writeLock().unlock();
    }
  }

  /**
   * Get current strategy version
   *
   * @return Current strategy version identifier
   */
  public String getCurrentStrategyVersion() {
    return currentStrategyVersion;
  }

  /**
   * Get current blueprint store (thread-safe)
   *
   * @return Current InfosetStore
   */
  public InfosetStore getCurrentBlueprintStore() {
    return blueprintStore.get();
  }

  /**
   * Validate blueprint strategy before hot-swapping
   *
   * @param store Strategy to validate
   * @return True if strategy is valid
   */
  private boolean validateBlueprintStrategy(InfosetStore store) {
    try {
      // Basic validation checks
      if (store.getNumPlayers() <= 0 || store.getNumActions() <= 0) {
        log.error("Invalid strategy: players={}, actions={}",
            store.getNumPlayers(), store.getNumActions());
        return false;
      }

      // Check for reasonable number of infosets
      long totalInfosets = 0;
      for (int p = 0; p < store.getNumPlayers(); p++) {
        totalInfosets += store.getMapForPlayer(p).size();
        totalInfosets += store.getAbstractionMapForPlayer(p).size();
      }

      if (totalInfosets == 0) {
        log.error("Invalid strategy: no information sets found");
        return false;
      }

      log.debug("Strategy validation passed: {} players, {} actions, {} infosets",
          store.getNumPlayers(), store.getNumActions(), totalInfosets);
      return true;

    } catch (Exception e) {
      log.error("Exception during strategy validation", e);
      return false;
    }
  }

  @Override
  public ActionInstance decideAction(List<Card> holeCards, List<Card> communityCards,
      BettingRound currentRound, Player player, int currentRoundBetAmount,
      List<ActionTrace> actionHistory, int potSize) {

    // For preflop, use blueprint strategy directly (since that's what we trained)
    if (currentRound == BettingRound.PREFLOP) {
      return getBlueprintAction(holeCards, communityCards, currentRound, player,
          currentRoundBetAmount, actionHistory, potSize);
    }

    // For post-flop, combine blueprint with real-time search
    return getRealTimeSearchAction(holeCards, communityCards, currentRound, player,
        currentRoundBetAmount, actionHistory, potSize);
  }

  /**
   * Get action from blueprint strategy (trained MCCFR strategy) Thread-safe access to current
   * blueprint store with caching optimization
   */
  private ActionInstance getBlueprintAction(List<Card> holeCards, List<Card> communityCards,
      BettingRound currentRound, Player player, int currentRoundBetAmount,
      List<ActionTrace> actionHistory, int potSize) {

    swapLock.readLock().lock();
    try {
      InfosetStore currentStore = blueprintStore.get();

      Infoset key = Infoset.of(holeCards, communityCards, currentRound, player,
          currentRoundBetAmount, actionHistory);

      // Try cached strategy first for optimal performance (5-10x faster)
      int abstractionKey = InfosetStoreKey.abstractInfoset(key);
      float[] cachedStrategy = currentStore.getCachedStrategy(player.getPlayerIndex(),
          abstractionKey);

      float[] strategy;
      if (cachedStrategy != null) {
        strategy = cachedStrategy;
        log.debug("🚀 Using cached strategy for player {} (cache hit)", player.getPlayerIndex());
      } else {
        // FIXED: Use correct getOrCreate method for abstraction keys
        InfosetValue infosetValue = currentStore.getOrCreate(player.getPlayerIndex(),
            abstractionKey);
        strategy = infosetValue.getAverageStrategy();
        log.debug("📊 Computing strategy for player {} (cache miss)", player.getPlayerIndex());
      }

      // Filter illegal actions
      float[] legalStrategy = filterIllegalActions(player, currentRoundBetAmount, potSize,
          strategy);

      // Sample action from strategy
      AbstractAction chosenAction = sampleAction(legalStrategy);

      log.debug("🎯 Blueprint action selected: {} for player {}", chosenAction, player.getName());
      return convertToActionInstance(chosenAction, player, currentRoundBetAmount, potSize);

    } finally {
      swapLock.readLock().unlock();
    }
  }

  /**
   * Real-time search for post-flop decisions Implements limited-depth subgame solving with action
   * pruning
   */
  private ActionInstance getRealTimeSearchAction(List<Card> holeCards, List<Card> communityCards,
      BettingRound currentRound, Player player, int currentRoundBetAmount,
      List<ActionTrace> actionHistory, int potSize) {

    // Get blueprint strategy as baseline
    ActionInstance blueprintAction = getBlueprintAction(holeCards, communityCards, currentRound,
        player, currentRoundBetAmount, actionHistory, potSize);

    // Perform limited-depth search
    Map<AbstractAction, Double> actionValues = performLimitedSearch(holeCards, communityCards,
        currentRound, player, currentRoundBetAmount,
        actionHistory, potSize, SEARCH_DEPTH);

    AbstractAction bestAction = selectBestAction(actionValues, blueprintAction);

    return convertToActionInstance(bestAction, player, currentRoundBetAmount, potSize);
  }

  /**
   * Perform limited-depth search with action pruning and time constraints REAL-TIME SEARCH
   * IMPLEMENTATION: Core Pluribus real-time search engine
   */
  private Map<AbstractAction, Double> performLimitedSearch(List<Card> holeCards,
      List<Card> communityCards,
      BettingRound currentRound, Player player, int currentRoundBetAmount,
      List<ActionTrace> actionHistory, int potSize, int depth) {

    long searchStartTime = System.currentTimeMillis();
    Map<AbstractAction, Double> actionValues = new ConcurrentHashMap<>();

    try {
      log.debug("🔍 Starting real-time search: depth={}, player={}, timeout={}ms",
          depth, player.getName(), SEARCH_TIMEOUT_MS);

      // Base case: Use blueprint strategy at leaf nodes or when depth exhausted
      if (depth <= 0) {
        return getLeafNodeValues(holeCards, communityCards, currentRound, player,
            currentRoundBetAmount, actionHistory, potSize);
      }

      // Create search game state snapshot for isolated simulation
      List<Player> players = createPlayerList(player); // Simplified for search
      SearchGameState searchState = SearchGameState.createSnapshot(
          communityCards, currentRound, potSize, currentRoundBetAmount,
          actionHistory, players, player.getPlayerIndex(), depth);

      if (!searchState.isValid()) {
        log.warn("Invalid search state created, falling back to blueprint strategy");
        return getLeafNodeValues(holeCards, communityCards, currentRound, player,
            currentRoundBetAmount, actionHistory, potSize);
      }

      // Get legal actions and prune to most promising ones
      List<AbstractAction> candidateActions = getPrunedActions(holeCards, communityCards,
          currentRound,
          player, currentRoundBetAmount, actionHistory, potSize);

      log.debug("🎯 Evaluating {} candidate actions at depth {}", candidateActions.size(), depth);

      // Evaluate each candidate action with time constraint checking
      for (AbstractAction action : candidateActions) {
        // Check timeout before each action evaluation
        long elapsedTime = System.currentTimeMillis() - searchStartTime;
        if (elapsedTime > SEARCH_TIMEOUT_MS) {
          log.debug("⏰ Search timeout reached after {}ms, returning partial results", elapsedTime);
          break;
        }

        try {
          double actionValue = simulateActionInSearch(action, searchState, holeCards,
              communityCards, currentRound, player,
              currentRoundBetAmount, actionHistory,
              potSize, depth - 1, searchStartTime);
          actionValues.put(action, actionValue);

          log.debug("📊 Action {} evaluated: value={:.4f}", action, actionValue);
        } catch (Exception e) {
          log.warn("Exception evaluating action {}: {}, skipping", action, e.getMessage());
          // Continue with other actions
        }
      }

      long totalTime = System.currentTimeMillis() - searchStartTime;
      log.debug("✅ Search completed: {}ms, {} actions evaluated", totalTime, actionValues.size());

      return actionValues;

    } catch (Exception e) {
      log.error("Exception in performLimitedSearch: {}, falling back to blueprint", e.getMessage());
      // Fallback to blueprint strategy on any exception
      return getLeafNodeValues(holeCards, communityCards, currentRound, player,
          currentRoundBetAmount, actionHistory, potSize);
    }
  }

  /**
   * Prune actions to most promising subset (action abstraction)
   */
  private List<AbstractAction> getPrunedActions(List<Card> holeCards, List<Card> communityCards,
      BettingRound currentRound, Player player, int currentRoundBetAmount,
      List<ActionTrace> actionHistory, int potSize) {

    List<AbstractAction> allActions = Arrays.asList(AbstractAction.values());
    List<AbstractAction> legalActions = new ArrayList<>();

    // Filter to legal actions
    for (AbstractAction action : allActions) {
      if (isActionLegal(action, player, currentRoundBetAmount, potSize)) {
        legalActions.add(action);
      }
    }

    // Prune to most promising actions (simple heuristic)
    if (legalActions.size() <= MAX_ACTIONS_TO_CONSIDER) {
      return legalActions;
    }

    // Keep fold, call, and best bet sizes
    List<AbstractAction> prunedActions = new ArrayList<>();
    prunedActions.add(AbstractAction.FOLD);
    prunedActions.add(AbstractAction.CHECK_OR_CALL);

    // Add most promising bet sizes
    if (legalActions.contains(AbstractAction.BET_OR_RAISE_60)) {
      prunedActions.add(AbstractAction.BET_OR_RAISE_60);
    }
    if (legalActions.contains(AbstractAction.BET_OR_RAISE_100)) {
      prunedActions.add(AbstractAction.BET_OR_RAISE_100);
    }

    return prunedActions;
  }

  /**
   * Create simplified player list for search operations
   */
  private List<Player> createPlayerList(Player mainPlayer) {
    List<Player> players = new ArrayList<>();
    players.add(mainPlayer);
    // Add simplified opponent players for search
    for (int i = 0; i < 2; i++) { // Assume 3-player game
      if (i != mainPlayer.getPlayerIndex()) {
        Player opponent = new Player("Opponent" + i, i, 10000);
        players.add(opponent);
      }
    }
    return players;
  }

  /**
   * Simulate taking an action in search context with time constraints
   */
  private double simulateActionInSearch(AbstractAction action, SearchGameState searchState,
      List<Card> holeCards, List<Card> communityCards,
      BettingRound currentRound, Player player, int currentRoundBetAmount,
      List<ActionTrace> actionHistory, int potSize, int remainingDepth,
      long searchStartTime) {

    // Check timeout before recursive search
    long elapsedTime = System.currentTimeMillis() - searchStartTime;
    if (elapsedTime > SEARCH_TIMEOUT_MS) {
      log.debug("⏰ Timeout in simulateActionInSearch, using blueprint evaluation");
      return evaluatePositionWithBlueprint(holeCards, communityCards, currentRound, player,
          currentRoundBetAmount, actionHistory, potSize);
    }

    try {
      // Calculate bet amount for this action
      int betAmount = calculateBetAmount(action, potSize, currentRoundBetAmount, player);

      // Apply action to search state
      SearchGameState newSearchState = searchState.withActionApplied(player.getPlayerIndex(),
          action, betAmount);

      if (newSearchState.isTerminal()) {
        // Terminal position - evaluate directly
        return evaluateTerminalPosition(newSearchState, player.getPlayerIndex(), holeCards,
            communityCards);
      }

      // Create new action history
      List<ActionTrace> newHistory = new ArrayList<>(actionHistory);
      newHistory.add(new ActionTrace(player.getPlayerIndex(), action, currentRound.ordinal(),
          actionHistory.size(), player.getPlayerIndex()));

      // Recursively evaluate resulting position with reduced depth
      Map<AbstractAction, Double> nextValues = performLimitedSearch(holeCards, communityCards,
          currentRound, player, currentRoundBetAmount,
          newHistory, potSize, remainingDepth);

      // Return weighted average of possible outcomes
      return nextValues.values().stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

    } catch (Exception e) {
      log.warn("Exception in simulateActionInSearch: {}, using blueprint fallback", e.getMessage());
      return evaluatePositionWithBlueprint(holeCards, communityCards, currentRound, player,
          currentRoundBetAmount, actionHistory, potSize);
    }
  }

  /**
   * Get leaf node values using proper expected utility calculation CRITICAL FIX: Returns expected
   * utilities instead of strategy probabilities
   */
  private Map<AbstractAction, Double> getLeafNodeValues(List<Card> holeCards,
      List<Card> communityCards,
      BettingRound currentRound, Player player, int currentRoundBetAmount,
      List<ActionTrace> actionHistory, int potSize) {

    Map<AbstractAction, Double> values = new HashMap<>();

    try {
      // Calculate hand strength using HandEvaluator
      double handStrength = calculateHandStrength(holeCards, communityCards);

      // Get legal actions for evaluation
      List<AbstractAction> legalActions = getPrunedActions(holeCards, communityCards, currentRound,
          player, currentRoundBetAmount, actionHistory, potSize);

      log.debug("🎯 Evaluating leaf node: handStrength={:.3f}, potSize={}, legalActions={}",
          handStrength, potSize, legalActions.size());

      // Calculate expected utility for each legal action
      for (AbstractAction action : legalActions) {
        double expectedUtility = calculateExpectedUtility(action, handStrength, potSize,
            currentRoundBetAmount, player);
        values.put(action, expectedUtility);

        log.debug("📊 Action {} utility: {:.4f}", action, expectedUtility);
      }

      // Blend with blueprint strategy for robustness
      blendWithBlueprintStrategy(values, holeCards, communityCards, currentRound, player,
          currentRoundBetAmount, actionHistory);

      return values;

    } catch (Exception e) {
      log.warn("Exception in getLeafNodeValues: {}, using blueprint fallback", e.getMessage());
      return getBlueprintStrategyAsValues(holeCards, communityCards, currentRound, player,
          currentRoundBetAmount, actionHistory);
    }
  }

  /**
   * Select best action combining search and blueprint with intelligent fallbacks CRITICAL FIX:
   * Proper value-based action selection instead of always returning CHECK_OR_CALL
   */
  private AbstractAction selectBestAction(Map<AbstractAction, Double> searchValues,
      ActionInstance blueprintAction) {
    try {
      log.debug("🎯 Selecting best action: searchValues={}, blueprintAction={}",
          searchValues.size(), blueprintAction.getAction());

      // Use search results if available and reliable
      if (!searchValues.isEmpty()) {
        // Find action with highest expected value
        AbstractAction bestSearchAction = searchValues.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse(null);

        if (bestSearchAction != null) {
          double bestValue = searchValues.get(bestSearchAction);
          log.debug("📊 Best search action: {} with value {:.4f}", bestSearchAction, bestValue);

          // Validate action is reasonable (not negative utility unless folding)
          if (bestValue >= 0.0 || bestSearchAction == AbstractAction.FOLD) {
            return bestSearchAction;
          } else {
            log.debug("⚠️ Best search action has negative utility, considering blueprint");
          }
        }
      }

      // Fallback to blueprint action if search failed or unreliable
      AbstractAction blueprintAbstractAction = convertToAbstractAction(blueprintAction);
      log.debug("🔄 Using blueprint fallback: {}", blueprintAbstractAction);
      return blueprintAbstractAction;

    } catch (Exception e) {
      log.warn("Exception in selectBestAction: {}, using safe fallback", e.getMessage());
      // Safe fallback - prefer call over fold, fold over risky bets
      return AbstractAction.CHECK_OR_CALL;
    }
  }

  /**
   * Convert ActionInstance back to AbstractAction for consistency
   */
  private AbstractAction convertToAbstractAction(ActionInstance actionInstance) {
    if (actionInstance == null) {
      return AbstractAction.CHECK_OR_CALL;
    }

    switch (actionInstance.getAction()) {
      case FOLD:
        return AbstractAction.FOLD;
      case CHECK:
      case CALL:
        return AbstractAction.CHECK_OR_CALL;
      case BET:
      case RAISE:
        // Determine bet size category based on amount
        int amount = actionInstance.getAmount();
        if (amount <= 0) {
          return AbstractAction.CHECK_OR_CALL;
        }

        // Simple categorization - could be enhanced
        return AbstractAction.BET_OR_RAISE_60; // Default bet size
      default:
        return AbstractAction.CHECK_OR_CALL;
    }
  }

  /**
   * Calculate hand strength using HandEvaluator
   */
  private double calculateHandStrength(List<Card> holeCards, List<Card> communityCards) {
    try {
      if (holeCards == null || holeCards.size() < 2) {
        return 0.5; // Neutral strength if no hole cards
      }

      List<Card> allCards = new ArrayList<>(holeCards);
      if (communityCards != null) {
        allCards.addAll(communityCards);
      }

      if (allCards.size() < 2) {
        return 0.5; // Neutral strength if insufficient cards
      }

      // Use HandEvaluator to get hand strength (0.0 to 1.0)
      int handValue = HandEvaluator.evaluateBestOf(allCards);

      // Normalize hand value to 0.0-1.0 range
      // HandEvaluator returns values where higher = better
      // Approximate normalization based on poker hand rankings
      double normalizedStrength = Math.min(1.0, Math.max(0.0, handValue / 10000000.0));

      log.debug("🃏 Hand strength: cards={}, value={}, normalized={:.3f}",
          allCards.size(), handValue, normalizedStrength);

      return normalizedStrength;

    } catch (Exception e) {
      log.warn("Exception calculating hand strength: {}, using neutral value", e.getMessage());
      return 0.5; // Neutral strength on error
    }
  }

  /**
   * Calculate expected utility for an action Formula: handStrength * potOdds * potSize - (1 -
   * handStrength) * betAmount
   */
  private double calculateExpectedUtility(AbstractAction action, double handStrength, int potSize,
      int currentRoundBetAmount, Player player) {
    try {
      switch (action) {
        case FOLD:
          // Folding loses current investment but no additional cost
          return -player.getCurrentBet();

        case CHECK_OR_CALL:
          int callAmount = Math.max(0, currentRoundBetAmount - player.getCurrentBet());
          if (callAmount == 0) {
            // Free check - only win/lose based on hand strength
            return handStrength * potSize - (1 - handStrength) * player.getCurrentBet();
          } else {
            // Calling - pot odds calculation
            double potOdds = (double) potSize / (potSize + callAmount);
            return handStrength * potOdds * (potSize + callAmount)
                - (1 - handStrength) * callAmount;
          }

        case BET_OR_RAISE_30:
        case BET_OR_RAISE_60:
        case BET_OR_RAISE_100:
        case BET_OR_RAISE_200:
        case BET_OR_RAISE_500:
          int betAmount = calculateBetAmount(action, potSize, currentRoundBetAmount, player);
          // Betting/raising - higher risk, higher reward
          double winProbability = Math.min(0.95, handStrength + 0.1); // Slight boost for aggression

          if (player.getChips() <= potSize * getAllInThreshold(action)) {
            // CRITICAL FIX: All-in utility calculation
            int allInAmount = player.getChips();
            // All-in is high risk, high reward - only do with strong hands or desperation
            double allInWinProbability = Math.min(0.98,
                handStrength + 0.2); // Higher boost for all-in aggression
            return allInWinProbability * (potSize + allInAmount)
                - (1 - allInWinProbability) * allInAmount;
          } else {
            return winProbability * (potSize + betAmount) - (1 - winProbability) * betAmount;
          }

        default:
          return 0.0;
      }
    } catch (Exception e) {
      log.warn("Exception calculating expected utility for {}: {}", action, e.getMessage());
      return 0.0;
    }
  }

  private float getAllInThreshold(AbstractAction action) {
    if (action == AbstractAction.BET_OR_RAISE_30) {
      return 0.45f;
    } else if (action == AbstractAction.BET_OR_RAISE_60) {
      return 0.8f;
    } else if (action == AbstractAction.BET_OR_RAISE_100) {
      return 1.5f;
    } else if (action == AbstractAction.BET_OR_RAISE_200) {
      return 3.5f;
    } else if (action == AbstractAction.BET_OR_RAISE_500) {
      return 7.0f;
    } else {
      return 0.0f;
    }
  }

  /**
   * Calculate bet amount for a given action
   */
  private int calculateBetAmount(AbstractAction action, int potSize, int currentRoundBetAmount,
      Player player) {
    switch (action) {
      case FOLD:
      case CHECK_OR_CALL:
        if (currentRoundBetAmount == 0) {
          return 0;
        } else {
          return Math.min(player.getChips(), currentRoundBetAmount - player.getCurrentBet());
        }
      case BET_OR_RAISE_30:
      case BET_OR_RAISE_60:
      case BET_OR_RAISE_100:
      case BET_OR_RAISE_200:
      case BET_OR_RAISE_500:
        if (player.getChips() <= potSize * getAllInThreshold(action)) {
          return player.getChips();
        } else {
          return (int) (potSize * getAllInMultiplier(action));
        }
      default:
        return 0;
    }
  }

  private float getAllInMultiplier(AbstractAction action) {
    if (action == AbstractAction.BET_OR_RAISE_30) {
      return 0.3f;
    } else if (action == AbstractAction.BET_OR_RAISE_60) {
      return 0.6f;
    } else if (action == AbstractAction.BET_OR_RAISE_100) {
      return 1.0f;
    } else if (action == AbstractAction.BET_OR_RAISE_200) {
      return 2.0f;
    } else if (action == AbstractAction.BET_OR_RAISE_500) {
      return 5.0f;
    } else {
      return 0.0f;
    }
  }

  /**
   * Evaluate position using blueprint strategy as fallback
   */
  private double evaluatePositionWithBlueprint(List<Card> holeCards, List<Card> communityCards,
      BettingRound currentRound, Player player, int currentRoundBetAmount,
      List<ActionTrace> actionHistory, int potSize) {

    swapLock.readLock().lock();
    try {
      // FIXED: Get current blueprint store from AtomicReference
      InfosetStore currentStore = blueprintStore.get();

      // Get blueprint strategy
      Infoset key = Infoset.of(holeCards, communityCards, currentRound, player,
          currentRoundBetAmount, actionHistory);

      // Try cached strategy first for optimal performance
      int abstractionKey = InfosetStoreKey.abstractInfoset(key);
      float[] cachedStrategy = currentStore.getCachedStrategy(player.getPlayerIndex(),
          abstractionKey);

      float[] strategy;
      if (cachedStrategy != null) {
        strategy = cachedStrategy;
      } else {
        // FIXED: Use correct getOrCreate method on InfosetStore
        InfosetValue infosetValue = currentStore.getOrCreate(player.getPlayerIndex(),
            abstractionKey);
        strategy = infosetValue.getAverageStrategy();
      }

      // Convert strategy to expected value (simplified)
      double expectedValue = 0.0;
      for (int i = 0; i < strategy.length && i < AbstractAction.values().length; i++) {
        AbstractAction action = AbstractAction.values()[i];
        double actionUtility = calculateExpectedUtility(action, 0.5, potSize, currentRoundBetAmount,
            player);
        expectedValue += strategy[i] * actionUtility;
      }

      return expectedValue;

    } catch (Exception e) {
      log.warn("Exception in evaluatePositionWithBlueprint: {}", e.getMessage());
      return 0.0;
    } finally {
      swapLock.readLock().unlock();
    }
  }

  /**
   * Evaluate terminal position (game over)
   */
  private double evaluateTerminalPosition(SearchGameState searchState, int playerId,
      List<Card> holeCards, List<Card> communityCards) {
    try {
      SearchGameState.PlayerSnapshot player = searchState.getPlayer(playerId);
      if (player == null) {
        return 0.0;
      }

      if (player.isFolded()) {
        // Player folded - lose current investment
        return -player.getCurrentBet();
      }

      // Calculate hand strength for showdown
      double handStrength = calculateHandStrength(holeCards, communityCards);

      // Simplified terminal evaluation - in real implementation would compare all hands
      if (handStrength > 0.7) {
        return searchState.getPotSize() * 0.8; // Win most of pot
      } else if (handStrength > 0.3) {
        return searchState.getPotSize() * 0.1; // Win small portion
      } else {
        return -player.getCurrentBet(); // Lose investment
      }

    } catch (Exception e) {
      log.warn("Exception in evaluateTerminalPosition: {}", e.getMessage());
      return 0.0;
    }
  }

  /**
   * Blend search results with blueprint strategy for robustness
   */
  private void blendWithBlueprintStrategy(Map<AbstractAction, Double> searchValues,
      List<Card> holeCards,
      List<Card> communityCards, BettingRound currentRound, Player player,
      int currentRoundBetAmount, List<ActionTrace> actionHistory) {
    try {
      Map<AbstractAction, Double> blueprintValues = getBlueprintStrategyAsValues(holeCards,
          communityCards,
          currentRound, player,
          currentRoundBetAmount, actionHistory);

      // Blend search and blueprint values
      for (Map.Entry<AbstractAction, Double> entry : blueprintValues.entrySet()) {
        AbstractAction action = entry.getKey();
        double blueprintValue = entry.getValue();

        if (searchValues.containsKey(action)) {
          double searchValue = searchValues.get(action);
          double blendedValue =
              (1 - BLUEPRINT_BLEND_FACTOR) * searchValue + BLUEPRINT_BLEND_FACTOR * blueprintValue;
          searchValues.put(action, blendedValue);
        } else {
          searchValues.put(action, blueprintValue * BLUEPRINT_BLEND_FACTOR);
        }
      }

    } catch (Exception e) {
      log.warn("Exception blending with blueprint strategy: {}", e.getMessage());
    }
  }

  /**
   * Get blueprint strategy as utility values
   */
  private Map<AbstractAction, Double> getBlueprintStrategyAsValues(List<Card> holeCards,
      List<Card> communityCards,
      BettingRound currentRound, Player player,
      int currentRoundBetAmount, List<ActionTrace> actionHistory) {
    Map<AbstractAction, Double> values = new HashMap<>();

    swapLock.readLock().lock();
    try {
      // FIXED: Get current blueprint store from AtomicReference
      InfosetStore currentStore = blueprintStore.get();

      Infoset key = Infoset.of(holeCards, communityCards, currentRound, player,
          currentRoundBetAmount, actionHistory);

      // Try cached strategy first for optimal performance
      int abstractionKey = InfosetStoreKey.abstractInfoset(key);
      float[] cachedStrategy = currentStore.getCachedStrategy(player.getPlayerIndex(),
          abstractionKey);

      float[] strategy;
      if (cachedStrategy != null) {
        strategy = cachedStrategy;
      } else {
        // FIXED: Use correct getOrCreate method on InfosetStore
        InfosetValue infosetValue = currentStore.getOrCreate(player.getPlayerIndex(),
            abstractionKey);
        strategy = infosetValue.getAverageStrategy();
      }

      // Convert strategy probabilities to utility estimates
      for (int i = 0; i < strategy.length && i < AbstractAction.values().length; i++) {
        AbstractAction action = AbstractAction.values()[i];
        // Use strategy probability as utility weight (simplified)
        double utility = strategy[i] * 100.0; // Scale for visibility
        values.put(action, utility);
      }

    } catch (Exception e) {
      log.warn("Exception getting blueprint strategy as values: {}", e.getMessage());
    } finally {
      swapLock.readLock().unlock();
    }

    return values;
  }

  // Helper methods
  private AbstractAction sampleAction(float[] strategy) {
    double cumulative = 0.0;
    double random = this.random.nextDouble();

    for (int i = 0; i < strategy.length; i++) {
      cumulative += strategy[i];
      if (random < cumulative) {
        return AbstractAction.values()[i];
      }
    }
    return AbstractAction.values()[strategy.length - 1];
  }

  private float[] filterIllegalActions(Player player, int currentRoundBetAmount, int potSize,
      float[] strategy) {
    float[] filtered = new float[strategy.length];

    for (int i = 0; i < strategy.length; i++) {
      if (isActionLegal(AbstractAction.values()[i], player, currentRoundBetAmount, potSize)) {
        filtered[i] = strategy[i];
      }
    }

    // Normalize
    float sum = 0f;
    for (float prob : filtered) {
      sum += prob;
    }
    if (sum > 1e-6f) {
      for (int i = 0; i < filtered.length; i++) {
        filtered[i] /= sum;
      }
    } else {
      Arrays.fill(filtered, 1f / filtered.length);
    }

    return filtered;
  }

  private boolean isActionLegal(AbstractAction action, Player player, int currentRoundBetAmount,
      int potSize) {
    switch (action) {
      case FOLD:
        return true;
      case CHECK_OR_CALL:
        return true;
      case BET_OR_RAISE_30:
      case BET_OR_RAISE_60:
      case BET_OR_RAISE_100:
      case BET_OR_RAISE_200:
      case BET_OR_RAISE_500:
        return player.getChips() > currentRoundBetAmount - player.getCurrentBet();
      default:
        return false;
    }
  }

  private ActionInstance convertToActionInstance(AbstractAction abstractAction, Player player,
      int currentRoundBetAmount, int potSize) {
    // Implementation similar to BlueprintStrategy.convertAbstractToRealAction
    switch (abstractAction) {
      case FOLD:
        return new ActionInstance(Action.FOLD, 0);
      case CHECK_OR_CALL:
        if (player.getCurrentBet() < currentRoundBetAmount) {
          return new ActionInstance(Action.CALL,
              Math.min(player.getChips(), currentRoundBetAmount - player.getCurrentBet()));
        } else {
          return new ActionInstance(Action.CHECK, 0);
        }
      case BET_OR_RAISE_30:
      case BET_OR_RAISE_60:
      case BET_OR_RAISE_100:
      case BET_OR_RAISE_200:
      case BET_OR_RAISE_500:
        if (player.getChips()
            <= potSize * getAllInThreshold(abstractAction)) {
          return new ActionInstance((currentRoundBetAmount == 0) ? Action.BET : Action.RAISE,
              player.getChips());
        } else {
          return new ActionInstance((currentRoundBetAmount == 0) ? Action.BET : Action.RAISE,
              Math.min(player.getChips(),
                  (int) (currentRoundBetAmount + potSize * getAllInMultiplier(
                      abstractAction))));
        }
      default:
        throw new IllegalArgumentException("Invalid abstract action: " + abstractAction);
    }
  }
}
