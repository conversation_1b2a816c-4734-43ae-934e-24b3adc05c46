package com.example.texasholdem.strategy;

import com.example.texasholdem.strategy.model.ActionTrace;
import com.example.texasholdem.model.BettingRound;
import com.example.texasholdem.model.Card;
import com.example.texasholdem.model.Player;
import com.example.texasholdem.strategy.model.AbstractAction;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Lightweight game state snapshot for real-time search operations. Provides isolated game state
 * simulation without affecting actual GameService state.
 * <p>
 * DESIGN PRINCIPLES: - Immutable snapshots for thread safety - Minimal memory footprint for
 * performance - Fast cloning for search tree exploration - Integration with existing Player and
 * Card models
 */
@Data
@Slf4j
public class SearchGameState {

  // Core game state
  private final List<Card> communityCards;
  private final BettingRound currentRound;
  private final int potSize;
  private final int currentRoundBetAmount;
  private final List<ActionTrace> actionHistory;

  // Player state snapshots
  private final Map<Integer, PlayerSnapshot> playerStates;
  private final int currentPlayerIndex;
  private final int numActivePlayers;

  // Search metadata
  private final long creationTime;
  private final int searchDepth;

  /**
   * Lightweight player state snapshot for search operations
   */
  @Data
  public static class PlayerSnapshot {

    private final int playerIndex;
    private final String name;
    private final List<Card> holeCards;
    private final int chips;
    private final int currentBet;
    private final boolean folded;
    private final boolean allIn;
    private final boolean active;

    public PlayerSnapshot(Player player) {
      this.playerIndex = player.getPlayerIndex();
      this.name = player.getName();
      this.holeCards = new ArrayList<>(player.getHand());
      this.chips = player.getChips();
      this.currentBet = player.getCurrentBet();
      this.folded = player.isFolded();
      this.allIn = player.isAllIn();
      this.active = player.isActive();
    }

    /**
     * Create a modified copy with updated state after action simulation
     */
    public PlayerSnapshot withActionApplied(int chipChange, boolean newFolded, boolean newAllIn) {
      return new PlayerSnapshot(
          this.playerIndex,
          this.name,
          new ArrayList<>(this.holeCards),
          this.chips + chipChange,
          this.currentBet + Math.max(0, chipChange),
          newFolded,
          newAllIn,
          !newFolded && !newAllIn
      );
    }

    private PlayerSnapshot(int playerIndex, String name, List<Card> holeCards,
        int chips, int currentBet, boolean folded, boolean allIn, boolean active) {
      this.playerIndex = playerIndex;
      this.name = name;
      this.holeCards = holeCards;
      this.chips = chips;
      this.currentBet = currentBet;
      this.folded = folded;
      this.allIn = allIn;
      this.active = active;
    }
  }

  /**
   * Create initial search game state from current game state
   */
  public static SearchGameState createSnapshot(List<Card> communityCards, BettingRound currentRound,
      int potSize, int currentRoundBetAmount,
      List<ActionTrace> actionHistory, List<Player> players,
      int currentPlayerIndex, int searchDepth) {

    Map<Integer, PlayerSnapshot> playerSnapshots = new HashMap<>();
    int activePlayers = 0;

    for (Player player : players) {
      playerSnapshots.put(player.getPlayerIndex(), new PlayerSnapshot(player));
      if (player.isActive()) {
        activePlayers++;
      }
    }

    return new SearchGameState(
        new ArrayList<>(communityCards),
        currentRound,
        potSize,
        currentRoundBetAmount,
        new ArrayList<>(actionHistory),
        playerSnapshots,
        currentPlayerIndex,
        activePlayers,
        System.currentTimeMillis(),
        searchDepth
    );
  }

  /**
   * Create a new search state with an action applied
   */
  public SearchGameState withActionApplied(int playerId, AbstractAction action, int betAmount) {
    Map<Integer, PlayerSnapshot> newPlayerStates = new HashMap<>(this.playerStates);
    PlayerSnapshot currentPlayer = newPlayerStates.get(playerId);

    if (currentPlayer == null) {
      log.warn("Player {} not found in search state, returning unchanged state", playerId);
      return this;
    }

    // Calculate action effects
    int chipChange = 0;
    boolean newFolded = currentPlayer.folded;
    boolean newAllIn = currentPlayer.allIn;
    int newPotSize = this.potSize;
    int newCurrentRoundBetAmount = this.currentRoundBetAmount;

    switch (action) {
      case FOLD:
        newFolded = true;
        break;
      case CHECK_OR_CALL:
        chipChange = -Math.max(0, this.currentRoundBetAmount - currentPlayer.currentBet);
        newPotSize += Math.abs(chipChange);

        if (currentPlayer.chips + chipChange < 0) {
          chipChange = -currentPlayer.chips; // Player all in call by all remaining chips
          newPotSize += currentPlayer.chips;
          newCurrentRoundBetAmount = Math.max(newCurrentRoundBetAmount,
              currentPlayer.currentBet + currentPlayer.chips);
          newAllIn = true; // Player is definitely all-in
        }
        break;
      case BET_OR_RAISE_30:
      case BET_OR_RAISE_60:
      case BET_OR_RAISE_100:
      case BET_OR_RAISE_200:
      case BET_OR_RAISE_500:
        chipChange = -betAmount;
        newPotSize += betAmount;
        newCurrentRoundBetAmount = Math.max(newCurrentRoundBetAmount,
            currentPlayer.currentBet + betAmount);

        if (currentPlayer.chips <= getAllInThreshold(action) * potSize) {
          chipChange = -currentPlayer.chips; // Player bets all remaining chips
          newPotSize += currentPlayer.chips;
          newCurrentRoundBetAmount = Math.max(newCurrentRoundBetAmount,
              currentPlayer.currentBet + currentPlayer.chips);
          newAllIn = true; // Player is definitely all-in
        }
        break;
    }

    // Update player state
    newPlayerStates.put(playerId, currentPlayer.withActionApplied(chipChange, newFolded, newAllIn));

    // Create new action history
    List<ActionTrace> newActionHistory = new ArrayList<>(this.actionHistory);
    newActionHistory.add(new ActionTrace(playerId, action, this.currentRound.ordinal(),
        this.actionHistory.size(), playerId));

    // Calculate next player
    int nextPlayer = getNextActivePlayer(playerId, newPlayerStates);

    return new SearchGameState(
        new ArrayList<>(this.communityCards),
        this.currentRound,
        newPotSize,
        newCurrentRoundBetAmount,
        newActionHistory,
        newPlayerStates,
        nextPlayer,
        countActivePlayers(newPlayerStates),
        this.creationTime,
        this.searchDepth
    );
  }

  private float getAllInThreshold(AbstractAction action) {
    if (action == AbstractAction.BET_OR_RAISE_30) {
      return 0.45f;
    } else if (action == AbstractAction.BET_OR_RAISE_60) {
      return 0.8f;
    } else if (action == AbstractAction.BET_OR_RAISE_100) {
      return 1.5f;
    } else if (action == AbstractAction.BET_OR_RAISE_200) {
      return 3.5f;
    } else if (action == AbstractAction.BET_OR_RAISE_500) {
      return 7.0f;
    } else {
      return 0.0f;
    }
  }

  /**
   * Get next active player after current player
   */
  private int getNextActivePlayer(int currentPlayer, Map<Integer, PlayerSnapshot> playerStates) {
    int numPlayers = playerStates.size();
    for (int i = 1; i <= numPlayers; i++) {
      int nextIndex = (currentPlayer + i) % numPlayers;
      PlayerSnapshot player = playerStates.get(nextIndex);
      if (player != null && player.active && !player.folded && !player.allIn) {
        return nextIndex;
      }
    }
    return currentPlayer; // Fallback if no active players found
  }

  /**
   * Count active players in the game state
   */
  private int countActivePlayers(Map<Integer, PlayerSnapshot> playerStates) {
    return (int) playerStates.values().stream()
        .filter(p -> p.active && !p.folded)
        .count();
  }

  /**
   * Check if this game state is terminal (game over)
   */
  public boolean isTerminal() {
    return numActivePlayers <= 1 ||
        playerStates.values().stream().allMatch(p -> p.folded || p.allIn);
  }

  /**
   * Get player snapshot by index
   */
  public PlayerSnapshot getPlayer(int playerIndex) {
    return playerStates.get(playerIndex);
  }

  /**
   * Get current acting player snapshot
   */
  public PlayerSnapshot getCurrentPlayer() {
    return playerStates.get(currentPlayerIndex);
  }

  /**
   * Check if search state is valid and consistent
   */
  public boolean isValid() {
    return communityCards != null &&
        currentRound != null &&
        playerStates != null &&
        !playerStates.isEmpty() &&
        potSize >= 0 &&
        currentRoundBetAmount >= 0 &&
        numActivePlayers >= 0;
  }

  /**
   * Get age of this search state in milliseconds
   */
  public long getAgeMs() {
    return System.currentTimeMillis() - creationTime;
  }
}
