package com.example.texasholdem.abstraction;

import com.example.texasholdem.strategy.model.AbstractAction;
import com.example.texasholdem.strategy.model.ActionTrace;
import com.example.texasholdem.model.BettingRound;
import com.example.texasholdem.model.Card;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Container class for abstraction system data structures
 * <p>
 * Contains all supporting data structures as static inner classes to avoid multiple public class
 * compilation errors while keeping related classes together.
 */
public class AbstractionDataStructures {

  /**
   * Game context for abstraction feature extraction
   */
  @Data
  @Builder
  public static class GameContext {

    private List<Card> holeCards;
    private List<Card> communityCards;
    private List<ActionTrace> actionHistory;
    private Integer position;
    private BettingRound bettingRound;
    private Integer numActivePlayers;
    private Integer potSize;
    private Integer currentBet;
    private Integer stackSize;
  }

  /**
   * Hand strength features for card abstraction
   */
  @Data
  @Builder
  public static class HandStrengthFeatures {

    private Double strength;        // Raw hand strength [0.0, 1.0]
    private Double potential;       // Improvement potential [0.0, 1.0]
    private HandCategory category;  // Hand category
    private Integer rank;           // Relative rank among all possible hands
    private Double equity;          // Equity against random hands
  }

  /**
   * Betting history features for action abstraction
   */
  @Data
  @Builder
  public static class BettingHistoryFeatures {

    private BettingPattern pattern;     // Overall betting pattern
    private Double aggressionLevel;     // Aggression level [0.0, 1.0]
    private Double potOdds;            // Current pot odds
    private Integer numBets;           // Number of bets in current round
    private Integer numRaises;         // Number of raises in current round
    private Double averageBetSize;     // Average bet size as fraction of pot
  }

  /**
   * Abstraction quality metrics
   */
  @Data
  public static class AbstractionQualityMetrics {

    private final AtomicLong sampleCount = new AtomicLong(0);
    private volatile double totalStrategicValue = 0.0;
    private volatile double totalCompressionRatio = 0.0;
    private volatile double minStrategicValue = Double.MAX_VALUE;
    private volatile double maxStrategicValue = Double.MIN_VALUE;
    private volatile double minCompressionRatio = Double.MAX_VALUE;
    private volatile double maxCompressionRatio = Double.MIN_VALUE;

    public synchronized void updateMetrics(double strategicValue, double compressionRatio) {
      long count = sampleCount.incrementAndGet();

      // Update totals
      totalStrategicValue += strategicValue;
      totalCompressionRatio += compressionRatio;

      // Update min/max
      minStrategicValue = Math.min(minStrategicValue, strategicValue);
      maxStrategicValue = Math.max(maxStrategicValue, strategicValue);
      minCompressionRatio = Math.min(minCompressionRatio, compressionRatio);
      maxCompressionRatio = Math.max(maxCompressionRatio, compressionRatio);
    }

    public double getAverageStrategicValue() {
      long count = sampleCount.get();
      return count > 0 ? totalStrategicValue / count : 0.0;
    }

    public double getAverageCompressionRatio() {
      long count = sampleCount.get();
      return count > 0 ? totalCompressionRatio / count : 0.0;
    }

    public long getSampleCount() {
      return sampleCount.get();
    }
  }

  /**
   * Abstraction statistics
   */
  @Data
  @Builder
  public static class AbstractionStatistics {

    private long totalAbstractions;
    private long totalInfosets;
    private double compressionRatio;
    private double averageAbstractionSize;
    private java.util.Map<String, Long> performanceCounters;

    public String getSummary() {
      return String.format(
          "Abstractions: %d, Infosets: %d, Compression: %.2fx, Avg Size: %.1f",
          totalAbstractions, totalInfosets, compressionRatio, averageAbstractionSize
      );
    }
  }

  /**
   * Validation result for abstraction quality
   */
  @Data
  @Builder
  public static class ValidationResult {

    private boolean valid;
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    @Builder.Default
    private List<String> warnings = new ArrayList<>();

    public void addError(String error) {
      errors.add(error);
    }

    public void addWarning(String warning) {
      warnings.add(warning);
    }

    public boolean hasErrors() {
      return !errors.isEmpty();
    }

    public boolean hasWarnings() {
      return !warnings.isEmpty();
    }

    public String getSummary() {
      StringBuilder sb = new StringBuilder();
      sb.append("Validation: ").append(valid ? "PASSED" : "FAILED");

      if (!errors.isEmpty()) {
        sb.append(", Errors: ").append(errors.size());
      }

      if (!warnings.isEmpty()) {
        sb.append(", Warnings: ").append(warnings.size());
      }

      return sb.toString();
    }
  }

  /**
   * Action cluster for action abstraction
   */
  @Data
  @Builder
  public static class ActionCluster {

    private int clusterId;
    private String clusterName;
    private List<ActionTemplate> actions;
    private Double representativeSize;  // Representative bet size for this cluster
    private ActionType actionType;      // Type of actions in this cluster
    private Double frequency;           // Frequency of use in training data

    @Builder.Default
    private List<Double> betSizes = new ArrayList<>();  // All bet sizes in this cluster

    public void addBetSize(double betSize) {
      betSizes.add(betSize);
      updateRepresentativeSize();
    }

    private void updateRepresentativeSize() {
      if (!betSizes.isEmpty()) {
        representativeSize = betSizes.stream()
            .mapToDouble(Double::doubleValue)
            .average()
            .orElse(0.0);
      }
    }

    public double getVariance() {
      if (betSizes.size() < 2) {
        return 0.0;
      }

      double mean = representativeSize != null ? representativeSize : 0.0;
      double variance = betSizes.stream()
          .mapToDouble(size -> Math.pow(size - mean, 2))
          .average()
          .orElse(0.0);

      return variance;
    }
  }

  /**
   * Action template for action abstraction
   */
  @Data
  @Builder
  public static class ActionTemplate {

    private ActionType action;
    private Double betSizeRatio;    // Bet size as ratio of pot (null for check/fold/call)
    private Double frequency;       // Frequency in training data
    private String description;     // Human-readable description

    // Additional fields for integration with existing AbstractAction system
    private String templateId;      // Unique identifier for this template
    private BettingRound bettingRound; // Betting round this template applies to

    @Builder.Default
    private java.util.List<com.example.texasholdem.strategy.model.AbstractAction> actions = new java.util.ArrayList<>();

    public boolean isCompatible(ActionTemplate other) {
      if (action != other.action) {
        return false;
      }

      if (betSizeRatio == null && other.betSizeRatio == null) {
        return true;
      }

      if (betSizeRatio == null || other.betSizeRatio == null) {
        return false;
      }

      // Consider compatible if bet sizes are within 20% of each other
      double ratio =
          Math.abs(betSizeRatio - other.betSizeRatio) / Math.max(betSizeRatio, other.betSizeRatio);
      return ratio <= 0.2;
    }

    /**
     * Check if this template contains a specific AbstractAction
     */
    public boolean containsAction(com.example.texasholdem.strategy.model.AbstractAction action) {
      return actions.contains(action);
    }

    /**
     * Add an AbstractAction to this template
     */
    public void addAction(com.example.texasholdem.strategy.model.AbstractAction action) {
      if (!actions.contains(action)) {
        actions.add(action);
      }
    }

    /**
     * Get the primary AbstractAction for this template
     */
    public com.example.texasholdem.strategy.model.AbstractAction getPrimaryAction() {
      return actions.isEmpty() ? null : actions.get(0);
    }
  }

  /**
   * Action type enumeration
   */
  public enum ActionType {
    FOLD, CHECK, CALL, BET, RAISE, ALL_IN
  }

  /**
   * Hand category enumeration for abstraction
   */
  public enum HandCategory {
    HIGH_CARD, PAIR, TWO_PAIR, THREE_OF_A_KIND, STRAIGHT,
    FLUSH, FULL_HOUSE, FOUR_OF_A_KIND, STRAIGHT_FLUSH, ROYAL_FLUSH
  }

  /**
   * Betting pattern enumeration for abstraction
   */
  public enum BettingPattern {
    PASSIVE, CONSERVATIVE, BALANCED, AGGRESSIVE, VERY_AGGRESSIVE
  }

  /**
   * Hand bucket for card abstraction
   */
  @Data
  @Builder
  public static class HandBucket {

    private int bucketId;
    private String bucketName;
    private Double minStrength;
    private Double maxStrength;
    private Double minPotential;
    private Double maxPotential;
    private List<HandExample> examples;
    private Integer sampleCount;

    @Builder.Default
    private List<HandExample> representatives = new ArrayList<>();

    public boolean contains(double strength, double potential) {
      boolean strengthMatch = (minStrength == null || strength >= minStrength) &&
          (maxStrength == null || strength <= maxStrength);
      boolean potentialMatch = (minPotential == null || potential >= minPotential) &&
          (maxPotential == null || potential <= maxPotential);

      return strengthMatch && potentialMatch;
    }

    public void addRepresentative(HandExample example) {
      representatives.add(example);
      if (representatives.size() > 10) {
        // Keep only the most representative examples
        representatives = representatives.subList(0, 10);
      }
    }
  }

  /**
   * Hand example for card abstraction
   */
  @Data
  @Builder
  public static class HandExample {

    private List<Card> holeCards;
    private List<Card> communityCards;
    private Double strength;
    private Double potential;
    private HandCategory category;
    private String description;
  }
}
