package com.example.texasholdem.abstraction;

import static com.example.texasholdem.model.Action.BET;
import static com.example.texasholdem.model.Action.CALL;
import static com.example.texasholdem.model.Action.CHECK;
import static com.example.texasholdem.model.Action.FOLD;
import static com.example.texasholdem.model.Action.RAISE;

import com.example.texasholdem.model.*;
import com.example.texasholdem.abstraction.AbstractionDataStructures.*;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Action Abstraction Engine for Pluribus Poker AI
 * <p>
 * Reduces action space complexity by clustering similar betting actions while preserving strategic
 * diversity. Integrates with existing ActionInstance and betting round handling in the game
 * engine.
 * <p>
 * Key features: - Bet sizing abstraction with strategic clustering - Preservation of critical
 * actions (check, fold, call) - Dynamic action space adaptation based on game context - Integration
 * with existing game engine components
 */
@Slf4j
public class ActionAbstractionEngine {

  private final AbstractionConfig config;
  private final Map<BettingRound, List<ActionCluster>> actionClusters;
  private final Map<String, ActionTemplate> actionTemplates;
  private final AtomicInteger nextClusterId;

  // Performance monitoring
  private final Map<String, Long> performanceCounters;

  // Action frequency tracking for refinement
  private final Map<ActionTemplate, Long> actionFrequencies;

  public ActionAbstractionEngine(AbstractionConfig config) {
    this.config = config != null ? config : AbstractionConfig.defaultConfig();
    this.actionClusters = new ConcurrentHashMap<>();
    this.actionTemplates = new ConcurrentHashMap<>();
    this.nextClusterId = new AtomicInteger(0);
    this.performanceCounters = new ConcurrentHashMap<>();
    this.actionFrequencies = new ConcurrentHashMap<>();

    initializeDefaultClusters();

    log.info("Action Abstraction Engine initialized with config: {}", config.getSummary());
  }

  /**
   * Initialize default action clusters for each betting round
   */
  private void initializeDefaultClusters() {
    for (BettingRound round : BettingRound.values()) {
      List<ActionCluster> clusters = createDefaultClustersForRound(round);
      actionClusters.put(round, clusters);

      log.debug("Initialized {} action clusters for {}", clusters.size(), round);
    }
  }

  /**
   * Create default action clusters for a betting round
   */
  private List<ActionCluster> createDefaultClustersForRound(BettingRound round) {
    List<ActionCluster> clusters = new ArrayList<>();

    // Always preserve basic actions
    clusters.add(createBasicActionCluster(ActionType.FOLD, "Fold"));
    clusters.add(createBasicActionCluster(ActionType.CHECK, "Check"));
    clusters.add(createBasicActionCluster(ActionType.CALL, "Call"));

    // Create bet sizing clusters based on pot ratios
    clusters.addAll(createBetSizingClusters(round));

    return clusters;
  }

  /**
   * Create basic action cluster (fold, check, call)
   */
  private ActionCluster createBasicActionCluster(ActionType actionType, String name) {
    ActionTemplate template = ActionTemplate.builder().actionType(actionType).betSizeRatio(null)
        .frequency(0.0).description(name).build();

    return ActionCluster.builder().clusterId(nextClusterId.getAndIncrement()).clusterName(name)
        .actions(Arrays.asList(template)).actionType(actionType).frequency(0.0).build();
  }

  /**
   * Create bet sizing clusters for strategic betting
   */
  private List<ActionCluster> createBetSizingClusters(BettingRound round) {
    List<ActionCluster> clusters = new ArrayList<>();

    // Define bet sizing ranges based on betting round
    double[] betSizes = getBetSizingRanges(round);

    for (int i = 0; i < betSizes.length; i++) {
      double betSize = betSizes[i];
      String clusterName = String.format("Bet %.1fx pot", betSize);

      ActionTemplate template = ActionTemplate.builder()
          .actionType(betSize >= 1.0 ? ActionType.RAISE : ActionType.BET).betSizeRatio(betSize)
          .frequency(0.0).description(clusterName).build();

      ActionCluster cluster = ActionCluster.builder().clusterId(nextClusterId.getAndIncrement())
          .clusterName(clusterName).actions(Arrays.asList(template)).representativeSize(betSize)
          .actionType(template.getActionType()).frequency(0.0).build();

      clusters.add(cluster);
    }

    return clusters;
  }

  /**
   * Get bet sizing ranges for a betting round
   */
  private double[] getBetSizingRanges(BettingRound round) {
    switch (round) {
      case PREFLOP:
        return new double[]{0.3, 0.5, 0.75, 1.0, 1.5, 2.0, 3.0}; // Conservative preflop sizing
      case FLOP:
        return new double[]{0.25, 0.5, 0.75, 1.0, 1.5, 2.5}; // Standard flop sizing
      case TURN:
        return new double[]{0.5, 0.75, 1.0, 1.5, 2.0, 3.0}; // Turn sizing with polarization
      case RIVER:
        return new double[]{0.5, 0.75, 1.0, 1.5, 2.5, 4.0}; // River sizing for value/bluff
      default:
        return new double[]{0.5, 1.0, 2.0}; // Default sizing
    }
  }

  /**
   * Abstract an action to its cluster representation
   */
  public ActionTemplate abstractAction(ActionInstance action, BettingRound round, int potSize) {
    long startTime = System.nanoTime();

    try {
      List<ActionCluster> clusters = actionClusters.get(round);
      if (clusters == null) {
        log.warn("No action clusters found for betting round: {}", round);
        return createDefaultActionTemplate(action);
      }

      // Handle non-betting actions directly
      if (action.getAction() == Action.FOLD) {
        return findActionTemplate(clusters, ActionType.FOLD);
      } else if (action.getAction() == Action.CHECK) {
        return findActionTemplate(clusters, ActionType.CHECK);
      } else if (action.getAction() == Action.CALL) {
        return findActionTemplate(clusters, ActionType.CALL);
      }

      // Handle betting actions with size abstraction
      if (action.getAction() == Action.BET || action.getAction() == Action.RAISE) {
        double betSizeRatio = potSize > 0 ? (double) action.getAmount() / potSize : 0.0;
        return findBestBetSizeCluster(clusters, betSizeRatio, action.getAction());
      }

      incrementCounter("unknown_actions");
      return createDefaultActionTemplate(action);

    } catch (Exception e) {
      log.error("Error abstracting action {}: {}", action, e.getMessage(), e);
      incrementCounter("errors");
      return createDefaultActionTemplate(action);
    } finally {
      long elapsedTime = System.nanoTime() - startTime;
      incrementCounter("total_time", elapsedTime);
      incrementCounter("abstractions_created");
    }
  }

  /**
   * Find action template for basic actions
   */
  private ActionTemplate findActionTemplate(List<ActionCluster> clusters, ActionType actionType) {
    return clusters.stream().filter(cluster -> cluster.getActionType() == actionType).findFirst()
        .map(cluster -> cluster.getActions().get(0)).orElse(null);
  }

  /**
   * Find best bet size cluster for a betting action
   */
  private ActionTemplate findBestBetSizeCluster(List<ActionCluster> clusters, double betSizeRatio,
      Action actionType) {
    ActionType targetType = actionType == Action.BET ? ActionType.BET : ActionType.RAISE;

    ActionCluster bestCluster = null;
    double bestDistance = Double.MAX_VALUE;

    for (ActionCluster cluster : clusters) {
      if (cluster.getActionType() == targetType && cluster.getRepresentativeSize() != null) {
        double distance = Math.abs(cluster.getRepresentativeSize() - betSizeRatio);
        if (distance < bestDistance) {
          bestDistance = distance;
          bestCluster = cluster;
        }
      }
    }

    if (bestCluster != null) {
      // Update cluster with this bet size
      bestCluster.addBetSize(betSizeRatio);
      return bestCluster.getActions().get(0);
    }

    // Create new template if no suitable cluster found
    return ActionTemplate.builder().actionType(targetType).betSizeRatio(betSizeRatio).frequency(0.0)
        .description(String.format("%s %.2fx pot", targetType, betSizeRatio)).build();
  }

  /**
   * Create default action template for unknown actions using ActionBridge
   */
  private ActionTemplate createDefaultActionTemplate(ActionInstance action) {
    // Use ActionBridge for consistent conversion
    ActionTemplate bridgeTemplate = ActionBridge.toActionTemplate(action.getAction());

    if (bridgeTemplate != null) {
      return bridgeTemplate;
    }

    // Fallback to original logic
    ActionType actionType = convertToActionType(action.getAction());
    return ActionTemplate.builder()
        .actionType(actionType)
        .betSizeRatio(null)
        .frequency(0.0)
        .description("Default " + actionType)
        .build();
  }

  /**
   * Convert Action to ActionType
   */
  private ActionType convertToActionType(Action action) {
    switch (action) {
      case FOLD:
        return ActionType.FOLD;
      case CHECK:
        return ActionType.CHECK;
      case CALL:
        return ActionType.CALL;
      case BET:
        return ActionType.BET;
      case RAISE:
        return ActionType.RAISE;
      default:
        return ActionType.BET; // Default fallback
    }
  }

  /**
   * Get available action templates for a betting round
   */
  public List<ActionTemplate> getAvailableActions(BettingRound round, GameContext context) {
    List<ActionCluster> clusters = actionClusters.get(round);
    if (clusters == null) {
      return Collections.emptyList();
    }

    return clusters.stream().flatMap(cluster -> cluster.getActions().stream())
        .filter(template -> isActionValid(template, context)).collect(Collectors.toList());
  }

  /**
   * Check if an action template is valid in the current context
   */
  private boolean isActionValid(ActionTemplate template, GameContext context) {
    // Basic validation - can be extended with more sophisticated logic
    switch (template.getActionType()) {
      case FOLD:
      case CALL:
        return true; // Always valid
      case CHECK:
        return context.getCurrentBet() == null || context.getCurrentBet() == 0;
      case BET:
      case RAISE:
        return template.getBetSizeRatio() != null && template.getBetSizeRatio() > 0.0 && (
            context.getStackSize() == null
                || template.getBetSizeRatio() * context.getPotSize() <= context.getStackSize());
      case ALL_IN:
        return context.getStackSize() != null && context.getStackSize() > 0;
      default:
        return false;
    }
  }

  /**
   * Update action frequency for refinement
   */
  public void updateActionFrequency(ActionTemplate template) {
    actionFrequencies.merge(template, 1L, Long::sum);
    incrementCounter("frequency_updates");
  }

  /**
   * Refine action clusters based on usage patterns
   */
  public void refineActionClusters() {
    log.info("Starting action cluster refinement");

    for (BettingRound round : BettingRound.values()) {
      refineActionClustersForRound(round);
    }

    log.info("Action cluster refinement completed");
  }

  /**
   * Refine action clusters for a specific betting round
   */
  private void refineActionClustersForRound(BettingRound round) {
    List<ActionCluster> clusters = actionClusters.get(round);
    if (clusters == null) {
      return;
    }

    // Analyze cluster usage and variance
    for (ActionCluster cluster : clusters) {
      if (cluster.getActionType() == ActionType.BET
          || cluster.getActionType() == ActionType.RAISE) {
        double variance = cluster.getVariance();

        // Split clusters with high variance
        if (variance > config.getActionSimilarityThreshold() && cluster.getBetSizes().size() > 10) {
          splitCluster(cluster, round);
        }

        // Merge clusters with low usage
        if (cluster.getFrequency() != null && cluster.getFrequency() < 0.01) {
          // Mark for potential merging
          log.debug("Low usage cluster {} in {}: frequency {}", cluster.getClusterName(), round,
              cluster.getFrequency());
        }
      }
    }
  }

  /**
   * Split a cluster with high variance
   */
  private void splitCluster(ActionCluster cluster, BettingRound round) {
    List<Double> betSizes = new ArrayList<>(cluster.getBetSizes());
    Collections.sort(betSizes);

    int midPoint = betSizes.size() / 2;
    List<Double> lowerSizes = betSizes.subList(0, midPoint);
    List<Double> upperSizes = betSizes.subList(midPoint, betSizes.size());

    // Create two new clusters
    ActionCluster lowerCluster = createClusterFromSizes(lowerSizes, cluster.getActionType(), round);
    ActionCluster upperCluster = createClusterFromSizes(upperSizes, cluster.getActionType(), round);

    // Replace original cluster
    List<ActionCluster> clusters = actionClusters.get(round);
    clusters.remove(cluster);
    clusters.add(lowerCluster);
    clusters.add(upperCluster);

    log.info("Split cluster {} into {} and {} for {}", cluster.getClusterName(),
        lowerCluster.getClusterName(), upperCluster.getClusterName(), round);
  }

  /**
   * Create cluster from bet sizes
   */
  private ActionCluster createClusterFromSizes(List<Double> betSizes, ActionType actionType,
      BettingRound round) {
    double avgSize = betSizes.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

    ActionTemplate template = ActionTemplate.builder().actionType(actionType).betSizeRatio(avgSize)
        .frequency(0.0).description(String.format("%s %.2fx pot", actionType, avgSize)).build();

    ActionCluster cluster = ActionCluster.builder().clusterId(nextClusterId.getAndIncrement())
        .clusterName(template.getDescription()).actions(Arrays.asList(template))
        .representativeSize(avgSize).actionType(actionType).frequency(0.0)
        .betSizes(new ArrayList<>(betSizes)).build();

    return cluster;
  }

  /**
   * Get action abstraction statistics
   */
  public ActionAbstractionStatistics getStatistics() {
    Map<BettingRound, Integer> clusterCounts = new HashMap<>();
    int totalClusters = 0;

    for (Map.Entry<BettingRound, List<ActionCluster>> entry : actionClusters.entrySet()) {
      int count = entry.getValue().size();
      clusterCounts.put(entry.getKey(), count);
      totalClusters += count;
    }

    return ActionAbstractionStatistics.builder().totalClusters(totalClusters)
        .clustersByRound(clusterCounts).totalTemplates(actionTemplates.size())
        .performanceCounters(new HashMap<>(performanceCounters)).build();
  }

  /**
   * Increment performance counter
   */
  private void incrementCounter(String counterName) {
    incrementCounter(counterName, 1L);
  }

  /**
   * Increment performance counter by value
   */
  private void incrementCounter(String counterName, long value) {
    performanceCounters.merge(counterName, value, Long::sum);
  }
}

/**
 * Action abstraction statistics
 */
@Data
@Builder
class ActionAbstractionStatistics {

  private int totalClusters;
  private Map<BettingRound, Integer> clustersByRound;
  private int totalTemplates;
  private Map<String, Long> performanceCounters;

  public String getSummary() {
    return String.format("Action Clusters: %d total, Templates: %d", totalClusters, totalTemplates);
  }
}
