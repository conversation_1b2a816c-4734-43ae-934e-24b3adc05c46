package com.example.texasholdem.abstraction;

import lombok.Builder;
import lombok.Data;

/**
 * Configuration for abstraction systems in Pluribus Poker AI
 * <p>
 * Controls abstraction quality, performance trade-offs, and system behavior for information,
 * action, and card abstraction engines.
 */
@Data
@Builder
public class AbstractionConfig {

  // Information Abstraction Configuration
  @Builder.Default
  private double similarityThreshold = 0.85; // Threshold for grouping similar infosets

  @Builder.Default
  private double minCompressionRatio = 2.0; // Minimum compression ratio to maintain

  @Builder.Default
  private int maxAbstractionSize = 1000; // Maximum infosets per abstraction

  @Builder.Default
  private boolean enableDynamicRefinement = true; // Enable dynamic abstraction refinement

  // Action Abstraction Configuration
  @Builder.Default
  private int maxActionClusters = 12; // Maximum action clusters per betting round

  @Builder.Default
  private double actionSimilarityThreshold = 0.75; // Threshold for grouping similar actions

  @Builder.Default
  private boolean preserveCheckFold = true; // Always preserve check/fold actions

  @Builder.Default
  private boolean enableBetSizing = true; // Enable bet sizing abstraction

  // Card Abstraction Configuration
  @Builder.Default
  private int handStrengthBuckets = 50; // Number of hand strength buckets

  @Builder.Default
  private int potentialBuckets = 25; // Number of hand potential buckets

  @Builder.Default
  private double handSimilarityThreshold = 0.90; // Threshold for grouping similar hands

  @Builder.Default
  private boolean enablePositionalAdjustment = true; // Adjust abstraction by position

  // Performance Configuration
  @Builder.Default
  private int cacheSize = 10000; // Size of feature cache

  @Builder.Default
  private boolean enableParallelProcessing = true; // Enable parallel abstraction processing

  @Builder.Default
  private int threadPoolSize = 4; // Thread pool size for parallel processing

  // Unified Pipeline Configuration
  @Builder.Default
  private boolean enableComprehensiveAbstraction = true; // Enable comprehensive abstraction pipeline

  @Builder.Default
  private boolean enableLegacyFallback = true; // Enable fallback to InfosetStoreKey

  @Builder.Default
  private boolean enablePerformanceOptimization = true; // Enable performance optimizations

  // Quality Metrics Configuration
  @Builder.Default
  private double minStrategicValue = 0.1; // Minimum strategic value to maintain

  @Builder.Default
  private boolean enableQualityMonitoring = true; // Enable quality monitoring

  @Builder.Default
  private int qualityCheckInterval = 1000; // Interval for quality checks

  // Refinement Configuration
  @Builder.Default
  private double refinementThreshold = 0.05; // Threshold for triggering refinement

  @Builder.Default
  private int refinementInterval = 10000; // Interval for refinement checks

  @Builder.Default
  private int maxRefinementIterations = 5; // Maximum refinement iterations

  /**
   * Create default configuration optimized for production use
   */
  public static AbstractionConfig defaultConfig() {
    return AbstractionConfig.builder().build();
  }

  /**
   * Create configuration optimized for training (higher quality, slower)
   */
  public static AbstractionConfig trainingConfig() {
    return AbstractionConfig.builder()
        .similarityThreshold(0.90)
        .minCompressionRatio(1.5)
        .actionSimilarityThreshold(0.80)
        .handSimilarityThreshold(0.95)
        .enableDynamicRefinement(true)
        .enableQualityMonitoring(true)
        .maxRefinementIterations(10)
        .build();
  }

  /**
   * Create configuration optimized for real-time play (lower quality, faster)
   */
  public static AbstractionConfig realtimeConfig() {
    return AbstractionConfig.builder()
        .similarityThreshold(0.75)
        .minCompressionRatio(3.0)
        .actionSimilarityThreshold(0.65)
        .handSimilarityThreshold(0.85)
        .enableDynamicRefinement(false)
        .enableQualityMonitoring(false)
        .enableParallelProcessing(true)
        .threadPoolSize(8)
        .build();
  }

  /**
   * Create configuration for testing (balanced quality and speed)
   */
  public static AbstractionConfig testConfig() {
    return AbstractionConfig.builder()
        .similarityThreshold(0.80)
        .minCompressionRatio(2.5)
        .maxActionClusters(8)
        .handStrengthBuckets(25)
        .potentialBuckets(10)
        .cacheSize(1000)
        .enableDynamicRefinement(false)
        .enableQualityMonitoring(true)
        .build();
  }

  /**
   * Validate configuration parameters
   */
  public void validate() {
    if (similarityThreshold < 0.0 || similarityThreshold > 1.0) {
      throw new IllegalArgumentException("Similarity threshold must be between 0.0 and 1.0");
    }

    if (minCompressionRatio < 1.0) {
      throw new IllegalArgumentException("Minimum compression ratio must be >= 1.0");
    }

    if (maxAbstractionSize <= 0) {
      throw new IllegalArgumentException("Maximum abstraction size must be positive");
    }

    if (maxActionClusters <= 0) {
      throw new IllegalArgumentException("Maximum action clusters must be positive");
    }

    if (handStrengthBuckets <= 0 || potentialBuckets <= 0) {
      throw new IllegalArgumentException("Hand buckets must be positive");
    }

    if (cacheSize <= 0) {
      throw new IllegalArgumentException("Cache size must be positive");
    }

    if (threadPoolSize <= 0) {
      throw new IllegalArgumentException("Thread pool size must be positive");
    }
  }

  /**
   * Get configuration summary for logging
   */
  public String getSummary() {
    return String.format(
        "AbstractionConfig{similarity=%.2f, compression=%.1f, actions=%d, hands=%d/%d, cache=%d, threads=%d}",
        similarityThreshold, minCompressionRatio, maxActionClusters,
        handStrengthBuckets, potentialBuckets, cacheSize, threadPoolSize
    );
  }
}
