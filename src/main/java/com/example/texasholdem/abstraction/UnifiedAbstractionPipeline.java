package com.example.texasholdem.abstraction;

import com.example.texasholdem.model.*;
import com.example.texasholdem.service.GameService;
import com.example.texasholdem.service.HandEvaluator;
import com.example.texasholdem.strategy.model.AbstractAction;
import com.example.texasholdem.strategy.model.ActionTrace;
import com.example.texasholdem.strategy.model.Infoset;
import com.example.texasholdem.strategy.model.InfosetStore;
import com.example.texasholdem.strategy.model.ThreadSafeInfosetStore;
import com.example.texasholdem.strategy.train.InfosetStoreKey;
import com.example.texasholdem.abstraction.AbstractionDataStructures.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Unified Abstraction Pipeline for Pluribus Poker AI
 * 
 * Integrates all abstraction engines (Information, Action, Card) into a single pipeline
 * that works seamlessly with existing MCCFR training infrastructure.
 * 
 * Key Features:
 * - Backward compatibility with InfosetStoreKey
 * - Performance optimization with multi-level caching
 * - Thread-safe operation for parallel training
 * - Configurable abstraction quality vs performance trade-offs
 * - Integration with existing InfosetStore and ThreadSafeInfosetStore
 * - Minimal overhead for 4-8x speedup maintenance
 */
@Slf4j
public class UnifiedAbstractionPipeline {

    // Core abstraction components
    private final AbstractionManager abstractionManager;
    private final AbstractionConfig config;
    
    // Performance optimization caches
    private final ConcurrentHashMap<Long, Integer> infosetAbstractionCache;
    private final ConcurrentHashMap<String, ActionTemplate> actionTemplateCache;
    private final ConcurrentHashMap<String, HandBucket> handBucketCache;
    private final ConcurrentHashMap<Long, ComprehensiveAbstraction> comprehensiveCache;
    
    // Performance monitoring
    private final AtomicLong totalAbstractionCalls;
    private final AtomicLong cacheHits;
    private final AtomicLong cacheMisses;
    private final AtomicLong fallbackToLegacy;
    
    // Thread-local optimization for parallel training
    private final ThreadLocal<GameContext> threadLocalGameContext;
    private final ThreadLocal<Map<String, Object>> threadLocalTempData;
    
    // Integration with existing stores
    private final InfosetStore infosetStore;
    private final ThreadSafeInfosetStore threadSafeInfosetStore;
    
    // Configuration flags
    private final boolean enableComprehensiveAbstraction;
    private final boolean enableLegacyFallback;
    private final boolean enablePerformanceOptimization;

    /**
     * Constructor for unified abstraction pipeline
     */
    public UnifiedAbstractionPipeline(AbstractionConfig config, InfosetStore infosetStore, 
            ThreadSafeInfosetStore threadSafeInfosetStore, HandEvaluator handEvaluator) {
        this.config = config != null ? config : AbstractionConfig.defaultConfig();
        this.infosetStore = infosetStore;
        this.threadSafeInfosetStore = threadSafeInfosetStore;
        
        // Initialize abstraction manager with all engines
        this.abstractionManager = new AbstractionManager(this.config, infosetStore, handEvaluator);
        
        // Initialize performance caches
        this.infosetAbstractionCache = new ConcurrentHashMap<>();
        this.actionTemplateCache = new ConcurrentHashMap<>();
        this.handBucketCache = new ConcurrentHashMap<>();
        this.comprehensiveCache = new ConcurrentHashMap<>();
        
        // Initialize performance monitoring
        this.totalAbstractionCalls = new AtomicLong(0);
        this.cacheHits = new AtomicLong(0);
        this.cacheMisses = new AtomicLong(0);
        this.fallbackToLegacy = new AtomicLong(0);
        
        // Initialize thread-local storage
        this.threadLocalGameContext = ThreadLocal.withInitial(() -> GameContext.builder().build());
        this.threadLocalTempData = ThreadLocal.withInitial(HashMap::new);
        
        // Configuration flags
        this.enableComprehensiveAbstraction = this.config.isEnableComprehensiveAbstraction();
        this.enableLegacyFallback = this.config.isEnableLegacyFallback();
        this.enablePerformanceOptimization = this.config.isEnablePerformanceOptimization();
        
        log.info("UnifiedAbstractionPipeline initialized: comprehensive={}, legacy={}, optimization={}", 
            enableComprehensiveAbstraction, enableLegacyFallback, enablePerformanceOptimization);
    }

    /**
     * Main abstraction method - replaces InfosetStoreKey.abstractInfoset()
     * 
     * This method provides backward compatibility while enabling the new abstraction pipeline
     */
    public int abstractInfoset(Infoset infoset) {
        totalAbstractionCalls.incrementAndGet();
        
        try {
            if (enableComprehensiveAbstraction) {
                return createComprehensiveAbstraction(infoset);
            } else {
                return createLegacyAbstraction(infoset);
            }
        } catch (Exception e) {
            log.warn("Error in abstraction pipeline, falling back to legacy: {}", e.getMessage());
            fallbackToLegacy.incrementAndGet();
            return InfosetStoreKey.abstractInfoset(infoset);
        }
    }

    /**
     * Create comprehensive abstraction using all engines
     */
    private int createComprehensiveAbstraction(Infoset infoset) {
        long infosetKey = infoset.toLongKey();
        
        // Check cache first
        Integer cachedAbstraction = infosetAbstractionCache.get(infosetKey);
        if (cachedAbstraction != null) {
            cacheHits.incrementAndGet();
            return cachedAbstraction;
        }
        
        cacheMisses.incrementAndGet();
        
        // Create game context from infoset
        GameContext context = createGameContextFromInfoset(infoset);
        
        // Create comprehensive abstraction
        ComprehensiveAbstraction comprehensive = abstractionManager.createComprehensiveAbstraction(
            createSearchGameStateFromContext(context));
        
        // Extract information abstraction ID
        int abstractionId = comprehensive.getInformationAbstractionId();
        
        // Cache the result
        if (enablePerformanceOptimization && infosetAbstractionCache.size() < config.getCacheSize()) {
            infosetAbstractionCache.put(infosetKey, abstractionId);
            comprehensiveCache.put(infosetKey, comprehensive);
        }
        
        return abstractionId;
    }

    /**
     * Create legacy abstraction for backward compatibility
     */
    private int createLegacyAbstraction(Infoset infoset) {
        return InfosetStoreKey.abstractInfoset(infoset);
    }

    /**
     * Create game context from infoset for abstraction engines
     */
    private GameContext createGameContextFromInfoset(Infoset infoset) {
        GameContext context = threadLocalGameContext.get();
        
        try {
            // Decode cards from infoset
            List<Card> allCards = decodeCardsFromBits(infoset.getHoleAndCommunityBits());
            
            // Separate hole cards and community cards based on betting round
            BettingRound round = BettingRound.values()[infoset.getRound()];
            int communityCardCount = getCommunityCardCount(round);
            
            List<Card> holeCards = allCards.subList(0, Math.min(2, allCards.size()));
            List<Card> communityCards = allCards.size() > 2 ? 
                allCards.subList(2, Math.min(2 + communityCardCount, allCards.size())) : 
                new ArrayList<>();
            
            // Create action history from encoded data
            List<ActionTrace> actionHistory = decodeActionHistory(infoset.getActionHistory());
            
            // Build context
            return GameContext.builder()
                .holeCards(holeCards)
                .communityCards(communityCards)
                .actionHistory(actionHistory)
                .position(infoset.getPlayerIndex())
                .bettingRound(round)
                .currentBet(infoset.getCurrentRoundBetAmount())
                .build();
                
        } catch (Exception e) {
            log.warn("Error creating game context from infoset: {}", e.getMessage());
            return GameContext.builder().build();
        }
    }

    /**
     * Get community card count for betting round
     */
    private int getCommunityCardCount(BettingRound round) {
        return switch (round) {
            case PREFLOP -> 0;
            case FLOP -> 3;
            case TURN -> 4;
            case RIVER -> 5;
            default -> 0;
        };
    }

    /**
     * Decode cards from bit representation
     */
    private List<Card> decodeCardsFromBits(long bits) {
        List<Card> cards = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            int cardIndex = (int) ((bits >> (i * 6)) & 0x3F);
            if (cardIndex >= 52) break;
            
            int suit = cardIndex / 13;
            int rank = cardIndex % 13;
            cards.add(new Card(Card.Rank.values()[rank], fromSuitIndex(suit)));
        }
        return cards;
    }

    /**
     * Convert suit index to Card.Suit
     */
    private Card.Suit fromSuitIndex(int index) {
        for (Card.Suit suit : Card.Suit.values()) {
            if (suit.getSuitIndex() == index) {
                return suit;
            }
        }
        return Card.Suit.CLUBS; // Default fallback
    }

    /**
     * Decode action history from encoded data
     */
    private List<ActionTrace> decodeActionHistory(long actionBits) {
        List<ActionTrace> history = new ArrayList<>();
        
        // Simple decoding - in production this would be more sophisticated
        for (int i = 0; i < 5 && actionBits > 0; i++) {
            int actionCode = (int) (actionBits & 0b111);
            actionBits >>= 3;
            
            AbstractAction action = switch (actionCode) {
                case 0b000 -> AbstractAction.FOLD;
                case 0b001 -> AbstractAction.CHECK_OR_CALL;
                case 0b010 -> AbstractAction.BET_OR_RAISE_30;
                case 0b011 -> AbstractAction.BET_OR_RAISE_60;
                case 0b100 -> AbstractAction.BET_OR_RAISE_100;
                case 0b101 -> AbstractAction.BET_OR_RAISE_200;
                case 0b110 -> AbstractAction.BET_OR_RAISE_500;
                default -> AbstractAction.CHECK_OR_CALL;
            };
            
            history.add(0, new ActionTrace(0, action, 0, i, 0));
        }
        
        return history;
    }

    /**
     * Create SearchGameState from GameContext for AbstractionManager
     */
    private SearchGameState createSearchGameStateFromContext(GameContext context) {
        // This is a simplified implementation
        // In production, this would create a proper SearchGameState
        return new SearchGameState() {
            @Override
            public BettingRound getCurrentRound() {
                return context.getBettingRound() != null ? context.getBettingRound() : BettingRound.PREFLOP;
            }
            
            @Override
            public PlayerSnapshot getCurrentPlayer() {
                return new PlayerSnapshot() {
                    @Override
                    public int getPlayerIndex() {
                        return context.getPosition() != null ? context.getPosition() : 0;
                    }
                    
                    @Override
                    public List<Card> getHoleCards() {
                        return context.getHoleCards() != null ? context.getHoleCards() : new ArrayList<>();
                    }
                    
                    @Override
                    public int getChips() {
                        return context.getStackSize() != null ? context.getStackSize() : 10000;
                    }
                };
            }
            
            @Override
            public List<Card> getCommunityCards() {
                return context.getCommunityCards() != null ? context.getCommunityCards() : new ArrayList<>();
            }
            
            @Override
            public List<ActionTrace> getActionHistory() {
                return context.getActionHistory() != null ? context.getActionHistory() : new ArrayList<>();
            }
        };
    }

    // ========================================
    // PERFORMANCE AND MONITORING METHODS
    // ========================================

    /**
     * Get performance statistics
     */
    public Map<String, Object> getPerformanceStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalAbstractionCalls", totalAbstractionCalls.get());
        stats.put("cacheHits", cacheHits.get());
        stats.put("cacheMisses", cacheMisses.get());
        stats.put("fallbackToLegacy", fallbackToLegacy.get());
        stats.put("cacheHitRate", calculateCacheHitRate());
        stats.put("infosetCacheSize", infosetAbstractionCache.size());
        stats.put("actionCacheSize", actionTemplateCache.size());
        stats.put("handCacheSize", handBucketCache.size());
        stats.put("comprehensiveCacheSize", comprehensiveCache.size());
        return stats;
    }

    /**
     * Calculate cache hit rate
     */
    private double calculateCacheHitRate() {
        long hits = cacheHits.get();
        long misses = cacheMisses.get();
        long total = hits + misses;
        return total > 0 ? (double) hits / total : 0.0;
    }

    /**
     * Clear all caches
     */
    public void clearCaches() {
        infosetAbstractionCache.clear();
        actionTemplateCache.clear();
        handBucketCache.clear();
        comprehensiveCache.clear();
        log.info("All abstraction caches cleared");
    }

    /**
     * Get cache statistics summary
     */
    public String getCacheStatistics() {
        return String.format(
            "UnifiedAbstractionPipeline Cache Stats: calls=%d, hits=%d, misses=%d, hit_rate=%.2f%%, " +
            "infoset_cache=%d, action_cache=%d, hand_cache=%d, comprehensive_cache=%d",
            totalAbstractionCalls.get(), cacheHits.get(), cacheMisses.get(),
            calculateCacheHitRate() * 100, infosetAbstractionCache.size(),
            actionTemplateCache.size(), handBucketCache.size(), comprehensiveCache.size()
        );
    }

    /**
     * Create default action template
     */
    private ActionTemplate createDefaultActionTemplate() {
        return ActionTemplate.builder()
            .templateId("default")
            .bettingRound(BettingRound.PREFLOP)
            .actions(Arrays.asList(
                AbstractAction.FOLD,
                AbstractAction.CHECK_OR_CALL,
                AbstractAction.BET_OR_RAISE_30
            ))
            .build();
    }

    /**
     * Create default hand bucket
     */
    private HandBucket createDefaultHandBucket() {
        return HandBucket.builder()
            .bucketId(0)
            .bucketName("default")
            .minStrength(0.0)
            .maxStrength(1.0)
            .build();
    }

    /**
     * Create default comprehensive abstraction
     */
    private ComprehensiveAbstraction createDefaultComprehensiveAbstraction() {
        return ComprehensiveAbstraction.builder()
            .informationAbstractionId(0)
            .actionTemplate(createDefaultActionTemplate())
            .handBucket(createDefaultHandBucket())
            .build();
    }

    /**
     * Create game context from GameService
     */
    private GameContext createGameContextFromGameService(Player player, GameService gameService,
            List<ActionTrace> history) {
        return GameContext.builder()
            .holeCards(player.getHand())
            .communityCards(gameService.getCommunityCards())
            .actionHistory(history)
            .position(player.getPlayerIndex())
            .bettingRound(gameService.getCurrentBettingRound())
            .numActivePlayers(gameService.getActivePlayers().size())
            .potSize(gameService.getPotSize())
            .currentBet(gameService.getCurrentRoundBetAmount())
            .stackSize(player.getChips())
            .build();
    }

    /**
     * Create cache key for action abstraction
     */
    private String createActionCacheKey(Player player, GameService gameService, List<ActionTrace> history) {
        return String.format("action_%d_%s_%d_%d_%d",
            player.getPlayerIndex(),
            gameService.getCurrentBettingRound(),
            gameService.getPotSize(),
            gameService.getCurrentRoundBetAmount(),
            history.size());
    }

    /**
     * Create cache key for hand abstraction
     */
    private String createHandCacheKey(List<Card> holeCards, List<Card> communityCards, BettingRound round) {
        StringBuilder sb = new StringBuilder("hand_");
        sb.append(round.ordinal()).append("_");

        for (Card card : holeCards) {
            sb.append(card.getRank().ordinal()).append(card.getSuit().getSuitIndex());
        }
        sb.append("_");
        for (Card card : communityCards) {
            sb.append(card.getRank().ordinal()).append(card.getSuit().getSuitIndex());
        }

        return sb.toString();
    }

    /**
     * Cleanup resources
     */
    public void cleanup() {
        clearCaches();
        threadLocalGameContext.remove();
        threadLocalTempData.remove();
        abstractionManager.cleanup();
        log.info("UnifiedAbstractionPipeline cleanup completed");
    }
}
