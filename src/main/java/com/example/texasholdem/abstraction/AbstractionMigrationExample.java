package com.example.texasholdem.abstraction;

import com.example.texasholdem.abstraction.AbstractionDataStructures.ActionTemplate;
import com.example.texasholdem.abstraction.AbstractionDataStructures.HandBucket;
import com.example.texasholdem.model.*;
import com.example.texasholdem.service.GameService;
import com.example.texasholdem.service.HandEvaluator;
import com.example.texasholdem.strategy.model.*;
import com.example.texasholdem.strategy.train.InfosetStoreKey;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * Migration example showing how to integrate the new abstraction system
 * with existing MCCFR trainers.
 * 
 * This class demonstrates:
 * 1. How to replace InfosetStoreKey.abstractInfoset() calls
 * 2. How to enable enhanced abstraction features
 * 3. How to maintain backward compatibility
 * 4. How to optimize performance for parallel training
 */
@Slf4j
public class AbstractionMigrationExample {

    // ========================================
    // BEFORE: Original Implementation
    // ========================================

    /**
     * Original method using InfosetStoreKey directly
     */
    public void originalAbstractionMethod(Player player, GameService gameService, 
            List<ActionTrace> history, InfosetStore store) {
        
        // OLD WAY: Direct InfosetStoreKey usage
        Infoset infoset = Infoset.of(
            player.getHand(),
            gameService.getCommunityCards(),
            gameService.getCurrentBettingRound(),
            player,
            gameService.getCurrentRoundBetAmount(),
            history
        );
        
        // OLD: Direct abstraction call
        int abstractionKey = InfosetStoreKey.abstractInfoset(infoset);
        
        // OLD: Direct store access
        InfosetValue infosetValue = store.getOrCreate(player.getPlayerIndex(), abstractionKey);
        
        log.debug("Original abstraction: player={}, key={}", player.getPlayerIndex(), abstractionKey);
    }

    // ========================================
    // AFTER: Enhanced Implementation
    // ========================================

    /**
     * Enhanced method using the new abstraction system
     */
    public void enhancedAbstractionMethod(Player player, GameService gameService, 
            List<ActionTrace> history, ThreadSafeInfosetStore store, 
            AbstractionIntegrationService abstractionService) {
        
        // NEW WAY: Create infoset (same as before)
        Infoset infoset = Infoset.of(
            player.getHand(),
            gameService.getCommunityCards(),
            gameService.getCurrentBettingRound(),
            player,
            gameService.getCurrentRoundBetAmount(),
            history
        );
        
        // NEW: Enhanced abstraction call with comprehensive features
        int abstractionKey = abstractionService.abstractInfoset(infoset);
        
        // NEW: Thread-safe store access (compatible with parallel training)
        ThreadSafeInfosetValue infosetValue = store.getOrCreate(player.getPlayerIndex(), abstractionKey);
        
        // NEW: Optional - Get additional abstraction information
        if (abstractionService.isEnhancedAbstractionEnabled()) {
            // Get action abstraction for better action space management
            ActionTemplate actionTemplate = abstractionService.getActionAbstraction(
                player, gameService, history);
            
            // Get card abstraction for hand strength analysis
            HandBucket handBucket = abstractionService.getCardAbstraction(
                player.getHand(), gameService.getCommunityCards(), 
                gameService.getCurrentBettingRound());
            
            // Get comprehensive abstraction for complete analysis
            ComprehensiveAbstraction comprehensive = abstractionService.getComprehensiveAbstraction(
                player, gameService, history);
            
            log.debug("Enhanced abstraction: player={}, key={}, action_template={}, hand_bucket={}", 
                player.getPlayerIndex(), abstractionKey, 
                actionTemplate != null ? actionTemplate.getTemplateId() : "null",
                handBucket != null ? handBucket.getBucketId() : "null");
        } else {
            log.debug("Enhanced abstraction (legacy mode): player={}, key={}", 
                player.getPlayerIndex(), abstractionKey);
        }
    }

    // ========================================
    // MIGRATION STRATEGIES
    // ========================================

    /**
     * Strategy 1: Drop-in replacement (minimal changes)
     */
    public void dropInReplacementExample(Player player, GameService gameService, 
            List<ActionTrace> history, InfosetStore store) {
        
        // STEP 1: Create infoset (no change)
        Infoset infoset = Infoset.of(
            player.getHand(),
            gameService.getCommunityCards(),
            gameService.getCurrentBettingRound(),
            player,
            gameService.getCurrentRoundBetAmount(),
            history
        );
        
        // STEP 2: Replace InfosetStoreKey.abstractInfoset() with static method
        // OLD: int abstractionKey = InfosetStoreKey.abstractInfoset(infoset);
        int abstractionKey = AbstractionIntegrationService.abstractInfosetStatic(infoset);
        
        // STEP 3: Use existing store (no change)
        InfosetValue infosetValue = store.getOrCreate(player.getPlayerIndex(), abstractionKey);
        
        // This approach provides enhanced abstraction with zero code changes
        // if AbstractionIntegrationService is properly initialized
    }

    /**
     * Strategy 2: Gradual migration (recommended)
     */
    public void gradualMigrationExample(Player player, GameService gameService, 
            List<ActionTrace> history, ThreadSafeInfosetStore store) {
        
        // STEP 1: Initialize abstraction service (once per trainer)
        AbstractionIntegrationService abstractionService = new AbstractionIntegrationService();
        
        // STEP 2: Use enhanced abstraction
        Infoset infoset = Infoset.of(
            player.getHand(),
            gameService.getCommunityCards(),
            gameService.getCurrentBettingRound(),
            player,
            gameService.getCurrentRoundBetAmount(),
            history
        );
        
        int abstractionKey = abstractionService.abstractInfoset(infoset);
        ThreadSafeInfosetValue infosetValue = store.getOrCreate(player.getPlayerIndex(), abstractionKey);
        
        // STEP 3: Gradually add enhanced features as needed
        // This can be done incrementally without breaking existing functionality
    }

    /**
     * Strategy 3: Full integration (maximum benefits)
     */
    public void fullIntegrationExample(Player player, GameService gameService, 
            List<ActionTrace> history, ThreadSafeInfosetStore store,
            AbstractionIntegrationService abstractionService) {
        
        // STEP 1: Get comprehensive abstraction
        ComprehensiveAbstraction comprehensive = abstractionService.getComprehensiveAbstraction(
            player, gameService, history);
        
        if (comprehensive != null) {
            // STEP 2: Use information abstraction
            int informationAbstractionId = comprehensive.getInformationAbstractionId();
            ThreadSafeInfosetValue infosetValue = store.getOrCreate(
                player.getPlayerIndex(), informationAbstractionId);
            
            // STEP 3: Use action abstraction for better action selection
            ActionTemplate actionTemplate = comprehensive.getActionTemplate();
            if (actionTemplate != null) {
                List<AbstractAction> availableActions = actionTemplate.getActions();
                // Use abstracted action space for more efficient training
                log.debug("Using {} abstracted actions instead of full action space", 
                    availableActions.size());
            }
            
            // STEP 4: Use card abstraction for hand evaluation
            HandBucket handBucket = comprehensive.getHandBucket();
            if (handBucket != null) {
                // Use hand bucket information for strategic decisions
                log.debug("Hand in bucket {} with strength range [{}, {}]", 
                    handBucket.getBucketId(), handBucket.getMinStrength(), handBucket.getMaxStrength());
            }
            
        } else {
            // Fallback to standard abstraction
            Infoset infoset = Infoset.of(player.getHand(), gameService.getCommunityCards(),
                gameService.getCurrentBettingRound(), player, gameService.getCurrentRoundBetAmount(), history);
            
            int abstractionKey = abstractionService.abstractInfoset(infoset);
            ThreadSafeInfosetValue infosetValue = store.getOrCreate(player.getPlayerIndex(), abstractionKey);
        }
    }

    // ========================================
    // PERFORMANCE OPTIMIZATION EXAMPLES
    // ========================================

    /**
     * Example of performance optimization for parallel training
     */
    public void performanceOptimizationExample(AbstractionIntegrationService abstractionService) {
        
        // Enable enhanced abstraction for performance-critical threads
        abstractionService.enableEnhancedAbstraction();
        
        // Monitor performance
        String stats = abstractionService.getPerformanceStatistics();
        log.info("Abstraction performance: {}", stats);
        
        // Adjust configuration based on performance metrics
        if (stats.contains("hit_rate") && stats.contains("90.")) {
            log.info("High cache hit rate detected - abstraction system performing well");
        } else {
            log.warn("Low cache hit rate - consider adjusting abstraction configuration");
        }
    }

    /**
     * Example of configuration tuning for different scenarios
     */
    public void configurationTuningExample() {
        
        // Training configuration (high quality, slower)
        AbstractionConfig trainingConfig = AbstractionConfig.trainingConfig();
        log.info("Training config: {}", trainingConfig.getSummary());
        
        // Real-time configuration (lower quality, faster)
        AbstractionConfig realtimeConfig = AbstractionConfig.realtimeConfig();
        log.info("Real-time config: {}", realtimeConfig.getSummary());
        
        // Custom configuration for specific needs
        AbstractionConfig customConfig = AbstractionConfig.builder()
            .enableComprehensiveAbstraction(true)
            .enablePerformanceOptimization(true)
            .cacheSize(20000)
            .threadPoolSize(4)
            .similarityThreshold(0.85)
            .build();
        log.info("Custom config: {}", customConfig.getSummary());
    }

    // ========================================
    // INTEGRATION CHECKLIST
    // ========================================

    /**
     * Integration checklist for migrating existing trainers
     */
    public void integrationChecklist() {
        log.info("=== Abstraction System Integration Checklist ===");
        log.info("1. ✓ Initialize AbstractionIntegrationService");
        log.info("2. ✓ Replace InfosetStoreKey.abstractInfoset() calls");
        log.info("3. ✓ Update to use ThreadSafeInfosetStore for parallel training");
        log.info("4. ✓ Add performance monitoring");
        log.info("5. ✓ Test backward compatibility");
        log.info("6. ✓ Validate thread safety");
        log.info("7. ✓ Measure performance impact");
        log.info("8. ✓ Configure abstraction quality vs speed trade-offs");
        log.info("9. ✓ Add comprehensive abstraction features (optional)");
        log.info("10. ✓ Update tests and documentation");
        log.info("=== Integration Complete ===");
    }

    /**
     * ActionBridge integration examples showing seamless conversion between systems
     */
    public void actionBridgeIntegrationExamples() {
        log.info("=== ActionBridge Integration Examples ===");

        // Example 1: Convert AbstractAction to ActionTemplate
        AbstractAction abstractAction = AbstractAction.BET_OR_RAISE_60;
        ActionTemplate template = ActionBridge.toActionTemplate(abstractAction);
        log.info("AbstractAction {} -> ActionTemplate: type={}, betRatio={}, desc='{}'",
            abstractAction, template.getAction(), template.getBetSizeRatio(), template.getDescription());

        // Example 2: Convert ActionTemplate back to AbstractAction
        AbstractAction convertedBack = ActionBridge.toAbstractAction(template);
        log.info("ActionTemplate -> AbstractAction: {}", convertedBack);

        // Example 3: Enhanced template with context
        ActionTemplate enhanced = ActionBridge.createEnhancedTemplate(
            AbstractAction.BET_OR_RAISE_100, BettingRound.FLOP, 200.0, 1000);
        log.info("Enhanced template: type={}, betRatio={}, freq={}, desc='{}'",
            enhanced.getAction(), enhanced.getBetSizeRatio(),
            enhanced.getFrequency(), enhanced.getDescription());

        // Example 4: Compatibility checking
        boolean compatible = ActionBridge.isCompatible(abstractAction, template);
        log.info("Compatibility check: {} <-> {} = {}", abstractAction, template.getAction(), compatible);

        // Example 5: Bulk conversion
        List<AbstractAction> actions = Arrays.asList(
            AbstractAction.FOLD, AbstractAction.CHECK_OR_CALL, AbstractAction.BET_OR_RAISE_30);
        List<ActionTemplate> templates = ActionBridge.toActionTemplateList(actions);
        log.info("Bulk conversion: {} actions -> {} templates", actions.size(), templates.size());

        // Example 6: Integration in trainer context
        demonstrateTrainerIntegration();

        log.info("=== ActionBridge Examples Complete ===");
    }

    /**
     * Demonstrate ActionBridge integration in trainer context
     */
    private void demonstrateTrainerIntegration() {
        log.info("--- Trainer Integration Example ---");

        // Scenario: Trainer has AbstractAction, needs ActionTemplate for abstraction
        AbstractAction trainerAction = AbstractAction.BET_OR_RAISE_200;

        // Convert to ActionTemplate for abstraction system
        ActionTemplate abstractionTemplate = ActionBridge.toActionTemplate(trainerAction);
        log.info("Trainer action {} abstracted to template: {}",
            trainerAction, abstractionTemplate.getDescription());

        // Use in abstraction system, then convert back for trainer
        AbstractAction abstractedAction = ActionBridge.toAbstractAction(abstractionTemplate);
        log.info("Abstracted action converted back for trainer: {}", abstractedAction);

        // Verify compatibility
        boolean isCompatible = ActionBridge.isCompatible(trainerAction, abstractionTemplate);
        log.info("Original and abstracted actions are compatible: {}", isCompatible);

        // This demonstrates seamless round-trip conversion maintaining compatibility
        assert trainerAction == abstractedAction || ActionBridge.isCompatible(trainerAction, abstractionTemplate);
    }
}
