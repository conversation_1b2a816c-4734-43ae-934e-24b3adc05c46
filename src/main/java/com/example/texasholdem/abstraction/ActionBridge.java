package com.example.texasholdem.abstraction;

import com.example.texasholdem.strategy.model.AbstractAction;
import com.example.texasholdem.abstraction.AbstractionDataStructures.*;
import com.example.texasholdem.model.BettingRound;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Bridge between AbstractAction (existing, proven) and ActionTemplate (new, abstraction system)
 * 
 * This class provides seamless integration between the two action representation systems:
 * - AbstractAction: Simple, proven enum used throughout existing trainers
 * - ActionTemplate: Sophisticated abstraction system for advanced features
 * 
 * Key Features:
 * - Bidirectional conversion between AbstractAction and ActionTemplate
 * - Maintains backward compatibility with existing code
 * - Enables advanced abstraction features when needed
 * - Thread-safe operation for parallel training
 * - Performance optimization with caching
 */
@Slf4j
public class ActionBridge {

    // Conversion caches for performance
    private static final Map<AbstractAction, ActionTemplate> abstractToTemplateCache = new ConcurrentHashMap<>();
    private static final Map<ActionTemplate, AbstractAction> templateToAbstractCache = new ConcurrentHashMap<>();
    
    // Default action templates for each AbstractAction
    private static final Map<AbstractAction, ActionTemplate> defaultTemplates = new ConcurrentHashMap<>();
    
    // Action type mappings
    private static final Map<AbstractAction, ActionType> actionTypeMap = new ConcurrentHashMap<>();
    private static final Map<ActionType, List<AbstractAction>> typeToActionsMap = new ConcurrentHashMap<>();
    
    static {
        initializeStaticMappings();
    }

    /**
     * Initialize static mappings between AbstractAction and ActionTemplate systems
     */
    private static void initializeStaticMappings() {
        // Map AbstractAction to ActionType
        actionTypeMap.put(AbstractAction.FOLD, ActionType.FOLD);
        actionTypeMap.put(AbstractAction.CHECK_OR_CALL, ActionType.CALL); // Simplified mapping
        actionTypeMap.put(AbstractAction.BET_OR_RAISE_30, ActionType.BET);
        actionTypeMap.put(AbstractAction.BET_OR_RAISE_60, ActionType.BET);
        actionTypeMap.put(AbstractAction.BET_OR_RAISE_100, ActionType.RAISE);
        actionTypeMap.put(AbstractAction.BET_OR_RAISE_200, ActionType.RAISE);
        actionTypeMap.put(AbstractAction.BET_OR_RAISE_500, ActionType.RAISE);
        actionTypeMap.put(AbstractAction.SMALL_BLIND, ActionType.BET);
        actionTypeMap.put(AbstractAction.BIG_BLIND, ActionType.BET);
        
        // Reverse mapping
        for (Map.Entry<AbstractAction, ActionType> entry : actionTypeMap.entrySet()) {
            typeToActionsMap.computeIfAbsent(entry.getValue(), k -> new ArrayList<>())
                .add(entry.getKey());
        }
        
        // Create default templates
        createDefaultTemplates();
        
        log.debug("ActionBridge static mappings initialized");
    }

    /**
     * Create default ActionTemplate for each AbstractAction
     */
    private static void createDefaultTemplates() {
        // FOLD
        defaultTemplates.put(AbstractAction.FOLD, ActionTemplate.builder()
            .action(ActionType.FOLD)
            .betSizeRatio(null)
            .frequency(0.1)
            .description("Fold")
            .build());
        
        // CHECK_OR_CALL
        defaultTemplates.put(AbstractAction.CHECK_OR_CALL, ActionTemplate.builder()
            .action(ActionType.CALL)
            .betSizeRatio(null)
            .frequency(0.4)
            .description("Check/Call")
            .build());
        
        // BET_OR_RAISE_30 (30% pot)
        defaultTemplates.put(AbstractAction.BET_OR_RAISE_30, ActionTemplate.builder()
            .action(ActionType.BET)
            .betSizeRatio(0.3)
            .frequency(0.2)
            .description("Bet/Raise 30% pot")
            .build());
        
        // BET_OR_RAISE_60 (60% pot)
        defaultTemplates.put(AbstractAction.BET_OR_RAISE_60, ActionTemplate.builder()
            .action(ActionType.BET)
            .betSizeRatio(0.6)
            .frequency(0.15)
            .description("Bet/Raise 60% pot")
            .build());
        
        // BET_OR_RAISE_100 (100% pot)
        defaultTemplates.put(AbstractAction.BET_OR_RAISE_100, ActionTemplate.builder()
            .action(ActionType.RAISE)
            .betSizeRatio(1.0)
            .frequency(0.1)
            .description("Bet/Raise 100% pot")
            .build());
        
        // BET_OR_RAISE_200 (200% pot)
        defaultTemplates.put(AbstractAction.BET_OR_RAISE_200, ActionTemplate.builder()
            .action(ActionType.RAISE)
            .betSizeRatio(2.0)
            .frequency(0.04)
            .description("Bet/Raise 200% pot")
            .build());
        
        // BET_OR_RAISE_500 (500% pot)
        defaultTemplates.put(AbstractAction.BET_OR_RAISE_500, ActionTemplate.builder()
            .action(ActionType.RAISE)
            .betSizeRatio(5.0)
            .frequency(0.01)
            .description("Bet/Raise 500% pot")
            .build());
        
        // SMALL_BLIND
        defaultTemplates.put(AbstractAction.SMALL_BLIND, ActionTemplate.builder()
            .action(ActionType.BET)
            .betSizeRatio(null) // Fixed amount, not pot-relative
            .frequency(0.0)
            .description("Small Blind")
            .build());
        
        // BIG_BLIND
        defaultTemplates.put(AbstractAction.BIG_BLIND, ActionTemplate.builder()
            .action(ActionType.BET)
            .betSizeRatio(null) // Fixed amount, not pot-relative
            .frequency(0.0)
            .description("Big Blind")
            .build());
    }

    // ========================================
    // CONVERSION METHODS
    // ========================================

    /**
     * Convert AbstractAction to ActionTemplate
     */
    public static ActionTemplate toActionTemplate(AbstractAction abstractAction) {
        if (abstractAction == null) {
            return null;
        }
        
        // Check cache first
        ActionTemplate cached = abstractToTemplateCache.get(abstractAction);
        if (cached != null) {
            return cached;
        }
        
        // Get default template
        ActionTemplate template = defaultTemplates.get(abstractAction);
        if (template != null) {
            // Cache the result
            abstractToTemplateCache.put(abstractAction, template);
            return template;
        }
        
        // Fallback: create basic template
        ActionTemplate fallback = ActionTemplate.builder()
            .action(actionTypeMap.getOrDefault(abstractAction, ActionType.FOLD))
            .betSizeRatio(null)
            .frequency(0.0)
            .description(abstractAction.name())
            .build();
        
        abstractToTemplateCache.put(abstractAction, fallback);
        return fallback;
    }

    /**
     * Convert ActionTemplate to closest AbstractAction
     */
    public static AbstractAction toAbstractAction(ActionTemplate template) {
        if (template == null) {
            return AbstractAction.FOLD; // Safe default
        }
        
        // Check cache first
        AbstractAction cached = templateToAbstractCache.get(template);
        if (cached != null) {
            return cached;
        }
        
        AbstractAction result = findBestAbstractAction(template);
        templateToAbstractCache.put(template, result);
        return result;
    }

    /**
     * Find best AbstractAction match for ActionTemplate
     */
    private static AbstractAction findBestAbstractAction(ActionTemplate template) {
        ActionType actionType = template.getAction();
        Double betSizeRatio = template.getBetSizeRatio();
        
        switch (actionType) {
            case FOLD:
                return AbstractAction.FOLD;
                
            case CHECK:
            case CALL:
                return AbstractAction.CHECK_OR_CALL;
                
            case BET:
            case RAISE:
                if (betSizeRatio == null) {
                    return AbstractAction.BET_OR_RAISE_30; // Default
                }
                
                // Find closest bet size
                if (betSizeRatio <= 0.45) return AbstractAction.BET_OR_RAISE_30;
                if (betSizeRatio <= 0.8) return AbstractAction.BET_OR_RAISE_60;
                if (betSizeRatio <= 1.5) return AbstractAction.BET_OR_RAISE_100;
                if (betSizeRatio <= 3.5) return AbstractAction.BET_OR_RAISE_200;
                return AbstractAction.BET_OR_RAISE_500;
                
            case ALL_IN:
                return AbstractAction.BET_OR_RAISE_500; // Closest to all-in
                
            default:
                return AbstractAction.FOLD; // Safe fallback
        }
    }

    // ========================================
    // ENHANCED INTEGRATION METHODS
    // ========================================

    /**
     * Create enhanced ActionTemplate with additional context
     */
    public static ActionTemplate createEnhancedTemplate(AbstractAction abstractAction, 
            BettingRound round, double potSize, int stackSize) {
        ActionTemplate base = toActionTemplate(abstractAction);
        
        // Enhance with context-specific information
        return ActionTemplate.builder()
            .action(base.getAction())
            .betSizeRatio(calculateContextualBetSize(abstractAction, potSize, stackSize))
            .frequency(calculateContextualFrequency(abstractAction, round))
            .description(createContextualDescription(abstractAction, round))
            .build();
    }

    /**
     * Get all AbstractActions that map to a specific ActionType
     */
    public static List<AbstractAction> getAbstractActionsForType(ActionType actionType) {
        return typeToActionsMap.getOrDefault(actionType, Collections.emptyList());
    }

    /**
     * Get ActionType for AbstractAction
     */
    public static ActionType getActionType(AbstractAction abstractAction) {
        return actionTypeMap.getOrDefault(abstractAction, ActionType.FOLD);
    }

    /**
     * Check if AbstractAction is compatible with ActionTemplate
     */
    public static boolean isCompatible(AbstractAction abstractAction, ActionTemplate template) {
        ActionType expectedType = getActionType(abstractAction);
        return expectedType == template.getAction();
    }

    /**
     * Create list of AbstractActions from ActionTemplate list
     */
    public static List<AbstractAction> toAbstractActionList(List<ActionTemplate> templates) {
        return templates.stream()
            .map(ActionBridge::toAbstractAction)
            .distinct()
            .toList();
    }

    /**
     * Create list of ActionTemplates from AbstractAction list
     */
    public static List<ActionTemplate> toActionTemplateList(List<AbstractAction> actions) {
        return actions.stream()
            .map(ActionBridge::toActionTemplate)
            .toList();
    }

    // ========================================
    // HELPER METHODS
    // ========================================

    private static Double calculateContextualBetSize(AbstractAction action, double potSize, int stackSize) {
        ActionTemplate base = defaultTemplates.get(action);
        if (base == null || base.getBetSizeRatio() == null) {
            return null;
        }
        
        // Adjust bet size based on stack size
        double maxBetRatio = stackSize / Math.max(potSize, 1.0);
        return Math.min(base.getBetSizeRatio(), maxBetRatio);
    }

    private static Double calculateContextualFrequency(AbstractAction action, BettingRound round) {
        ActionTemplate base = defaultTemplates.get(action);
        if (base == null) {
            return 0.0;
        }
        
        // Adjust frequency based on betting round
        double baseFreq = base.getFrequency();
        return switch (round) {
            case PREFLOP -> baseFreq;
            case FLOP -> baseFreq * 0.8;
            case TURN -> baseFreq * 0.6;
            case RIVER -> baseFreq * 0.4;
            default -> baseFreq;
        };
    }

    private static String createContextualDescription(AbstractAction action, BettingRound round) {
        ActionTemplate base = defaultTemplates.get(action);
        String baseDesc = base != null ? base.getDescription() : action.name();
        return String.format("%s (%s)", baseDesc, round.name());
    }

    /**
     * Clear caches (for testing or memory management)
     */
    public static void clearCaches() {
        abstractToTemplateCache.clear();
        templateToAbstractCache.clear();
        log.debug("ActionBridge caches cleared");
    }

    /**
     * Get cache statistics
     */
    public static String getCacheStatistics() {
        return String.format("ActionBridge Cache Stats: abstract->template=%d, template->abstract=%d",
            abstractToTemplateCache.size(), templateToAbstractCache.size());
    }
}
