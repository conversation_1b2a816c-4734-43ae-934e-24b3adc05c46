package com.example.texasholdem.abstraction;

import com.example.texasholdem.model.*;
import com.example.texasholdem.strategy.model.ActionTrace;
import java.util.List;

/**
 * Interface for game state representation used by abstraction engines
 * 
 * Provides a unified interface for accessing game state information
 * needed by the abstraction system without coupling to specific
 * GameService implementations.
 */
public interface SearchGameState {

    /**
     * Get current betting round
     */
    BettingRound getCurrentRound();

    /**
     * Get current player information
     */
    PlayerSnapshot getCurrentPlayer();

    /**
     * Get community cards
     */
    List<Card> getCommunityCards();

    /**
     * Get action history
     */
    List<ActionTrace> getActionHistory();

    /**
     * Player snapshot interface for abstraction
     */
    interface PlayerSnapshot {
        
        /**
         * Get player index
         */
        int getPlayerIndex();
        
        /**
         * Get player's hole cards
         */
        List<Card> getHoleCards();
        
        /**
         * Get player's chip count
         */
        int getChips();
    }
}
