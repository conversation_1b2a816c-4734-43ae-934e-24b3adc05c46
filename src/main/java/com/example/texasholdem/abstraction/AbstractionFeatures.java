package com.example.texasholdem.abstraction;

import com.example.texasholdem.model.BettingRound;
import com.example.texasholdem.abstraction.AbstractionDataStructures.*;
import lombok.Builder;
import lombok.Data;

import java.util.Objects;

/**
 * Feature representation for information set abstraction
 * <p>
 * Captures the essential characteristics of a poker information set for clustering and abstraction
 * purposes.
 */
@Data
@Builder
public class AbstractionFeatures {

  // Hand strength features
  private Double handStrength;        // Normalized hand strength [0.0, 1.0]
  private Double handPotential;       // Hand improvement potential [0.0, 1.0]
  private HandCategory handCategory;  // Categorical hand type

  // Betting history features
  private BettingPattern bettingPattern;  // Pattern of betting actions
  private Double aggressionLevel;         // Aggression level [0.0, 1.0]
  private Double potOdds;                 // Current pot odds [0.0, 1.0]

  // Positional features
  private Integer position;               // Player position (0 = button, etc.)
  private BettingRound bettingRound;      // Current betting round
  private Integer numActivePlayers;       // Number of active players

  // Strategic features
  private Double bluffFrequency;          // Historical bluff frequency [0.0, 1.0]
  private Double foldEquity;              // Estimated fold equity [0.0, 1.0]
  private Double expectedValue;           // Expected value estimate

  /**
   * Calculate feature similarity between two feature sets
   */
  public double calculateSimilarity(AbstractionFeatures other) {
    if (other == null) {
      return 0.0;
    }

    double similarity = 0.0;
    int featureCount = 0;

    // Hand strength similarity (weighted 30%)
    if (handStrength != null && other.handStrength != null) {
      similarity += 0.3 * (1.0 - Math.abs(handStrength - other.handStrength));
      featureCount++;
    }

    // Hand potential similarity (weighted 20%)
    if (handPotential != null && other.handPotential != null) {
      similarity += 0.2 * (1.0 - Math.abs(handPotential - other.handPotential));
      featureCount++;
    }

    // Hand category similarity (weighted 15%)
    if (handCategory != null && other.handCategory != null) {
      similarity += 0.15 * (handCategory == other.handCategory ? 1.0 : 0.0);
      featureCount++;
    }

    // Betting pattern similarity (weighted 15%)
    if (bettingPattern != null && other.bettingPattern != null) {
      similarity += 0.15 * calculateBettingPatternSimilarity(other.bettingPattern);
      featureCount++;
    }

    // Aggression level similarity (weighted 10%)
    if (aggressionLevel != null && other.aggressionLevel != null) {
      similarity += 0.1 * (1.0 - Math.abs(aggressionLevel - other.aggressionLevel));
      featureCount++;
    }

    // Position similarity (weighted 5%)
    if (position != null && other.position != null) {
      double positionSimilarity =
          1.0 - Math.abs(position - other.position) / 9.0; // Assuming max 9 players
      similarity += 0.05 * Math.max(0.0, positionSimilarity);
      featureCount++;
    }

    // Betting round similarity (weighted 5%)
    if (bettingRound != null && other.bettingRound != null) {
      similarity += 0.05 * (bettingRound == other.bettingRound ? 1.0 : 0.0);
      featureCount++;
    }

    return featureCount > 0 ? similarity : 0.0;
  }

  /**
   * Calculate betting pattern similarity
   */
  private double calculateBettingPatternSimilarity(BettingPattern otherPattern) {
    if (bettingPattern == otherPattern) {
      return 1.0;
    }

    // Define pattern similarity matrix
    switch (bettingPattern) {
      case PASSIVE:
        return otherPattern == BettingPattern.CONSERVATIVE ? 0.7 : 0.3;
      case CONSERVATIVE:
        return otherPattern == BettingPattern.PASSIVE ? 0.7 :
            otherPattern == BettingPattern.BALANCED ? 0.6 : 0.3;
      case BALANCED:
        return otherPattern == BettingPattern.CONSERVATIVE ? 0.6 :
            otherPattern == BettingPattern.AGGRESSIVE ? 0.6 : 0.4;
      case AGGRESSIVE:
        return otherPattern == BettingPattern.BALANCED ? 0.6 :
            otherPattern == BettingPattern.VERY_AGGRESSIVE ? 0.7 : 0.3;
      case VERY_AGGRESSIVE:
        return otherPattern == BettingPattern.AGGRESSIVE ? 0.7 : 0.2;
      default:
        return 0.0;
    }
  }

  /**
   * Get feature vector for machine learning applications
   */
  public double[] toFeatureVector() {
    return new double[]{
        handStrength != null ? handStrength : 0.0,
        handPotential != null ? handPotential : 0.0,
        handCategory != null ? handCategory.ordinal() / 10.0 : 0.0, // Normalize to [0,1]
        bettingPattern != null ? bettingPattern.ordinal() / 5.0 : 0.0, // Normalize to [0,1]
        aggressionLevel != null ? aggressionLevel : 0.0,
        potOdds != null ? potOdds : 0.0,
        position != null ? position / 9.0 : 0.0, // Normalize to [0,1]
        bettingRound != null ? bettingRound.ordinal() / 4.0 : 0.0, // Normalize to [0,1]
        numActivePlayers != null ? numActivePlayers / 10.0 : 0.0, // Normalize to [0,1]
        bluffFrequency != null ? bluffFrequency : 0.0,
        foldEquity != null ? foldEquity : 0.0,
        expectedValue != null ? Math.max(0.0, Math.min(1.0, (expectedValue + 1.0) / 2.0)) : 0.5
        // Normalize to [0,1]
    };
  }

  /**
   * Create features from feature vector
   */
  public static AbstractionFeatures fromFeatureVector(double[] vector) {
    if (vector.length < 12) {
      throw new IllegalArgumentException("Feature vector must have at least 12 elements");
    }

    return AbstractionFeatures.builder()
        .handStrength(vector[0])
        .handPotential(vector[1])
        .handCategory(HandCategory.values()[(int) (vector[2] * 10) % HandCategory.values().length])
        .bettingPattern(
            BettingPattern.values()[(int) (vector[3] * 5) % BettingPattern.values().length])
        .aggressionLevel(vector[4])
        .potOdds(vector[5])
        .position((int) (vector[6] * 9))
        .bettingRound(BettingRound.values()[(int) (vector[7] * 4) % BettingRound.values().length])
        .numActivePlayers((int) (vector[8] * 10))
        .bluffFrequency(vector[9])
        .foldEquity(vector[10])
        .expectedValue(vector[11] * 2.0 - 1.0) // Denormalize from [0,1] to [-1,1]
        .build();
  }

  /**
   * Validate feature values
   */
  public boolean isValid() {
    return (handStrength == null || (handStrength >= 0.0 && handStrength <= 1.0)) &&
        (handPotential == null || (handPotential >= 0.0 && handPotential <= 1.0)) &&
        (aggressionLevel == null || (aggressionLevel >= 0.0 && aggressionLevel <= 1.0)) &&
        (potOdds == null || (potOdds >= 0.0 && potOdds <= 1.0)) &&
        (position == null || position >= 0) &&
        (numActivePlayers == null || numActivePlayers >= 0) &&
        (bluffFrequency == null || (bluffFrequency >= 0.0 && bluffFrequency <= 1.0)) &&
        (foldEquity == null || (foldEquity >= 0.0 && foldEquity <= 1.0));
  }

  /**
   * Get feature hash for caching
   */
  public int getFeatureHash() {
    return Objects.hash(
        handStrength, handPotential, handCategory,
        bettingPattern, aggressionLevel, potOdds,
        position, bettingRound, numActivePlayers,
        bluffFrequency, foldEquity, expectedValue
    );
  }

  @Override
  public String toString() {
    return String.format(
        "Features{hand=%.2f/%.2f/%s, betting=%s/%.2f, pos=%d/%s/%d, value=%.2f}",
        handStrength != null ? handStrength : 0.0,
        handPotential != null ? handPotential : 0.0,
        handCategory,
        bettingPattern,
        aggressionLevel != null ? aggressionLevel : 0.0,
        position,
        bettingRound,
        numActivePlayers,
        expectedValue != null ? expectedValue : 0.0
    );
  }
}


