package com.example.texasholdem.abstraction;

import com.example.texasholdem.strategy.model.Infoset;
import com.example.texasholdem.strategy.model.InfosetStore;
import com.example.texasholdem.strategy.model.ThreadSafeInfosetStore;
import com.example.texasholdem.strategy.train.InfosetStoreKey;
import com.example.texasholdem.service.HandEvaluator;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.atomic.AtomicLong;

/**
 * Enhanced InfosetStoreKey that integrates with the UnifiedAbstractionPipeline
 * 
 * Provides backward compatibility with existing InfosetStoreKey while enabling
 * the new comprehensive abstraction system. Acts as a bridge between the old
 * and new abstraction approaches.
 * 
 * Key Features:
 * - Backward compatibility with existing code
 * - Seamless integration with UnifiedAbstractionPipeline
 * - Performance monitoring and optimization
 * - Thread-safe operation for parallel training
 * - Configurable abstraction quality vs performance trade-offs
 */
@Slf4j
public class EnhancedInfosetStoreKey {

    // Singleton instance for global access
    private static volatile EnhancedInfosetStoreKey instance;
    private static final Object LOCK = new Object();
    
    // Core abstraction pipeline
    private final UnifiedAbstractionPipeline abstractionPipeline;
    
    // Configuration
    private final AbstractionConfig config;
    
    // Performance monitoring
    private final AtomicLong totalAbstractionCalls;
    private final AtomicLong pipelineUsage;
    private final AtomicLong legacyUsage;
    
    // Thread-local optimization
    private final ThreadLocal<Boolean> useComprehensiveAbstraction;

    /**
     * Private constructor for singleton pattern
     */
    private EnhancedInfosetStoreKey(AbstractionConfig config, InfosetStore infosetStore,
            ThreadSafeInfosetStore threadSafeInfosetStore, HandEvaluator handEvaluator) {
        this.config = config;
        this.abstractionPipeline = new UnifiedAbstractionPipeline(config, infosetStore, 
            threadSafeInfosetStore, handEvaluator);
        
        // Initialize performance monitoring
        this.totalAbstractionCalls = new AtomicLong(0);
        this.pipelineUsage = new AtomicLong(0);
        this.legacyUsage = new AtomicLong(0);
        
        // Initialize thread-local optimization
        this.useComprehensiveAbstraction = ThreadLocal.withInitial(() -> 
            config.isEnableComprehensiveAbstraction());
        
        log.info("EnhancedInfosetStoreKey initialized with comprehensive abstraction: {}", 
            config.isEnableComprehensiveAbstraction());
    }

    /**
     * Get singleton instance
     */
    public static EnhancedInfosetStoreKey getInstance(AbstractionConfig config, InfosetStore infosetStore,
            ThreadSafeInfosetStore threadSafeInfosetStore, HandEvaluator handEvaluator) {
        if (instance == null) {
            synchronized (LOCK) {
                if (instance == null) {
                    instance = new EnhancedInfosetStoreKey(config, infosetStore, 
                        threadSafeInfosetStore, handEvaluator);
                }
            }
        }
        return instance;
    }

    /**
     * Get singleton instance with default configuration
     */
    public static EnhancedInfosetStoreKey getInstance() {
        if (instance == null) {
            throw new IllegalStateException("EnhancedInfosetStoreKey not initialized. " +
                "Call getInstance(config, stores, evaluator) first.");
        }
        return instance;
    }

    /**
     * Main abstraction method - enhanced version of InfosetStoreKey.abstractInfoset()
     * 
     * This method provides the same interface as the original but with enhanced capabilities
     */
    public int abstractInfoset(Infoset infoset) {
        totalAbstractionCalls.incrementAndGet();
        
        try {
            if (useComprehensiveAbstraction.get() && config.isEnableComprehensiveAbstraction()) {
                pipelineUsage.incrementAndGet();
                return abstractionPipeline.abstractInfoset(infoset);
            } else {
                legacyUsage.incrementAndGet();
                return InfosetStoreKey.abstractInfoset(infoset);
            }
        } catch (Exception e) {
            log.warn("Error in enhanced abstraction, falling back to legacy: {}", e.getMessage());
            legacyUsage.incrementAndGet();
            return InfosetStoreKey.abstractInfoset(infoset);
        }
    }

    /**
     * Static method for backward compatibility
     */
    public static int abstractInfosetStatic(Infoset infoset) {
        if (instance != null) {
            return instance.abstractInfoset(infoset);
        } else {
            // Fallback to original implementation if not initialized
            return InfosetStoreKey.abstractInfoset(infoset);
        }
    }

    /**
     * Enable comprehensive abstraction for current thread
     */
    public void enableComprehensiveAbstraction() {
        useComprehensiveAbstraction.set(true);
        log.debug("Enabled comprehensive abstraction for thread: {}", Thread.currentThread().getName());
    }

    /**
     * Disable comprehensive abstraction for current thread (use legacy)
     */
    public void disableComprehensiveAbstraction() {
        useComprehensiveAbstraction.set(false);
        log.debug("Disabled comprehensive abstraction for thread: {}", Thread.currentThread().getName());
    }

    /**
     * Check if comprehensive abstraction is enabled for current thread
     */
    public boolean isComprehensiveAbstractionEnabled() {
        return useComprehensiveAbstraction.get();
    }

    /**
     * Get the underlying abstraction pipeline
     */
    public UnifiedAbstractionPipeline getAbstractionPipeline() {
        return abstractionPipeline;
    }

    /**
     * Get performance statistics
     */
    public String getPerformanceStatistics() {
        long total = totalAbstractionCalls.get();
        long pipeline = pipelineUsage.get();
        long legacy = legacyUsage.get();
        
        double pipelinePercentage = total > 0 ? (double) pipeline / total * 100 : 0;
        double legacyPercentage = total > 0 ? (double) legacy / total * 100 : 0;
        
        return String.format(
            "EnhancedInfosetStoreKey Stats: total=%d, pipeline=%d (%.1f%%), legacy=%d (%.1f%%), %s",
            total, pipeline, pipelinePercentage, legacy, legacyPercentage,
            abstractionPipeline.getCacheStatistics()
        );
    }

    /**
     * Get detailed performance metrics
     */
    public java.util.Map<String, Object> getDetailedStatistics() {
        java.util.Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("totalAbstractionCalls", totalAbstractionCalls.get());
        stats.put("pipelineUsage", pipelineUsage.get());
        stats.put("legacyUsage", legacyUsage.get());
        stats.put("comprehensiveEnabled", config.isEnableComprehensiveAbstraction());
        stats.put("legacyFallbackEnabled", config.isEnableLegacyFallback());
        stats.put("performanceOptimizationEnabled", config.isEnablePerformanceOptimization());
        
        // Add pipeline statistics
        stats.putAll(abstractionPipeline.getPerformanceStatistics());
        
        return stats;
    }

    /**
     * Clear all caches and reset statistics
     */
    public void reset() {
        abstractionPipeline.clearCaches();
        totalAbstractionCalls.set(0);
        pipelineUsage.set(0);
        legacyUsage.set(0);
        log.info("EnhancedInfosetStoreKey reset completed");
    }

    /**
     * Cleanup resources
     */
    public void cleanup() {
        abstractionPipeline.cleanup();
        useComprehensiveAbstraction.remove();
        log.info("EnhancedInfosetStoreKey cleanup completed");
    }

    /**
     * Initialize global instance for static access
     */
    public static void initialize(AbstractionConfig config, InfosetStore infosetStore,
            ThreadSafeInfosetStore threadSafeInfosetStore, HandEvaluator handEvaluator) {
        getInstance(config, infosetStore, threadSafeInfosetStore, handEvaluator);
        log.info("EnhancedInfosetStoreKey globally initialized");
    }

    /**
     * Check if global instance is initialized
     */
    public static boolean isInitialized() {
        return instance != null;
    }
}
