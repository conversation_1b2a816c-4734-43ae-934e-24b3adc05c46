package com.example.texasholdem.abstraction;

import com.example.texasholdem.abstraction.AbstractionDataStructures.ActionTemplate;
import com.example.texasholdem.abstraction.AbstractionDataStructures.HandBucket;
import com.example.texasholdem.model.*;
import com.example.texasholdem.service.GameService;
import com.example.texasholdem.service.HandEvaluator;
import com.example.texasholdem.strategy.model.*;
import com.example.texasholdem.strategy.train.InfosetStoreKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.List;
import java.util.Map;

/**
 * Integration service for the unified abstraction system
 * 
 * Provides seamless integration between the new abstraction pipeline and existing
 * MCCFR training infrastructure. Handles initialization, configuration, and
 * provides drop-in replacement methods for existing abstraction calls.
 * 
 * Key Features:
 * - Automatic initialization and configuration
 * - Backward compatibility with existing code
 * - Performance monitoring and optimization
 * - Thread-safe operation for parallel training
 * - Spring integration for dependency injection
 */
@Service
@Slf4j
public class AbstractionIntegrationService {

    // Core components
    private InfosetStore infosetStore;
    private ThreadSafeInfosetStore threadSafeInfosetStore;
    private HandEvaluator handEvaluator;
    
    // Abstraction system
    private EnhancedInfosetStoreKey enhancedKey;
    private UnifiedAbstractionPipeline pipeline;
    private AbstractionConfig config;
    
    // Configuration flags
    private boolean initialized = false;
    private boolean enableEnhancedAbstraction = true;

    /**
     * Initialize the abstraction integration service
     */
    @PostConstruct
    public void initialize() {
        if (initialized) {
            return;
        }
        
        try {
            log.info("Initializing AbstractionIntegrationService...");
            
            // Create default configuration
            this.config = createOptimalConfiguration();
            
            // Initialize enhanced abstraction system
            initializeAbstractionSystem();
            
            // Set up integration hooks
            setupIntegrationHooks();
            
            this.initialized = true;
            log.info("AbstractionIntegrationService initialized successfully");
            
        } catch (Exception e) {
            log.error("Failed to initialize AbstractionIntegrationService", e);
            this.enableEnhancedAbstraction = false;
        }
    }

    /**
     * Initialize with custom configuration
     */
    public void initialize(AbstractionConfig customConfig, InfosetStore infosetStore,
            ThreadSafeInfosetStore threadSafeInfosetStore, HandEvaluator handEvaluator) {
        this.config = customConfig;
        this.infosetStore = infosetStore;
        this.threadSafeInfosetStore = threadSafeInfosetStore;
        this.handEvaluator = handEvaluator;
        
        initialize();
    }

    /**
     * Create optimal configuration for the current environment
     */
    private AbstractionConfig createOptimalConfiguration() {
        // Determine optimal configuration based on available resources
        int availableProcessors = Runtime.getRuntime().availableProcessors();
        long availableMemory = Runtime.getRuntime().maxMemory();
        
        if (availableMemory > 8L * 1024 * 1024 * 1024) { // > 8GB
            return AbstractionConfig.builder()
                .threadPoolSize(Math.min(availableProcessors, 8))
                .cacheSize(50000)
                .enableComprehensiveAbstraction(true)
                .enablePerformanceOptimization(true)
                .enableParallelProcessing(true)
                .enableDynamicRefinement(true)
                .build();
        } else if (availableMemory > 4L * 1024 * 1024 * 1024) { // > 4GB
            return AbstractionConfig.builder()
                .threadPoolSize(Math.min(availableProcessors, 4))
                .cacheSize(20000)
                .enableComprehensiveAbstraction(true)
                .enablePerformanceOptimization(true)
                .enableParallelProcessing(true)
                .enableDynamicRefinement(false)
                .build();
        } else {
            return AbstractionConfig.builder()
                .threadPoolSize(Math.min(availableProcessors, 2))
                .cacheSize(5000)
                .enableComprehensiveAbstraction(false)
                .enableLegacyFallback(true)
                .enableParallelProcessing(false)
                .enableDynamicRefinement(false)
                .build();
        }
    }

    /**
     * Initialize the abstraction system components
     */
    private void initializeAbstractionSystem() {
        // Create stores if not provided
        if (infosetStore == null) {
            infosetStore = new InfosetStore(6, 6); // Default 6 players, 6 actions
        }
        
        if (threadSafeInfosetStore == null) {
            threadSafeInfosetStore = new ThreadSafeInfosetStore(6, 6);
        }
        
        // Initialize enhanced key
        this.enhancedKey = EnhancedInfosetStoreKey.getInstance(config, infosetStore, 
            threadSafeInfosetStore, handEvaluator);
        
        // Get pipeline reference
        this.pipeline = enhancedKey.getAbstractionPipeline();
        
        log.info("Abstraction system initialized with config: {}", config.getSummary());
    }

    /**
     * Set up integration hooks for seamless operation
     */
    private void setupIntegrationHooks() {
        // Initialize global enhanced key for static access
        EnhancedInfosetStoreKey.initialize(config, infosetStore, threadSafeInfosetStore, handEvaluator);
        
        log.debug("Integration hooks set up successfully");
    }

    // ========================================
    // PUBLIC API METHODS
    // ========================================

    /**
     * Enhanced abstraction method - drop-in replacement for InfosetStoreKey.abstractInfoset()
     */
    public int abstractInfoset(Infoset infoset) {
        if (!initialized || !enableEnhancedAbstraction) {
            return InfosetStoreKey.abstractInfoset(infoset);
        }
        
        return enhancedKey.abstractInfoset(infoset);
    }

    /**
     * Get action abstraction for game state
     */
    public ActionTemplate getActionAbstraction(Player player, GameService gameService,
            List<ActionTrace> history) {
        if (!initialized || !enableEnhancedAbstraction) {
            return null;
        }
        
        return pipeline.getActionAbstraction(player, gameService, history);
    }

    /**
     * Get card abstraction for hand
     */
    public HandBucket getCardAbstraction(List<Card> holeCards, List<Card> communityCards,
            BettingRound round) {
        if (!initialized || !enableEnhancedAbstraction) {
            return null;
        }
        
        return pipeline.getCardAbstraction(holeCards, communityCards, round);
    }

    /**
     * Get comprehensive abstraction for complete game state
     */
    public ComprehensiveAbstraction getComprehensiveAbstraction(Player player, GameService gameService,
            List<ActionTrace> history) {
        if (!initialized || !enableEnhancedAbstraction) {
            return null;
        }
        
        return pipeline.getComprehensiveAbstraction(player, gameService, history);
    }

    /**
     * Enable enhanced abstraction for current thread
     */
    public void enableEnhancedAbstraction() {
        if (initialized && enhancedKey != null) {
            enhancedKey.enableComprehensiveAbstraction();
        }
    }

    /**
     * Disable enhanced abstraction for current thread
     */
    public void disableEnhancedAbstraction() {
        if (initialized && enhancedKey != null) {
            enhancedKey.disableComprehensiveAbstraction();
        }
    }

    /**
     * Check if enhanced abstraction is available and enabled
     */
    public boolean isEnhancedAbstractionEnabled() {
        return initialized && enableEnhancedAbstraction && 
               enhancedKey != null && enhancedKey.isComprehensiveAbstractionEnabled();
    }

    /**
     * Get performance statistics
     */
    public String getPerformanceStatistics() {
        if (!initialized || enhancedKey == null) {
            return "AbstractionIntegrationService not initialized";
        }
        
        return enhancedKey.getPerformanceStatistics();
    }

    /**
     * Get detailed performance metrics
     */
    public Map<String, Object> getDetailedStatistics() {
        if (!initialized || enhancedKey == null) {
            return Map.of("status", "not_initialized");
        }
        
        Map<String, Object> stats = enhancedKey.getDetailedStatistics();
        stats.put("integrationServiceInitialized", initialized);
        stats.put("enhancedAbstractionEnabled", enableEnhancedAbstraction);
        return stats;
    }

    /**
     * Reset all caches and statistics
     */
    public void reset() {
        if (initialized && enhancedKey != null) {
            enhancedKey.reset();
        }
    }

    /**
     * Get current configuration
     */
    public AbstractionConfig getConfiguration() {
        return config;
    }

    /**
     * Update configuration (requires reinitialization)
     */
    public void updateConfiguration(AbstractionConfig newConfig) {
        this.config = newConfig;
        this.initialized = false;
        initialize();
    }

    // ========================================
    // STATIC UTILITY METHODS
    // ========================================

    /**
     * Static method for backward compatibility
     */
    public static int abstractInfosetStatic(Infoset infoset) {
        if (EnhancedInfosetStoreKey.isInitialized()) {
            return EnhancedInfosetStoreKey.abstractInfosetStatic(infoset);
        } else {
            return InfosetStoreKey.abstractInfoset(infoset);
        }
    }

    // ========================================
    // LIFECYCLE METHODS
    // ========================================

    /**
     * Cleanup resources
     */
    @PreDestroy
    public void cleanup() {
        if (initialized && enhancedKey != null) {
            enhancedKey.cleanup();
        }
        
        if (pipeline != null) {
            pipeline.cleanup();
        }
        
        this.initialized = false;
        log.info("AbstractionIntegrationService cleanup completed");
    }
}
