package com.example.texasholdem.abstraction;

import com.example.texasholdem.model.*;
import com.example.texasholdem.service.HandEvaluator;
import com.example.texasholdem.abstraction.AbstractionDataStructures.*;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Card Abstraction Engine for Pluribus Poker AI
 * <p>
 * Reduces information set space by grouping similar hands based on: - Hand strength relative to all
 * possible hands - Hand potential for improvement - Strategic similarity in different game
 * contexts
 * <p>
 * Integrates with existing HandEvaluator framework and maintains strategic distinctions while
 * reducing complexity.
 */
@Slf4j
public class CardAbstractionEngine {

  private final AbstractionConfig config;

  // Hand buckets organized by betting round
  private final Map<BettingRound, List<HandBucket>> handBuckets;

  // Cache for hand strength calculations
  private final Map<String, HandStrengthFeatures> strengthCache;

  // Performance monitoring
  private final Map<String, Long> performanceCounters;

  // Hand examples for bucket validation
  private final Map<Integer, List<HandExample>> bucketExamples;

  public CardAbstractionEngine(AbstractionConfig config, HandEvaluator handEvaluator) {
    this.config = config != null ? config : AbstractionConfig.defaultConfig();
    // HandEvaluator is used as static methods, so we don't store the instance
    this.handBuckets = new ConcurrentHashMap<>();
    this.strengthCache = new ConcurrentHashMap<>();
    this.performanceCounters = new ConcurrentHashMap<>();
    this.bucketExamples = new ConcurrentHashMap<>();

    initializeHandBuckets();

    log.info("Card Abstraction Engine initialized with {} strength buckets, {} potential buckets",
        config.getHandStrengthBuckets(), config.getPotentialBuckets());
  }

  /**
   * Initialize hand buckets for each betting round
   */
  private void initializeHandBuckets() {
    for (BettingRound round : BettingRound.values()) {
      List<HandBucket> buckets = createHandBucketsForRound(round);
      handBuckets.put(round, buckets);

      log.debug("Initialized {} hand buckets for {}", buckets.size(), round);
    }
  }

  /**
   * Create hand buckets for a specific betting round
   */
  private List<HandBucket> createHandBucketsForRound(BettingRound round) {
    List<HandBucket> buckets = new ArrayList<>();

    int strengthBuckets = getStrengthBucketsForRound(round);
    int potentialBuckets = getPotentialBucketsForRound(round);

    double strengthStep = 1.0 / strengthBuckets;
    double potentialStep = 1.0 / potentialBuckets;

    int bucketId = 0;

    for (int s = 0; s < strengthBuckets; s++) {
      for (int p = 0; p < potentialBuckets; p++) {
        double minStrength = s * strengthStep;
        double maxStrength = (s + 1) * strengthStep;
        double minPotential = p * potentialStep;
        double maxPotential = (p + 1) * potentialStep;

        String bucketName = String.format("%s_S%d_P%d", round, s, p);

        HandBucket bucket = HandBucket.builder().bucketId(bucketId++).bucketName(bucketName)
            .minStrength(minStrength).maxStrength(maxStrength).minPotential(minPotential)
            .maxPotential(maxPotential).examples(new ArrayList<>()).sampleCount(0).build();

        buckets.add(bucket);
      }
    }

    return buckets;
  }

  /**
   * Get number of strength buckets for a betting round
   */
  private int getStrengthBucketsForRound(BettingRound round) {
    switch (round) {
      case PREFLOP:
        return Math.max(10, config.getHandStrengthBuckets() / 5); // Fewer buckets preflop
      case FLOP:
        return Math.max(15, config.getHandStrengthBuckets() / 3); // More granularity on flop
      case TURN:
      case RIVER:
        return config.getHandStrengthBuckets(); // Full granularity postflop
      default:
        return config.getHandStrengthBuckets();
    }
  }

  /**
   * Get number of potential buckets for a betting round
   */
  private int getPotentialBucketsForRound(BettingRound round) {
    switch (round) {
      case PREFLOP:
        return config.getPotentialBuckets(); // High potential variance preflop
      case FLOP:
        return Math.max(10, config.getPotentialBuckets() / 2); // Moderate potential on flop
      case TURN:
        return Math.max(5, config.getPotentialBuckets() / 5); // Lower potential on turn
      case RIVER:
        return 1; // No potential on river
      default:
        return config.getPotentialBuckets();
    }
  }

  /**
   * Abstract a hand to its bucket representation
   */
  public HandBucket abstractHand(List<Card> holeCards, List<Card> communityCards,
      BettingRound round) {
    long startTime = System.nanoTime();

    // Validate input parameters
    if (holeCards == null || communityCards == null || round == null) {
      log.warn("Null parameters passed to abstractHand: holeCards={}, communityCards={}, round={}",
          holeCards, communityCards, round);
      incrementCounter("null_parameter_errors");
      return getDefaultBucket(round != null ? round : BettingRound.PREFLOP);
    }

    try {
      // Calculate hand strength features
      HandStrengthFeatures features = calculateHandStrengthFeatures(holeCards, communityCards,
          round);

      // Find appropriate bucket
      HandBucket bucket = findHandBucket(features, round);

      if (bucket != null) {
        // Update bucket with this hand example
        updateBucketWithExample(bucket, holeCards, communityCards, features);
        incrementCounter("successful_abstractions");
      } else {
        log.warn("No suitable bucket found for hand: {} with features: {}",
            formatHand(holeCards, communityCards), features);
        incrementCounter("failed_abstractions");
      }

      return bucket;

    } catch (Exception e) {
      log.error("Error abstracting hand {}: {}", formatHand(holeCards, communityCards),
          e.getMessage(), e);
      incrementCounter("errors");
      return getDefaultBucket(round);
    } finally {
      long elapsedTime = System.nanoTime() - startTime;
      incrementCounter("total_time", elapsedTime);
      incrementCounter("abstractions_created");
    }
  }

  /**
   * Calculate hand strength features
   */
  private HandStrengthFeatures calculateHandStrengthFeatures(List<Card> holeCards,
      List<Card> communityCards, BettingRound round) {
    // Validate input parameters
    if (holeCards == null || communityCards == null) {
      log.warn(
          "Null parameters passed to calculateHandStrengthFeatures: holeCards={}, communityCards={}",
          holeCards, communityCards);
      return getDefaultHandStrengthFeatures();
    }

    String handKey = createHandKey(holeCards, communityCards);

    // Check cache first
    HandStrengthFeatures cached = strengthCache.get(handKey);
    if (cached != null) {
      incrementCounter("cache_hits");
      return cached;
    }

    incrementCounter("cache_misses");

    // Calculate hand strength using existing HandEvaluator
    List<Card> allCards = new ArrayList<>(holeCards);
    allCards.addAll(communityCards);

    double strength = calculateHandStrength(allCards);
    double potential = calculateHandPotential(holeCards, communityCards, round);
    HandCategory category = determineHandCategory(allCards);
    int rank = calculateHandRank(allCards);
    double equity = calculateEquity(holeCards, communityCards);

    HandStrengthFeatures features = HandStrengthFeatures.builder().strength(strength)
        .potential(potential).category(category).rank(rank).equity(equity).build();

    // Cache the result
    if (strengthCache.size() < config.getCacheSize()) {
      strengthCache.put(handKey, features);
    }

    return features;
  }

  /**
   * Calculate normalized hand strength [0.0, 1.0]
   */
  private double calculateHandStrength(List<Card> allCards) {
    if (allCards == null || allCards.size() < 5) {
      return 0.0; // Cannot evaluate with null or less than 5 cards
    }

    try {
      // Use existing HandEvaluator to get hand strength
      short handValue;
      if (allCards.size() == 5) {
        handValue = HandEvaluator.evaluateHand(allCards);
      } else if (allCards.size() == 7) {
        handValue = HandEvaluator.evaluateBestOf(allCards);
      } else {
        // For other sizes, take best 5 cards
        List<Card> best5 = allCards.subList(0, Math.min(5, allCards.size()));
        if (best5.size() == 5) {
          handValue = HandEvaluator.evaluateHand(best5);
        } else {
          return 0.5; // Default for incomplete hands
        }
      }

      // Normalize to [0.0, 1.0] range
      // HandEvaluator returns lower values for better hands, so invert
      return Math.min(1.0, Math.max(0.0, (10000.0 - handValue) / 10000.0));

    } catch (Exception e) {
      log.warn("Error evaluating hand strength for {}: {}", allCards, e.getMessage());
      return 0.5; // Default to middle strength
    }
  }

  /**
   * Calculate hand potential for improvement
   */
  private double calculateHandPotential(List<Card> holeCards, List<Card> communityCards,
      BettingRound round) {
    if (round == BettingRound.RIVER) {
      return 0.0; // No potential on river
    }

    // Simplified potential calculation
    // In production, this would use Monte Carlo simulation or lookup tables
    // to calculate the probability of improving to a better hand

    int cardsToSee = getCardsToSee(round);
    if (cardsToSee == 0) {
      return 0.0;
    }

    // Basic potential based on hand category and outs
    List<Card> allCards = new ArrayList<>(holeCards);
    allCards.addAll(communityCards);

    HandCategory currentCategory = determineHandCategory(allCards);
    int outs = estimateOuts(holeCards, communityCards, currentCategory);

    // Calculate potential based on outs and cards to see
    double potential = Math.min(1.0, (outs * cardsToSee) / 47.0); // 47 unknown cards

    return potential;
  }

  /**
   * Get number of cards still to be seen
   */
  private int getCardsToSee(BettingRound round) {
    switch (round) {
      case PREFLOP:
        return 5;
      case FLOP:
        return 2;
      case TURN:
        return 1;
      case RIVER:
        return 0;
      default:
        return 0;
    }
  }

  /**
   * Estimate number of outs for hand improvement
   */
  private int estimateOuts(List<Card> holeCards, List<Card> communityCards,
      HandCategory currentCategory) {
    // Simplified outs calculation
    // In production, this would be much more sophisticated

    switch (currentCategory) {
      case HIGH_CARD:
        return 6; // Pair outs
      case PAIR:
        return 5; // Two pair or trips outs
      case TWO_PAIR:
        return 4; // Full house outs
      case THREE_OF_A_KIND:
        return 7; // Full house or quads outs
      case STRAIGHT:
      case FLUSH:
        return 2; // Improvement outs
      default:
        return 0; // Already strong hand
    }
  }

  /**
   * Determine hand category
   */
  private HandCategory determineHandCategory(List<Card> allCards) {
    if (allCards == null || allCards.size() < 5) {
      return HandCategory.HIGH_CARD;
    }

    // Use existing HandEvaluator to determine hand type
    // This is a simplified implementation
    try {
      short handValue;
      if (allCards.size() == 5) {
        handValue = HandEvaluator.evaluateHand(allCards);
      } else if (allCards.size() == 7) {
        handValue = HandEvaluator.evaluateBestOf(allCards);
      } else {
        // For other sizes, take best 5 cards
        List<Card> best5 = allCards.subList(0, Math.min(5, allCards.size()));
        if (best5.size() == 5) {
          handValue = HandEvaluator.evaluateHand(best5);
        } else {
          return HandCategory.HIGH_CARD;
        }
      }

      // Map hand evaluator result to hand category
      // HandEvaluator returns lower values for better hands
      if (handValue <= 10) {
        return HandCategory.ROYAL_FLUSH;
      }
      if (handValue <= 40) {
        return HandCategory.STRAIGHT_FLUSH;
      }
      if (handValue <= 166) {
        return HandCategory.FOUR_OF_A_KIND;
      }
      if (handValue <= 322) {
        return HandCategory.FULL_HOUSE;
      }
      if (handValue <= 1599) {
        return HandCategory.FLUSH;
      }
      if (handValue <= 1609) {
        return HandCategory.STRAIGHT;
      }
      if (handValue <= 2467) {
        return HandCategory.THREE_OF_A_KIND;
      }
      if (handValue <= 3325) {
        return HandCategory.TWO_PAIR;
      }
      if (handValue <= 6185) {
        return HandCategory.PAIR;
      }
      return HandCategory.HIGH_CARD;

    } catch (Exception e) {
      log.warn("Error determining hand category for {}: {}", allCards, e.getMessage());
      return HandCategory.HIGH_CARD;
    }
  }

  /**
   * Calculate hand rank among all possible hands
   */
  private int calculateHandRank(List<Card> allCards) {
    // Simplified ranking - in production this would use precomputed tables
    if (allCards == null) {
      return 0;
    }
    try {
      if (allCards.size() == 5) {
        return HandEvaluator.evaluateHand(allCards);
      } else if (allCards.size() == 7) {
        return HandEvaluator.evaluateBestOf(allCards);
      } else {
        List<Card> best5 = allCards.subList(0, Math.min(5, allCards.size()));
        if (best5.size() == 5) {
          return HandEvaluator.evaluateHand(best5);
        }
      }
      return 0;
    } catch (Exception e) {
      return 0;
    }
  }

  /**
   * Calculate equity against random hands
   */
  private double calculateEquity(List<Card> holeCards, List<Card> communityCards) {
    // Simplified equity calculation
    // In production, this would use Monte Carlo simulation or lookup tables

    List<Card> allCards = new ArrayList<>(holeCards);
    allCards.addAll(communityCards);

    double strength = calculateHandStrength(allCards);

    // Basic equity approximation based on hand strength
    return Math.min(1.0, Math.max(0.0, strength * 0.8 + 0.1));
  }

  /**
   * Find appropriate hand bucket for features
   */
  private HandBucket findHandBucket(HandStrengthFeatures features, BettingRound round) {
    List<HandBucket> buckets = handBuckets.get(round);
    if (buckets == null) {
      return null;
    }

    return buckets.stream()
        .filter(bucket -> bucket.contains(features.getStrength(), features.getPotential()))
        .findFirst().orElse(null);
  }

  /**
   * Update bucket with hand example
   */
  private void updateBucketWithExample(HandBucket bucket, List<Card> holeCards,
      List<Card> communityCards, HandStrengthFeatures features) {
    HandExample example = HandExample.builder().holeCards(new ArrayList<>(holeCards))
        .communityCards(new ArrayList<>(communityCards)).strength(features.getStrength())
        .potential(features.getPotential()).category(features.getCategory())
        .description(formatHand(holeCards, communityCards)).build();

    bucket.addRepresentative(example);
    bucket.setSampleCount(bucket.getSampleCount() != null ? bucket.getSampleCount() + 1 : 1);

    // Update bucket examples for validation
    bucketExamples.computeIfAbsent(bucket.getBucketId(), k -> new ArrayList<>()).add(example);
  }

  /**
   * Get default hand strength features for error cases
   */
  private HandStrengthFeatures getDefaultHandStrengthFeatures() {
    return HandStrengthFeatures.builder().strength(0.5).potential(0.0)
        .category(HandCategory.HIGH_CARD).rank(7462) // Worst possible hand rank
        .equity(0.5).build();
  }

  /**
   * Get default bucket for error cases
   */
  private HandBucket getDefaultBucket(BettingRound round) {
    List<HandBucket> buckets = handBuckets.get(round);
    if (buckets != null && !buckets.isEmpty()) {
      return buckets.get(buckets.size() / 2); // Return middle bucket
    }

    return HandBucket.builder().bucketId(-1).bucketName("DEFAULT").minStrength(0.4).maxStrength(0.6)
        .minPotential(0.0).maxPotential(1.0).examples(new ArrayList<>()).sampleCount(0).build();
  }

  /**
   * Create hand key for caching
   */
  private String createHandKey(List<Card> holeCards, List<Card> communityCards) {
    if (holeCards == null || communityCards == null) {
      return "null_hand";
    }

    StringBuilder sb = new StringBuilder();

    // Sort cards for consistent key generation
    List<Card> allCards = new ArrayList<>(holeCards);
    allCards.addAll(communityCards);
    allCards.sort((c1, c2) -> c1.toString().compareTo(c2.toString()));

    for (Card card : allCards) {
      sb.append(card.toString());
    }

    return sb.toString();
  }

  /**
   * Format hand for display
   */
  private String formatHand(List<Card> holeCards, List<Card> communityCards) {
    StringBuilder sb = new StringBuilder();

    for (Card card : holeCards) {
      sb.append(card.toString());
    }

    if (!communityCards.isEmpty()) {
      sb.append(" | ");
      for (Card card : communityCards) {
        sb.append(card.toString());
      }
    }

    return sb.toString();
  }

  /**
   * Get card abstraction statistics
   */
  public CardAbstractionStatistics getStatistics() {
    Map<BettingRound, Integer> bucketCounts = new HashMap<>();
    int totalBuckets = 0;
    int totalExamples = 0;

    for (Map.Entry<BettingRound, List<HandBucket>> entry : handBuckets.entrySet()) {
      int count = entry.getValue().size();
      bucketCounts.put(entry.getKey(), count);
      totalBuckets += count;

      totalExamples += entry.getValue().stream()
          .mapToInt(bucket -> bucket.getSampleCount() != null ? bucket.getSampleCount() : 0).sum();
    }

    return CardAbstractionStatistics.builder().totalBuckets(totalBuckets)
        .bucketsByRound(bucketCounts).totalExamples(totalExamples).cacheSize(strengthCache.size())
        .cacheHitRate(calculateCacheHitRate())
        .performanceCounters(new HashMap<>(performanceCounters)).build();
  }

  /**
   * Calculate cache hit rate
   */
  private double calculateCacheHitRate() {
    long hits = performanceCounters.getOrDefault("cache_hits", 0L);
    long misses = performanceCounters.getOrDefault("cache_misses", 0L);
    long total = hits + misses;

    return total > 0 ? (double) hits / total : 0.0;
  }

  /**
   * Increment performance counter
   */
  private void incrementCounter(String counterName) {
    incrementCounter(counterName, 1L);
  }

  /**
   * Increment performance counter by value
   */
  private void incrementCounter(String counterName, long value) {
    performanceCounters.merge(counterName, value, Long::sum);
  }
}

/**
 * Card abstraction statistics
 */
@Data
@Builder
class CardAbstractionStatistics {

  private int totalBuckets;
  private Map<BettingRound, Integer> bucketsByRound;
  private int totalExamples;
  private int cacheSize;
  private double cacheHitRate;
  private Map<String, Long> performanceCounters;

  public String getSummary() {
    return String.format("Hand Buckets: %d total, Examples: %d, Cache: %d (%.1f%% hit rate)",
        totalBuckets, totalExamples, cacheSize, cacheHitRate * 100);
  }
}
