package com.example.texasholdem.abstraction;

import com.example.texasholdem.model.*;
import com.example.texasholdem.strategy.model.ActionTrace;
import com.example.texasholdem.strategy.model.InfosetStore;
import com.example.texasholdem.strategy.model.InfosetValue;
import com.example.texasholdem.abstraction.AbstractionDataStructures.*;
import com.example.texasholdem.abstraction.AbstractionFeatures;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Information Abstraction Engine for Pluribus Poker AI
 * <p>
 * Reduces game tree complexity by grouping similar information sets based on: - Hand strength and
 * potential - Betting history patterns - Position and game context - Strategic similarity metrics
 * <p>
 * Integrates with existing InfosetStore architecture and maintains compatibility with serialization
 * and caching systems.
 */
@Slf4j
public class InformationAbstractionEngine {

  private final int numPlayers;
  private final int numActions;

  // Abstraction configuration
  private final AbstractionConfig config;

  // Abstraction mappings (thread-safe for concurrent access)
  private final Map<Long, Integer> infosetToAbstraction;
  private final Map<Integer, Set<Long>> abstractionToInfosets;

  // Quality metrics
  private final AtomicLong totalAbstractions;
  private final AtomicLong totalInfosets;
  private final Map<Integer, AbstractionQualityMetrics> qualityMetrics;

  // Performance monitoring
  private final Map<String, Long> performanceCounters;

  public InformationAbstractionEngine(int numPlayers, int numActions, AbstractionConfig config) {
    this.numPlayers = numPlayers;
    this.numActions = numActions;
    this.config = config != null ? config : AbstractionConfig.defaultConfig();

    this.infosetToAbstraction = new ConcurrentHashMap<>();
    this.abstractionToInfosets = new ConcurrentHashMap<>();
    this.totalAbstractions = new AtomicLong(0);
    this.totalInfosets = new AtomicLong(0);
    this.qualityMetrics = new ConcurrentHashMap<>();
    this.performanceCounters = new ConcurrentHashMap<>();

    log.info("Information Abstraction Engine initialized: {} players, {} actions, config: {}",
        numPlayers, numActions, config);
  }

  /**
   * Create abstraction for an information set based on game context
   */
  public int createAbstraction(long infosetKey, GameContext context) {
    long startTime = System.nanoTime();

    try {
      // Check if abstraction already exists
      Integer existingAbstraction = infosetToAbstraction.get(infosetKey);
      if (existingAbstraction != null) {
        incrementCounter("cache_hits");
        return existingAbstraction;
      }

      // Calculate abstraction features
      AbstractionFeatures features = extractFeatures(infosetKey, context);

      // Find similar existing abstraction or create new one
      int abstractionId = findOrCreateAbstraction(features, infosetKey);

      // Update mappings
      infosetToAbstraction.put(infosetKey, abstractionId);
      abstractionToInfosets.computeIfAbsent(abstractionId, k -> ConcurrentHashMap.newKeySet())
          .add(infosetKey);

      totalInfosets.incrementAndGet();
      incrementCounter("abstractions_created");

      long elapsedTime = System.nanoTime() - startTime;
      incrementCounter("total_time", elapsedTime);

      log.debug("Created abstraction {} for infoset {} in {}ns", abstractionId, infosetKey,
          elapsedTime);

      return abstractionId;

    } catch (Exception e) {
      log.error("Error creating abstraction for infoset {}: {}", infosetKey, e.getMessage(), e);
      incrementCounter("errors");
      // Return a default abstraction to maintain system stability
      return getDefaultAbstraction(infosetKey);
    }
  }

  /**
   * Get abstraction ID for an existing information set
   */
  public Integer getAbstraction(long infosetKey) {
    return infosetToAbstraction.get(infosetKey);
  }

  /**
   * Get all information sets belonging to an abstraction
   */
  public Set<Long> getInfosetsForAbstraction(int abstractionId) {
    return abstractionToInfosets.getOrDefault(abstractionId, Collections.emptySet());
  }

  /**
   * Extract features from information set for abstraction clustering
   */
  private AbstractionFeatures extractFeatures(long infosetKey, GameContext context) {
    var builder = AbstractionFeatures.builder();

    // Extract hand strength features
    if (context.getHoleCards() != null && context.getCommunityCards() != null) {
      HandStrengthFeatures handFeatures = extractHandStrengthFeatures(
          context.getHoleCards(), context.getCommunityCards());
      builder.handStrength(handFeatures.getStrength())
          .handPotential(handFeatures.getPotential())
          .handCategory(handFeatures.getCategory());
    }

    // Extract betting history features
    if (context.getActionHistory() != null) {
      BettingHistoryFeatures bettingFeatures = extractBettingHistoryFeatures(
          context.getActionHistory());
      builder.bettingPattern(bettingFeatures.getPattern())
          .aggressionLevel(bettingFeatures.getAggressionLevel())
          .potOdds(bettingFeatures.getPotOdds());
    }

    // Extract positional features
    builder.position(context.getPosition())
        .bettingRound(context.getBettingRound())
        .numActivePlayers(context.getNumActivePlayers());

    return builder.build();
  }

  /**
   * Find existing similar abstraction or create new one
   */
  private int findOrCreateAbstraction(AbstractionFeatures features, long infosetKey) {
    // Look for similar existing abstractions
    for (Map.Entry<Integer, Set<Long>> entry : abstractionToInfosets.entrySet()) {
      int abstractionId = entry.getKey();

      // Get representative infoset from this abstraction
      Set<Long> infosets = entry.getValue();
      if (!infosets.isEmpty()) {
        Long representativeInfoset = infosets.iterator().next();

        // Calculate similarity (this would use cached features in production)
        double similarity = calculateSimilarity(features, representativeInfoset);

        if (similarity >= config.getSimilarityThreshold()) {
          log.debug("Found similar abstraction {} with similarity {}", abstractionId, similarity);
          return abstractionId;
        }
      }
    }

    // Create new abstraction
    int newAbstractionId = generateNewAbstractionId();
    totalAbstractions.incrementAndGet();

    log.debug("Created new abstraction {} for features: {}", newAbstractionId, features);
    return newAbstractionId;
  }

  /**
   * Calculate similarity between features and existing abstraction
   */
  private double calculateSimilarity(AbstractionFeatures features, Long representativeInfoset) {
    // Simplified similarity calculation - in production this would be more sophisticated
    // and use cached feature vectors for performance

    double similarity = 0.0;
    int featureCount = 0;

    // Hand strength similarity (weighted heavily)
    if (features.getHandStrength() != null) {
      // This would compare with cached features of representative infoset
      similarity += 0.4; // Placeholder - would be actual calculation
      featureCount++;
    }

    // Betting pattern similarity
    if (features.getBettingPattern() != null) {
      similarity += 0.3; // Placeholder - would be actual calculation
      featureCount++;
    }

    // Position similarity
    if (features.getPosition() != null) {
      similarity += 0.2; // Placeholder - would be actual calculation
      featureCount++;
    }

    // Betting round similarity
    if (features.getBettingRound() != null) {
      similarity += 0.1; // Placeholder - would be actual calculation
      featureCount++;
    }

    return featureCount > 0 ? similarity / featureCount : 0.0;
  }

  /**
   * Extract hand strength features for abstraction
   */
  private HandStrengthFeatures extractHandStrengthFeatures(List<Card> holeCards,
      List<Card> communityCards) {
    // This would integrate with existing HandEvaluator
    // Placeholder implementation
    return HandStrengthFeatures.builder()
        .strength(0.5) // Would be actual hand strength calculation
        .potential(0.3) // Would be actual potential calculation
        .category(HandCategory.PAIR) // Would be actual hand category
        .build();
  }

  /**
   * Extract betting history features for abstraction
   */
  private BettingHistoryFeatures extractBettingHistoryFeatures(List<ActionTrace> actionHistory) {
    // Analyze betting patterns, aggression, etc.
    // Placeholder implementation
    return BettingHistoryFeatures.builder()
        .pattern(BettingPattern.AGGRESSIVE)
        .aggressionLevel(0.6)
        .potOdds(0.25)
        .build();
  }

  /**
   * Generate new unique abstraction ID
   */
  private int generateNewAbstractionId() {
    return (int) totalAbstractions.get();
  }

  /**
   * Get default abstraction for error cases
   */
  private int getDefaultAbstraction(long infosetKey) {
    // Use hash-based default abstraction to maintain consistency
    return Math.abs((int) (infosetKey % 1000));
  }

  /**
   * Update abstraction quality metrics
   */
  public void updateQualityMetrics(int abstractionId, double strategicValue,
      double compressionRatio) {
    AbstractionQualityMetrics metrics = qualityMetrics.computeIfAbsent(abstractionId,
        k -> new AbstractionQualityMetrics());

    metrics.updateMetrics(strategicValue, compressionRatio);

    log.debug(
        "Updated quality metrics for abstraction {}: strategic value {}, compression ratio {}",
        abstractionId, strategicValue, compressionRatio);
  }

  /**
   * Get abstraction statistics
   */
  public AbstractionStatistics getStatistics() {
    return AbstractionStatistics.builder()
        .totalAbstractions(totalAbstractions.get())
        .totalInfosets(totalInfosets.get())
        .compressionRatio((double) totalInfosets.get() / Math.max(1, totalAbstractions.get()))
        .averageAbstractionSize(calculateAverageAbstractionSize())
        .performanceCounters(new HashMap<>(performanceCounters))
        .build();
  }

  /**
   * Calculate average abstraction size
   */
  private double calculateAverageAbstractionSize() {
    if (abstractionToInfosets.isEmpty()) {
      return 0.0;
    }

    double totalSize = abstractionToInfosets.values().stream()
        .mapToInt(Set::size)
        .sum();

    return totalSize / abstractionToInfosets.size();
  }

  /**
   * Increment performance counter
   */
  private void incrementCounter(String counterName) {
    incrementCounter(counterName, 1L);
  }

  /**
   * Increment performance counter by value
   */
  private void incrementCounter(String counterName, long value) {
    performanceCounters.merge(counterName, value, Long::sum);
  }

  /**
   * Validate abstraction quality and consistency
   */
  public ValidationResult validateAbstractions() {
    List<String> errors = new ArrayList<>();
    List<String> warnings = new ArrayList<>();
    boolean valid = true;

    try {
      // Check for consistency
      long mappedInfosets = infosetToAbstraction.size();
      long totalMappedInfosets = abstractionToInfosets.values().stream()
          .mapToLong(Set::size)
          .sum();

      if (mappedInfosets != totalMappedInfosets) {
        errors.add("Inconsistent mapping: " + mappedInfosets + " vs " + totalMappedInfosets);
      }

      // Check abstraction quality
      double avgCompressionRatio = calculateAverageAbstractionSize();
      if (avgCompressionRatio < config.getMinCompressionRatio()) {
        warnings.add("Low compression ratio: " + avgCompressionRatio);
      }

      // Check for empty abstractions
      long emptyAbstractions = abstractionToInfosets.values().stream()
          .mapToLong(set -> set.isEmpty() ? 1 : 0)
          .sum();

      if (emptyAbstractions > 0) {
        warnings.add("Found " + emptyAbstractions + " empty abstractions");
      }

      valid = errors.isEmpty();

    } catch (Exception e) {
      errors.add("Validation failed: " + e.getMessage());
      valid = false;
    }

    return ValidationResult.builder()
        .valid(valid)
        .errors(errors)
        .warnings(warnings)
        .build();
  }
}
