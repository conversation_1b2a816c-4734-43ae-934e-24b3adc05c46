package com.example.texasholdem.abstraction;

import com.example.texasholdem.model.*;
import com.example.texasholdem.service.HandEvaluator;
import com.example.texasholdem.strategy.model.ActionTrace;
import com.example.texasholdem.strategy.model.InfosetStore;
import com.example.texasholdem.strategy.SearchGameState;
import com.example.texasholdem.abstraction.AbstractionDataStructures.*;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Abstraction Manager for Pluribus Poker AI
 * <p>
 * Coordinates all three abstraction engines: - Information Abstraction: Reduces game tree
 * complexity - Action Abstraction: Simplifies betting action space - Card Abstraction: Groups
 * similar hands
 * <p>
 * Provides unified interface for abstraction operations and manages dynamic refinement, quality
 * monitoring, and performance optimization.
 */
@Slf4j
public class AbstractionManager {

  private final AbstractionConfig config;

  // Abstraction engines
  private final InformationAbstractionEngine informationEngine;
  private final ActionAbstractionEngine actionEngine;
  private final CardAbstractionEngine cardEngine;

  // Thread pools for parallel processing
  private final ExecutorService abstractionExecutor;
  private final ScheduledExecutorService refinementScheduler;

  // Integration with existing components
  private final InfosetStore infosetStore;
  private final HandEvaluator handEvaluator;

  // Performance monitoring
  private volatile boolean isRefinementEnabled;
  private volatile long lastRefinementTime;

  public AbstractionManager(AbstractionConfig config, InfosetStore infosetStore,
      HandEvaluator handEvaluator) {
    this.config = config != null ? config : AbstractionConfig.defaultConfig();
    this.infosetStore = infosetStore;
    this.handEvaluator = handEvaluator; // Can be null since HandEvaluator is now used as static methods

    // Validate configuration
    this.config.validate();

    // Initialize abstraction engines
    this.informationEngine = new InformationAbstractionEngine(infosetStore.getNumPlayers(),
        infosetStore.getNumActions(), config);
    this.actionEngine = new ActionAbstractionEngine(config);
    this.cardEngine = new CardAbstractionEngine(config, handEvaluator);

    // Initialize thread pools
    this.abstractionExecutor = config.isEnableParallelProcessing() ? Executors.newFixedThreadPool(
        config.getThreadPoolSize()) : null;
    this.refinementScheduler =
        config.isEnableDynamicRefinement() ? Executors.newScheduledThreadPool(2) : null;

    // Initialize refinement
    this.isRefinementEnabled = config.isEnableDynamicRefinement();
    this.lastRefinementTime = System.currentTimeMillis();

    if (isRefinementEnabled) {
      scheduleRefinement();
    }

    log.info("Abstraction Manager initialized with config: {}", config.getSummary());
  }

  /**
   * Create comprehensive abstraction for a game state
   */
  public ComprehensiveAbstraction createAbstraction(SearchGameState gameState) {
    long startTime = System.nanoTime();

    try {
      GameContext context = createGameContext(gameState);

      // Create abstractions using all three engines
      CompletableFuture<Integer> infoAbstraction = null;
      CompletableFuture<ActionTemplate> actionAbstraction = null;
      CompletableFuture<HandBucket> cardAbstraction = null;

      if (abstractionExecutor != null && config.isEnableParallelProcessing()) {
        // Parallel abstraction creation
        infoAbstraction = CompletableFuture.supplyAsync(
            () -> createInformationAbstraction(gameState, context), abstractionExecutor);

        actionAbstraction = CompletableFuture.supplyAsync(
            () -> createActionAbstraction(gameState, context), abstractionExecutor);

        cardAbstraction = CompletableFuture.supplyAsync(
            () -> createCardAbstraction(gameState, context), abstractionExecutor);

        // Wait for all abstractions to complete
        CompletableFuture.allOf(infoAbstraction, actionAbstraction, cardAbstraction).join();

        return ComprehensiveAbstraction.builder().informationAbstractionId(infoAbstraction.get())
            .actionTemplate(actionAbstraction.get()).handBucket(cardAbstraction.get())
            .gameContext(context).creationTime(System.nanoTime() - startTime).build();

      } else {
        // Sequential abstraction creation
        Integer infoId = createInformationAbstraction(gameState, context);
        ActionTemplate actionTemplate = createActionAbstraction(gameState, context);
        HandBucket handBucket = createCardAbstraction(gameState, context);

        return ComprehensiveAbstraction.builder().informationAbstractionId(infoId)
            .actionTemplate(actionTemplate).handBucket(handBucket).gameContext(context)
            .creationTime(System.nanoTime() - startTime).build();
      }

    } catch (Exception e) {
      log.error("Error creating comprehensive abstraction for game state: {}", e.getMessage(), e);
      return createDefaultAbstraction(gameState);
    }
  }

  /**
   * Create information abstraction for game state
   */
  private Integer createInformationAbstraction(SearchGameState gameState, GameContext context) {
    try {
      // Generate infoset key for current game state
      long infosetKey = generateInfosetKey(gameState);

      return informationEngine.createAbstraction(infosetKey, context);

    } catch (Exception e) {
      log.error("Error creating information abstraction: {}", e.getMessage(), e);
      return 0; // Default abstraction
    }
  }

  /**
   * Create action abstraction for game state
   */
  private ActionTemplate createActionAbstraction(SearchGameState gameState, GameContext context) {
    try {
      // Get current action from game state
      ActionInstance currentAction = getCurrentAction(gameState);
      if (currentAction == null) {
        return getDefaultActionTemplate();
      }

      return actionEngine.abstractAction(currentAction, gameState.getCurrentRound(),
          gameState.getPotSize());

    } catch (Exception e) {
      log.error("Error creating action abstraction: {}", e.getMessage(), e);
      return getDefaultActionTemplate();
    }
  }

  /**
   * Create card abstraction for game state
   */
  private HandBucket createCardAbstraction(SearchGameState gameState, GameContext context) {
    try {
      SearchGameState.PlayerSnapshot currentPlayer = gameState.getCurrentPlayer();
      if (currentPlayer == null) {
        return getDefaultHandBucket(gameState.getCurrentRound());
      }

      return cardEngine.abstractHand(currentPlayer.getHoleCards(), gameState.getCommunityCards(),
          gameState.getCurrentRound());

    } catch (Exception e) {
      log.error("Error creating card abstraction: {}", e.getMessage(), e);
      return getDefaultHandBucket(gameState.getCurrentRound());
    }
  }

  /**
   * Create game context from game state
   */
  private GameContext createGameContext(SearchGameState gameState) {
    SearchGameState.PlayerSnapshot currentPlayer = gameState.getCurrentPlayer();

    return GameContext.builder()
        .holeCards(currentPlayer != null ? currentPlayer.getHoleCards() : null)
        .communityCards(gameState.getCommunityCards()).actionHistory(gameState.getActionHistory())
        .position(currentPlayer != null ? currentPlayer.getPlayerIndex() : null)
        .bettingRound(gameState.getCurrentRound()).numActivePlayers(gameState.getNumActivePlayers())
        .potSize(gameState.getPotSize()).currentBet(gameState.getCurrentRoundBetAmount())
        .stackSize(currentPlayer != null ? currentPlayer.getChips() : null).build();
  }

  /**
   * Generate infoset key for game state
   */
  private long generateInfosetKey(SearchGameState gameState) {
    // Simplified infoset key generation
    // In production, this would use the existing infoset key generation logic

    long key = 0L;

    // Include betting round
    key = (key << 4) | gameState.getCurrentRound().ordinal();

    // Include position
    SearchGameState.PlayerSnapshot currentPlayer = gameState.getCurrentPlayer();
    if (currentPlayer != null) {
      key = (key << 4) | (currentPlayer.getPlayerIndex() & 0xF);
    }

    // Include pot size (normalized)
    int potBucket = Math.min(15, gameState.getPotSize() / 100);
    key = (key << 4) | potBucket;

    // Include action history hash
    if (gameState.getActionHistory() != null) {
      int actionHash = gameState.getActionHistory().hashCode() & 0xFFFF;
      key = (key << 16) | actionHash;
    }

    return key;
  }

  /**
   * Get current action from game state
   */
  private ActionInstance getCurrentAction(SearchGameState gameState) {
    if (gameState.getActionHistory() != null && !gameState.getActionHistory().isEmpty()) {
      ActionTrace lastTrace = gameState.getActionHistory()
          .get(gameState.getActionHistory().size() - 1);
      // Convert AbstractAction to ActionInstance
      return new ActionInstance(lastTrace.getActionInstance().getAction(),
          0); // Amount would need to be tracked separately
    }
    return null;
  }

  /**
   * Schedule dynamic refinement
   */
  private void scheduleRefinement() {
    if (refinementScheduler != null) {
      refinementScheduler.scheduleAtFixedRate(this::performRefinement,
          config.getRefinementInterval(), config.getRefinementInterval(), TimeUnit.MILLISECONDS);

      log.info("Scheduled dynamic refinement every {}ms", config.getRefinementInterval());
    }
  }

  /**
   * Perform dynamic refinement of abstractions
   */
  private void performRefinement() {
    if (!isRefinementEnabled) {
      return;
    }

    try {
      log.info("Starting dynamic abstraction refinement");
      long startTime = System.currentTimeMillis();

      // Refine action clusters based on usage patterns
      actionEngine.refineActionClusters();

      // Validate abstraction quality
      ValidationResult infoValidation = informationEngine.validateAbstractions();
      if (!infoValidation.isValid()) {
        log.warn("Information abstraction validation failed: {}", infoValidation.getSummary());
      }

      lastRefinementTime = System.currentTimeMillis();
      long elapsedTime = lastRefinementTime - startTime;

      log.info("Dynamic refinement completed in {}ms", elapsedTime);

    } catch (Exception e) {
      log.error("Error during dynamic refinement: {}", e.getMessage(), e);
    }
  }

  /**
   * Get comprehensive abstraction statistics
   */
  public AbstractionManagerStatistics getStatistics() {
    return AbstractionManagerStatistics.builder()
        .informationStats(informationEngine.getStatistics())
        .actionStats(actionEngine.getStatistics()).cardStats(cardEngine.getStatistics())
        .isRefinementEnabled(isRefinementEnabled).lastRefinementTime(lastRefinementTime)
        .config(config).build();
  }

  /**
   * Enable or disable dynamic refinement
   */
  public void setRefinementEnabled(boolean enabled) {
    this.isRefinementEnabled = enabled;
    log.info("Dynamic refinement {}", enabled ? "enabled" : "disabled");
  }

  /**
   * Get card abstraction engine
   */
  public CardAbstractionEngine getCardEngine() {
    return cardEngine;
  }

  /**
   * Get action abstraction engine
   */
  public ActionAbstractionEngine getActionEngine() {
    return actionEngine;
  }

  /**
   * Get information abstraction engine
   */
  public InformationAbstractionEngine getInformationEngine() {
    return informationEngine;
  }

  /**
   * Create default abstraction for error cases
   */
  private ComprehensiveAbstraction createDefaultAbstraction(SearchGameState gameState) {
    return ComprehensiveAbstraction.builder().informationAbstractionId(0)
        .actionTemplate(getDefaultActionTemplate())
        .handBucket(getDefaultHandBucket(gameState.getCurrentRound()))
        .gameContext(createGameContext(gameState)).creationTime(0L).build();
  }

  /**
   * Get default action template
   */
  private ActionTemplate getDefaultActionTemplate() {
    return ActionTemplate.builder().action(ActionType.FOLD).betSizeRatio(null).frequency(0.0)
        .description("Default Fold").build();
  }

  /**
   * Get default hand bucket
   */
  private HandBucket getDefaultHandBucket(BettingRound round) {
    return HandBucket.builder().bucketId(-1).bucketName("DEFAULT_" + round).minStrength(0.4)
        .maxStrength(0.6).minPotential(0.0).maxPotential(1.0).examples(new java.util.ArrayList<>())
        .sampleCount(0).build();
  }

  /**
   * Shutdown abstraction manager and cleanup resources
   */
  public void shutdown() {
    log.info("Shutting down Abstraction Manager");

    if (abstractionExecutor != null) {
      abstractionExecutor.shutdown();
      try {
        if (!abstractionExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
          abstractionExecutor.shutdownNow();
        }
      } catch (InterruptedException e) {
        abstractionExecutor.shutdownNow();
        Thread.currentThread().interrupt();
      }
    }

    if (refinementScheduler != null) {
      refinementScheduler.shutdown();
      try {
        if (!refinementScheduler.awaitTermination(5, TimeUnit.SECONDS)) {
          refinementScheduler.shutdownNow();
        }
      } catch (InterruptedException e) {
        refinementScheduler.shutdownNow();
        Thread.currentThread().interrupt();
      }
    }

    log.info("Abstraction Manager shutdown completed");
  }
}

/**
 * Comprehensive abstraction result
 */
@Data
@Builder
class ComprehensiveAbstraction {

  private Integer informationAbstractionId;
  private ActionTemplate actionTemplate;
  private HandBucket handBucket;
  private GameContext gameContext;
  private Long creationTime; // Nanoseconds

  public String getSummary() {
    return String.format("Abstraction{info=%d, action=%s, hand=%s, time=%dns}",
        informationAbstractionId, actionTemplate != null ? actionTemplate.getAction() : "null",
        handBucket != null ? handBucket.getBucketName() : "null", creationTime);
  }
}

/**
 * Abstraction manager statistics
 */
@Data
@Builder
class AbstractionManagerStatistics {

  private AbstractionStatistics informationStats;
  private ActionAbstractionStatistics actionStats;
  private CardAbstractionStatistics cardStats;
  private boolean isRefinementEnabled;
  private long lastRefinementTime;
  private AbstractionConfig config;

  public String getSummary() {
    return String.format("AbstractionManager{info: %s, action: %s, card: %s, refinement: %s}",
        informationStats != null ? informationStats.getSummary() : "null",
        actionStats != null ? actionStats.getSummary() : "null",
        cardStats != null ? cardStats.getSummary() : "null",
        isRefinementEnabled ? "enabled" : "disabled");
  }
}
