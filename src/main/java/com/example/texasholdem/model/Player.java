package com.example.texasholdem.model;

import com.example.texasholdem.strategy.StrategyManager;
import com.example.texasholdem.strategy.model.ActionTrace;
import com.example.texasholdem.strategy.RandomStrategy;
import lombok.Getter;
import lombok.Setter;
import com.example.texasholdem.strategy.Strategy;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class Player {

  private final String name;
  private final List<Card> hand = new ArrayList<>();
  private int chips;
  private int currentBet = 0;
  private boolean folded = false;
  private boolean allIn = false;
  private final Strategy strategy;
  private ActionInstance lastAction = new ActionInstance(Action.CHECK, 0);
  private int playerIndex;


  public Player(String name, int playerIndex, int initialChips, Strategy strategy) {
    this.name = name;
    this.chips = initialChips;
    this.playerIndex = playerIndex;
    this.strategy = strategy;
  }

  public Player(String name, int playerIndex, int initialChips) {
    this(name, playerIndex, initialChips, new RandomStrategy());
  }

  public Player(String name, int initialChips, Strategy strategy) {
    this.name = name;
    this.chips = initialChips;
    this.strategy = strategy;
  }

  public void addCardToHand(Card card) {
    hand.add(card);
  }

  public void clearHand() {
    hand.clear();
  }

  public int getHandSize() {
    return hand.size();
  }

  public void placeBet(int amount) {
    if (amount > chips) {
      throw new IllegalArgumentException("Not enough chips to place the bet.");
    }
    chips -= amount;
    currentBet += amount;
    if (chips == 0) {
      allIn = true;
    }
  }

  public void resetBet() {
    currentBet = 0;
  }

  public void resetPlayerState() {
    currentBet = 0;
    allIn = false;
    folded = false;
    clearHand();
  }

  public void resetPlayerStateForCFR() {
    currentBet = 0;
    allIn = false;
    folded = false;
    chips = 10000;
    clearHand();
  }

  public void resetPlayerStateForRecovery(){
    currentBet = 0;
    allIn = false;
    folded = false;
    chips = 10000;
  }


  public void fold() {
    folded = true;
  }

  public ActionInstance decideAction(
      List<Card> communityCards,
      BettingRound currentRound,
      int currentRoundBetAmount,
      List<ActionTrace> actionHistory,
      int potSize
  ) {
    ActionInstance ai = strategy.decideAction(
        hand, communityCards, currentRound, this,
        currentRoundBetAmount, actionHistory, potSize
    );
    this.lastAction = ai;
    return ai;
  }

  public int getTotalContribution() {
    return currentBet;
  }

  public int collectBet(int amount) {
    int collectedAmount = Math.min(amount, currentBet);
    currentBet -= collectedAmount;
    return collectedAmount;
  }

  public boolean isActive() {
    return !folded && !allIn;
  }
}
