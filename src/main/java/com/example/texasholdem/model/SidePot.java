package com.example.texasholdem.model;

import java.io.Serializable;
import java.util.Collection;
import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
public class SidePot implements Serializable{
    private int amount;
    private Set<Player> eligiblePlayers;

    public SidePot() {
        this.amount = 0;
        this.eligiblePlayers = new HashSet<>();
    }

    public void addAmount(int amount) {
        this.amount += amount;
    }

    public void setEligiblePlayers(Set<Player> players) {
        eligiblePlayers = players;
    }
} 